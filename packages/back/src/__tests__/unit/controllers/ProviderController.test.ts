jest.mock('../../../core/services/db/sequelize', () => ({
  query: jest.fn(),
  define: jest.fn(),
  QueryTypes: { SELECT: 'SELECT' },
}))
jest.mock('../../../core/resources/serviceResource', () => ({
  __esModule: true,
  default: {},
}))
jest.mock('../../../microServices/workers/jobs/whatsappBusiness/driver/gupshupFunctions', () => ({
  __esModule: true,
  default: {},
}))

jest.mock('../../../microServices/workers/jobs/whatsappBusiness/driver/baseFunctions', () => ({
  __esModule: true,
  dialogGetWabaAccount: jest.fn(),
}))

import { ProviderController } from '../../../microServices/api/controllers/provider/ProviderController'
import { Request, Response } from 'express'
import * as baseFunctions from '../../../microServices/workers/jobs/whatsappBusiness/driver/baseFunctions'
import BadRequestHttpError from '../../../core/utils/error/BadRequestHttpError'

describe('ProviderController', () => {
  let logger: any
  let resource: any
  let controller: ProviderController
  let req: Partial<Request>
  let res: Partial<Response>
  let sendMock: jest.Mock
  let statusMock: jest.Mock
  let jsonMock: jest.Mock
  describe('mmliteEnabled', () => {
    beforeEach(() => {
      logger = { log: jest.fn() }
      resource = {
        findById: jest.fn(),
        update: jest.fn(),
      }
      controller = new ProviderController(logger, resource as any)
      sendMock = jest.fn()
      jsonMock = jest.fn()
      statusMock = jest.fn(() => ({ json: jsonMock }))
      req = { params: { serviceId: '123' } }
      res = { send: sendMock, status: statusMock } as any
    })

    it('should return mmliteEnabled ONBOARDED if service is already onboarded', async () => {
      resource.findById.mockResolvedValue({
        internalData: { mmlite: 'ONBOARDED' },
        data: { providerType: 'gupshup' },
      })
      await controller.mmliteEnabled(req as Request, res as Response)
      expect(sendMock).toHaveBeenCalledWith({ mmliteEnabled: 'ONBOARDED' })
    })

    it('should throw BadRequestHttpError if service is not found', async () => {
      resource.findById.mockResolvedValue(null)
      await controller.mmliteEnabled(req as Request, res as Response)
      expect(statusMock).toHaveBeenCalledWith(400)
      expect(jsonMock).toHaveBeenCalledWith({ message: expect.stringContaining('Service ID: 123 is required') })
    })

    it('should throw BadRequestHttpError if providerType is not supported', async () => {
      resource.findById.mockResolvedValue({
        internalData: {},
        data: { providerType: 'unknown' },
      })
      await controller.mmliteEnabled(req as Request, res as Response)
      expect(statusMock).toHaveBeenCalledWith(400)
      expect(jsonMock).toHaveBeenCalledWith({
        message: expect.stringContaining('Provider type unknown is not supported'),
      })
    })

    it('should call provider function and update resource, then send status', async () => {
      const service = {
        internalData: {},
        data: { providerType: 'gupshup' },
      }
      const providerfnc = jest.fn().mockResolvedValue({ wabaInfo: { mmLiteStatus: 'ENABLED' } })
      controller['providerType'].gupshup = providerfnc
      resource.findById.mockResolvedValue(service)
      resource.update.mockResolvedValue({})
      await controller.mmliteEnabled(req as Request, res as Response)
      expect(providerfnc).toHaveBeenCalledWith(service)
      expect(resource.update).toHaveBeenCalledWith(service, { internalData: { mmlite: 'ENABLED' } })
      expect(sendMock).toHaveBeenCalledWith({ mmliteEnabled: 'ENABLED' })
    })

    it('should handle unexpected errors and return 500', async () => {
      resource.findById.mockRejectedValue(new Error('Unexpected'))
      await controller.mmliteEnabled(req as Request, res as Response)
      expect(statusMock).toHaveBeenCalledWith(500)
      expect(jsonMock).toHaveBeenCalledWith({ message: 'Unexpected' })
      expect(logger.log).toHaveBeenCalledWith(expect.stringContaining('Error enabling flag'), 'error', [
        expect.any(Error),
      ])
    })
  })

  describe('dialogSetWabaAccount', () => {
    beforeEach(() => {
      sendMock = jest.fn()
      jsonMock = jest.fn()
      req = { params: { serviceId: '456' } } as Partial<Request>
      res = { send: sendMock, status: statusMock } as Partial<Response>
      res = { send: sendMock, status: statusMock } as any
    })

    it('should return success if waba_account already exists', async () => {
      resource.findById.mockResolvedValue({
        internalData: { waba_account: 'acc123' },
        data: { providerType: '360Dialog' },
      })
      await controller.dialogSetWabaAccount(req as Request, res as Response)
      expect(sendMock).toHaveBeenCalledWith({ success: true })
    })

    it('should throw BadRequestHttpError if service is not found', async () => {
      resource.findById.mockResolvedValue(null)
      await controller.dialogSetWabaAccount(req as Request, res as Response)
      expect(statusMock).toHaveBeenCalledWith(400)
      expect(jsonMock).toHaveBeenCalledWith({
        success: false,
        message: expect.stringContaining('Service ID: 456 is required'),
      })
    })

    it('should throw BadRequestHttpError if providerType is not 360Dialog', async () => {
      resource.findById.mockResolvedValue({
        internalData: {},
        data: { providerType: 'gupshup' },
      })
      await controller.dialogSetWabaAccount(req as Request, res as Response)
      expect(statusMock).toHaveBeenCalledWith(400)
      expect(jsonMock).toHaveBeenCalledWith({
        success: false,
        message: expect.stringContaining('The provider is not a 360Dialog type'),
      })
    })

    it('should fetch waba_account, update resource, and return success', async () => {
      const service = {
        internalData: {},
        data: { providerType: '360Dialog' },
      }
      const wabaAccount = 'newWabaAccount'
      ;(baseFunctions.dialogGetWabaAccount as jest.Mock).mockResolvedValue(wabaAccount)
      resource.findById.mockResolvedValue(service)
      resource.update.mockResolvedValue({})
      await controller.dialogSetWabaAccount(req as Request, res as Response)
      expect(resource.update).toHaveBeenCalledWith(service, { internalData: { waba_account: wabaAccount } })
      expect(sendMock).toHaveBeenCalledWith({ success: true })
    })

    it('should handle unexpected errors and return 500', async () => {
      resource.findById.mockRejectedValue(new Error('Unexpected'))
      await controller.dialogSetWabaAccount(req as Request, res as Response)
      expect(statusMock).toHaveBeenCalledWith(500)
      expect(jsonMock).toHaveBeenCalledWith({ success: false, message: 'Unexpected' })
      expect(logger.log).toHaveBeenCalledWith(expect.stringContaining('Error enabling flag'), 'error', [
        expect.any(Error),
      ])
    })

    it('should handle BadRequestHttpError and return 400', async () => {
      resource.findById.mockImplementation(() => {
        throw new BadRequestHttpError('bad request')
      })
      await controller.dialogSetWabaAccount(req as Request, res as Response)
      expect(statusMock).toHaveBeenCalledWith(400)
      expect(jsonMock).toHaveBeenCalledWith({ success: false, message: 'bad request' })
    })
  })
})
