import MessagesController from '../../../microServices/api/controllers/MessagesController'
import messageResource from '../../../core/resources/messageResource'
import userResource from '../../../core/resources/userResource'
import contactResource from '../../../core/resources/contactResource'
import driverService from '../../../core/services/driverService'
import { Request, Response } from 'express'
import HttpError from '../../../core/utils/error/HttpError'
import BadRequestHttpError from '../../../core/utils/error/BadRequestHttpError'
import { differenceInMinutes } from 'date-fns'
import { ContactInstance } from '../../../core/dbSequelize/models/Contact'
import { MessageInstance } from '../../../core/dbSequelize/models/Message'

jest.mock('../../../core/resources/messageResource', () => ({
  create: jest.fn(),
}))

jest.mock('../../../core/resources/contactResource', () => ({
  findById: jest.fn(),
  findMany: jest.fn(),
}))

jest.mock('../../../core/resources/userResource', () => ({
  findById: jest.fn(),
}))

jest.mock('../../../core/resources/fileResource', () => jest.fn())
jest.mock('../../../core/resources/serviceResource', () => ({
  findById: jest.fn(),
}))

jest.mock('../../../core/services/driverService', () => ({
  getServiceRpc: jest.fn(),
}))

jest.mock('../../../microServices/api/controllers/BaseResourceController', () => jest.fn())
jest.mock('../../../core/dbSequelize/models/Message', () => jest.fn())
jest.mock('../../../core/transformers/messageTransformer', () => jest.fn())
jest.mock('../../../core/services/logs/reportError', () => jest.fn())
jest.mock('../../../core/services/logs/Logger', () => jest.fn())
jest.mock('typedi', () => {
  return {
    get: jest.fn().mockReturnValue({
      log: jest.fn(),
    }),
  }
})
jest.mock('../../../core/services/jobs/http/HttpJobsDispatcher', () => jest.fn())
jest.mock('../../../core/services/queue/redisTaskQueue', () => jest.fn())
jest.mock('date-fns', () => ({
  differenceInMinutes: jest.fn(),
}))

describe('MessagesController', () => {
  let messagesController: MessagesController
  let req: Partial<Request>
  let res: Partial<Response>
  let mockDifferenceInMinutes: jest.Mock

  beforeEach(() => {
    jest.clearAllMocks()
    messagesController = new MessagesController()

    mockDifferenceInMinutes = (differenceInMinutes as jest.Mock).mockReturnValue(14)

    req = {
      params: { id: 'message-id' },
      body: { text: 'new text', contactId: 'contact-id' },
    }

    res = {
      locals: {
        user: { id: 'user-id', permissions: [] },
      },
    }
  })

  describe('sendVcards', () => {
    it('should throw BadRequestHttpError when serviceId is missing', async () => {
      const req = {
        body: {
          contactId: 'someContactId',
          contactsIds: ['contactId1', 'contactId2'],
        },
      } as Request
      const res = {
        locals: {
          accountId: 'someAccountId',
          user: { id: 'someUserId' },
        },
        status: jest.fn().mockReturnThis(),
        json: jest.fn(),
      } as unknown as Response

      await expect(messagesController.sendVcards(req, res)).rejects.toThrow(
        new BadRequestHttpError('ServiceId is required and must be a string.'),
      )
    })

    it('should throw BadRequestHttpError when contactID is missing', async () => {
      const req = {
        body: {
          contactsIds: ['contactId1', 'contactId2'],
          serviceId: 'someServiceId',
        },
      } as Request
      const res = {
        locals: {
          accountId: 'someAccountId',
          user: { id: 'someUserId' },
        },
        status: jest.fn().mockReturnThis(),
        json: jest.fn(),
      } as unknown as Response

      await expect(messagesController.sendVcards(req, res)).rejects.toThrow(
        new BadRequestHttpError('ContactId is required and must be a string.'),
      )
    })

    it('should throw BadRequestHttpError when contactsIds is missing', async () => {
      const req = {
        body: {
          contactId: 'someContactId',
          serviceId: 'someServiceId',
        },
      } as Request
      const res = {
        locals: {
          accountId: 'someAccountId',
          user: { id: 'someUserId' },
        },
        status: jest.fn().mockReturnThis(),
        json: jest.fn(),
      } as unknown as Response

      await expect(messagesController.sendVcards(req, res)).rejects.toThrow(
        new BadRequestHttpError('contactsIds is required and must be an array.'),
      )
    })

    it('should throw BadRequestHttpError when contactsIds is not an array', async () => {
      const req = {
        body: {
          contactId: 'someContactId',
          contactsIds: 'contactId1',
          serviceId: 'someServiceId',
        },
      } as Request
      const res = {
        locals: {
          accountId: 'someAccountId',
          user: { id: 'someUserId' },
        },
        status: jest.fn().mockReturnThis(),
        json: jest.fn(),
      } as unknown as Response

      await expect(messagesController.sendVcards(req, res)).rejects.toThrow(
        new BadRequestHttpError('contactsIds is required and must be an array.'),
      )
    })

    it('should throw BadRequestHttpError when contactId is not found', async () => {
      const req = {
        body: {
          contactId: 'someContactId',
          contactsIds: ['contactId1', 'contactId2'],
          serviceId: 'someServiceId',
        },
      } as Request
      const res = {
        locals: {
          accountId: 'someAccountId',
          user: { id: 'someUserId' },
        },
        status: jest.fn().mockReturnThis(),
        json: jest.fn(),
      } as unknown as Response

      jest.spyOn(contactResource, 'findById').mockResolvedValue(null)

      await expect(messagesController.sendVcards(req, res)).rejects.toThrow(
        new BadRequestHttpError('ContactId not found.'),
      )
    })

    it('should throw BadRequestHttpError when contactsIds are not found', async () => {
      const req = {
        body: {
          contactId: 'someContactId',
          contactsIds: ['contactId1', 'contactId2'],
          serviceId: 'someServiceId',
        },
      } as Request
      const res = {
        locals: {
          accountId: 'someAccountId',
          user: { id: 'someUserId' },
        },
        status: jest.fn().mockReturnThis(),
        json: jest.fn(),
      } as unknown as Response

      jest.spyOn(contactResource, 'findById').mockResolvedValue({ id: 'someContactId' } as ContactInstance)
      jest.spyOn(contactResource, 'findMany').mockResolvedValue([])

      await expect(messagesController.sendVcards(req, res)).rejects.toThrow(
        new BadRequestHttpError('ContactsIds not found.'),
      )
    })

    it('should throw BadRequestHttpError when error sending contacts', async () => {
      const req = {
        body: {
          contactId: 'someContactId',
          contactsIds: ['contactId1', 'contactId2'],
          serviceId: 'someServiceId',
        },
      } as Request
      const res = {
        locals: {
          accountId: 'someAccountId',
          user: { id: 'someUserId' },
        },
        status: jest.fn().mockReturnThis(),
        json: jest.fn(),
      } as unknown as Response

      jest.spyOn(contactResource, 'findById').mockResolvedValue({ id: 'someContactId' } as ContactInstance)
      jest
        .spyOn(contactResource, 'findMany')
        .mockResolvedValue([{ id: 'contactId1' }, { id: 'contactId2' }] as ContactInstance[])
      jest.spyOn(driverService, 'getServiceRpc').mockResolvedValueOnce({
        wplvSendVCardContactMessage: jest.fn().mockResolvedValue(null),
      })

      await expect(messagesController.sendVcards(req, res)).rejects.toThrow(
        new BadRequestHttpError('Error sending contacts.'),
      )
    })

    it('should successfully send vcards', async () => {
      const req = {
        body: {
          contactId: 'someContactId',
          contactsIds: ['contactId1', 'contactId2'],
          serviceId: 'someServiceId',
        },
      } as Request
      const res = {
        locals: {
          accountId: 'someAccountId',
          user: { id: 'someUserId' },
        },
      } as unknown as Response

      jest
        .spyOn(contactResource, 'findById')
        .mockResolvedValue({ id: 'someContactId', idFromService: 'serviceContactId' } as ContactInstance)
      jest.spyOn(contactResource, 'findMany').mockResolvedValue([
        { id: 'contactId1', idFromService: 'serviceContactId1' },
        { id: 'contactId2', idFromService: 'serviceContactId2' },
      ] as ContactInstance[])
      jest.spyOn(driverService, 'getServiceRpc').mockResolvedValueOnce({
        wplvSendVCardContactMessage: jest.fn().mockResolvedValue({
          id: 'messageId',
          type: 'vcard',
          vCardString: 'vCardString',
          vcardList: [],
        }),
      })
      jest.spyOn(messageResource, 'create').mockResolvedValue({ id: 'messageId' } as MessageInstance)

      const result = await messagesController.sendVcards(req, res)

      expect(messageResource.create).toHaveBeenCalledWith(
        expect.objectContaining({
          type: 'vcard',
          serviceId: 'someServiceId',
        }),
      )

      expect(result).toEqual({ id: 'messageId' })
    })
  })

  describe('Edit Message', () => {
    it('should return 404 if user is not found', async () => {
      userResource.findById = jest.fn().mockResolvedValue(null)

      await expect(messagesController.editMessage(req as Request, res as Response)).rejects.toThrow(
        new HttpError(404, 'User not found'),
      )
    })

    it('should return 404 if contact is not found', async () => {
      userResource.findById = jest.fn().mockResolvedValue({ id: 'userId' })
      contactResource.findById = jest.fn().mockResolvedValue(null)

      await expect(messagesController.editMessage(req as Request, res as Response)).rejects.toThrow(
        new HttpError(404, 'Contact not found'),
      )
    })

    it('should return 404 if message is not found', async () => {
      userResource.findById = jest.fn().mockResolvedValue({ id: 'userId' })
      contactResource.findById = jest.fn().mockResolvedValue({ id: 'contactId' })
      messageResource.findById = jest.fn().mockResolvedValue(null)

      await expect(messagesController.editMessage(req as Request, res as Response)).rejects.toThrow(
        new HttpError(404, 'Message not found'),
      )
    })

    it('should successfully edit the message', async () => {
      const message = { isFromMe: true, idFromService: 'idFromService', timestamp: new Date() }
      const contact = { serviceId: 'contactServiceId' }
      userResource.findById = jest.fn().mockResolvedValue({ id: 'userId' })
      contactResource.findById = jest.fn().mockResolvedValue(contact)
      messageResource.findById = jest.fn().mockResolvedValue(message)
      driverService.editMessage = jest.fn().mockResolvedValueOnce(true)
      await messagesController.editMessage(req as Request, res as Response)
      expect(driverService.editMessage).toHaveBeenCalledWith(contact.serviceId, message.idFromService, 'new text', {})
    })

    it('should return 400 if message text is empty', async () => {
      req.body.text = ''
      await expect(messagesController.editMessage(req as Request, res as Response)).rejects.toThrow(
        new HttpError(400, 'Text cannot is be empty'),
      )
    })

    it('should return 400 if message text contains only whitespace', async () => {
      req.body.text = '   '
      await expect(messagesController.editMessage(req as Request, res as Response)).rejects.toThrow(
        new HttpError(400, 'Text cannot is be empty'),
      )
    })

    it('should return 403 if message is not from the user', async () => {
      const message = { isFromMe: false }
      userResource.findById = jest.fn().mockResolvedValue({ id: 'userId' })
      contactResource.findById = jest.fn().mockResolvedValue({ id: 'contactId' })
      messageResource.findById = jest.fn().mockResolvedValue(message)

      await expect(messagesController.editMessage(req as Request, res as Response)).rejects.toThrow(
        new HttpError(403, 'Cannot edit message'),
      )
    })

    it('should return 403 if editing time exceeds 15 minutes', async () => {
      const message = { isFromMe: true, timestamp: new Date() }
      userResource.findById = jest.fn().mockResolvedValue({ id: 'userId' })
      contactResource.findById = jest.fn().mockResolvedValue({ id: 'contactId' })
      messageResource.findById = jest.fn().mockResolvedValue(message)
      mockDifferenceInMinutes.mockReturnValueOnce(18)

      await expect(messagesController.editMessage(req as Request, res as Response)).rejects.toThrow(
        new HttpError(403, 'Cannot edit message'),
      )
    })

    it('should throw 500 if driverService fails to edit message', async () => {
      const message = { isFromMe: true, idFromService: 'idFromService', timestamp: new Date() }
      const contact = { serviceId: 'contactServiceId' }
      userResource.findById = jest.fn().mockResolvedValue({ id: 'userId' })
      contactResource.findById = jest.fn().mockResolvedValue(contact)
      messageResource.findById = jest.fn().mockResolvedValue(message)
      driverService.editMessage = jest.fn().mockRejectedValue(new Error('Driver service error'))

      await expect(messagesController.editMessage(req as Request, res as Response)).rejects.toThrow(
        new HttpError(500, 'Internal Server Error'),
      )
    })

    it('should return 400 if mentionedList format is wrong', async () => {
      const newReqBody = { ...req }
      newReqBody.body.mentionedList = 'wrongFormat'

      const message = { isFromMe: true, idFromService: 'idFromService', timestamp: new Date() }
      const contact = { serviceId: 'contactServiceId' }
      userResource.findById = jest.fn().mockResolvedValue({ id: 'userId' })
      contactResource.findById = jest.fn().mockResolvedValue(contact)
      messageResource.findById = jest.fn().mockResolvedValue(message)
      await expect(messagesController.editMessage(newReqBody as Request, res as Response)).rejects.toThrow(
        new HttpError(400, 'Invalid mentionedList format, must be array'),
      )
    })

    it('should return 400 if mentionedList format is wrong', async () => {
      const newReqBody = { ...req }
      newReqBody.body.mentionedList = ['wrongFormat']

      const message = { isFromMe: true, idFromService: 'idFromService', timestamp: new Date() }
      const contact = { serviceId: 'contactServiceId' }
      userResource.findById = jest.fn().mockResolvedValue({ id: 'userId' })
      contactResource.findById = jest.fn().mockResolvedValue(contact)
      messageResource.findById = jest.fn().mockResolvedValue(message)
      driverService.editMessage = jest.fn().mockResolvedValueOnce(true)
      await expect(messagesController.editMessage(newReqBody as Request, res as Response)).rejects.toThrow(
        new HttpError(400, 'Invalid mentionedList format, must be array of contacts idFromService'),
      )
    })

    it('should edit message with mentionedList with success', async () => {
      const mentionedList: Array<string> = ['<EMAIL>', '<EMAIL>']
      const newReqBody = { ...req }
      newReqBody.body.mentionedList = mentionedList

      const message = { isFromMe: true, idFromService: 'idFromService', timestamp: new Date() }
      const contact = { serviceId: 'contactServiceId' }
      userResource.findById = jest.fn().mockResolvedValue({ id: 'userId' })
      contactResource.findById = jest.fn().mockResolvedValue(contact)
      messageResource.findById = jest.fn().mockResolvedValue(message)
      driverService.editMessage = jest.fn().mockResolvedValueOnce(true)
      await messagesController.editMessage(newReqBody as Request, res as Response)
      expect(driverService.editMessage).toHaveBeenCalledWith(contact.serviceId, message.idFromService, 'new text', {
        mentionedList,
      })
    })
  })
})
