import BaseResource, { EventTransaction, CREATED, UPDATED, DESTROYED } from '../../../../core/resources/BaseResource'
import optimizeEvents from '../../../../core/resources/utils/optimizeEvents'
import { EventEmitter } from 'events'

jest.mock('../../../../core/resources/utils/optimizeEvents', () => jest.fn((events) => events))
jest.mock('events', () => {
  return {
    EventEmitter: jest.fn().mockImplementation(() => ({
      emit: jest.fn(),
      setMaxListeners: jest.fn(),
    })),
  }
})

describe('BaseResource', () => {
  let resource: BaseResource<any>
  let mockRepo: any

  beforeEach(() => {
    jest.clearAllMocks()

    mockRepo = {
      transaction: jest.fn((fn) => fn({})),
      nestedTransaction: jest.fn((fn) => fn({})),
      create: jest.fn(() => Promise.resolve({ id: 1 })),
      update: jest.fn((model, data) => Promise.resolve({ ...model, ...data })),
      destroy: jest.fn(() => Promise.resolve()),
      findById: jest.fn((id) => Promise.resolve({ id })),
      build: jest.fn(),
      findManyPaginated: jest.fn(() =>
        Promise.resolve({
          data: [],
          total: 0,
          limit: 0,
          skip: 0,
          currentPage: 0,
          lastPage: 0,
          from: 0,
          to: 0,
        }),
      ),
    }

    resource = new BaseResource(mockRepo as any)
  })

  it('initializes getters and events', () => {
    expect(resource.getRepository()).toBe(mockRepo)
    expect(resource.getEvents()).toEqual([CREATED, UPDATED, DESTROYED])
    expect(resource.getEmitter()).toBeInstanceOf(mockRepo.constructor)
  })

  it('emit returns null for falsy or empty data', () => {
    expect(resource.emit('someEvent', null)).toBeNull()
    expect(resource.emit('someEvent', [])).toBeNull()
  })

  it('emit uses eventTransaction.pushEmit when provided', () => {
    const pushEmit = jest.fn()
    const et: EventTransaction = { events: [], pushEmit }
    const payload = { id: 42 }

    resource.emit('evt', payload, et)

    expect(pushEmit).toHaveBeenCalledWith(resource.getEmitter(), 'evt', payload)
  })

  it('eventTransaction collects events, calls optimizeEvents and emits via provided emitters', async () => {
    const mockEmitter = new EventEmitter()
    mockEmitter.emit = jest.fn()

    const fn = jest.fn(async (transaction: EventTransaction) => {
      transaction.pushEmit(mockEmitter as any, 'testEvent', { id: 99 })
    })

    await resource.eventTransaction(fn)

    expect(fn).toHaveBeenCalledTimes(1)
    expect(optimizeEvents).toHaveBeenCalledWith([[mockEmitter, 'testEvent', { id: 99 }]])
    expect(mockEmitter.emit).toHaveBeenCalledWith('testEvent', { id: 99 })
  })

  it('maybeEventTransaction uses passed transaction when provided', async () => {
    const passed: EventTransaction = { events: [], pushEmit: jest.fn() }
    const spyFn = jest.fn(async (t: EventTransaction) => 'ok')

    const res = await resource.maybeEventTransaction(spyFn, passed)

    expect(res).toBe('ok')
    expect(spyFn).toHaveBeenCalledWith(passed)
    // optimizeEvents should not have been invoked because we did not create a new transaction
    expect(optimizeEvents).not.toHaveBeenCalled()
  })

  it('create delegates to repository and emits CREATED when dontEmit is false', async () => {
    const model = { id: 2, name: 'x' }
    mockRepo.create.mockResolvedValueOnce(model)

    const emitSpy = jest.spyOn(resource.getEmitter(), 'emit')

    const res = await resource.create({ name: 'x' })

    expect(mockRepo.create).toHaveBeenCalledWith({ name: 'x' }, {})
    expect(res).toBe(model)
    expect(emitSpy).toHaveBeenCalledWith(CREATED, model)
  })

  it('create does not emit when dontEmit is true', async () => {
    const model = { id: 3 }
    mockRepo.create.mockResolvedValueOnce(model)

    const emitSpy = jest.spyOn(resource.getEmitter(), 'emit').mockClear()

    const res = await resource.create({ any: true }, { dontEmit: true })

    expect(res).toBe(model)
    expect(emitSpy).not.toHaveBeenCalled()
  })

  it('update delegates to repository and emits UPDATED', async () => {
    const original = { id: 5, a: 1 }
    const updated = { id: 5, a: 2 }
    mockRepo.update.mockResolvedValueOnce(updated)

    const emitSpy = jest.spyOn(resource.getEmitter(), 'emit')

    const res = await resource.update(original, { a: 2 })

    expect(mockRepo.update).toHaveBeenCalledWith(original, { a: 2 }, {})
    expect(res).toEqual(updated)
    expect(emitSpy).toHaveBeenCalledWith(UPDATED, updated)
  })

  it('destroy delegates to repository and emits DESTROYED', async () => {
    const model = { id: 7 }
    mockRepo.destroy.mockResolvedValueOnce(undefined)

    const emitSpy = jest.spyOn(resource.getEmitter(), 'emit')

    const res = await resource.destroy(model)

    expect(mockRepo.destroy).toHaveBeenCalledWith(model, {})
    expect(res).toBe(model)
    expect(emitSpy).toHaveBeenCalledWith(DESTROYED, model)
  })

  it('createMany calls internal create for each item and emits CREATED with array', async () => {
    // Spy internal create to simulate per-item creation (createMany calls this.create with dontEmit: true)
    const createdItems = [{ value: 1 }, { value: 2 }]
    const createSpy = jest
      .spyOn(resource as any, 'create')
      .mockImplementation((item: any, opts: any) => Promise.resolve({ ...item }))

    const emitSpy = jest.spyOn(resource.getEmitter(), 'emit')

    const res = await resource.createMany(createdItems)

    expect(createSpy).toHaveBeenCalledTimes(createdItems.length)
    // After internal creates, createMany should emit once with the array of created models
    expect(emitSpy).toHaveBeenCalledWith(CREATED, expect.arrayContaining(createdItems))
    expect(res).toEqual(expect.arrayContaining(createdItems))
  })
})
describe('BaseResource on', () => {
  let resource: BaseResource<any>
  let mockRepo: any

  beforeEach(() => {
    jest.clearAllMocks()

    mockRepo = {
      transaction: jest.fn((fn) => fn({})),
      nestedTransaction: jest.fn((fn) => fn({})),
      create: jest.fn(() => Promise.resolve({ id: 1 })),
      update: jest.fn((model, data) => Promise.resolve({ ...model, ...data })),
      destroy: jest.fn(() => Promise.resolve()),
      findById: jest.fn((id) => Promise.resolve({ id })),
      build: jest.fn(),
      findManyPaginated: jest.fn(() =>
        Promise.resolve({
          data: [],
          total: 0,
          limit: 0,
          skip: 0,
          currentPage: 0,
          lastPage: 0,
          from: 0,
          to: 0,
        }),
      ),
    }

    resource = new BaseResource(mockRepo as any)
  })

  it('delegates to emitter.on and returns its value', () => {
    const onSpy = jest.fn().mockReturnValue('registered')
    ;(resource.getEmitter() as any).on = onSpy

    const listener = jest.fn()
    const res = resource.on('myEvent', listener)

    expect(onSpy).toHaveBeenCalledWith('myEvent', listener)
    expect(res).toBe('registered')
  })

  it('does not call emitter.on when emitter.emit is missing and returns undefined', () => {
    const onSpy = jest.fn()
    ;(resource.getEmitter() as any).on = onSpy
    ;(resource.getEmitter() as any).emit = undefined

    const res = resource.on('otherEvent', () => {})

    expect(onSpy).not.toHaveBeenCalled()
    expect(res).toBeUndefined()
  })
})
describe('BaseResource emit behavior', () => {
  let resource: BaseResource<any>
  let mockRepo: any
  beforeEach(() => {
    jest.clearAllMocks()

    mockRepo = {
      transaction: jest.fn((fn) => fn({})),
      nestedTransaction: jest.fn((fn) => fn({})),
      create: jest.fn(() => Promise.resolve({ id: 1 })),
      update: jest.fn((model, data) => Promise.resolve({ ...model, ...data })),
      destroy: jest.fn(() => Promise.resolve()),
      findById: jest.fn((id) => Promise.resolve({ id })),
      build: jest.fn(),
      findManyPaginated: jest.fn(() =>
        Promise.resolve({
          data: [],
          total: 0,
          limit: 0,
          skip: 0,
          currentPage: 0,
          lastPage: 0,
          from: 0,
          to: 0,
        }),
      ),
    }

    resource = new BaseResource(mockRepo as any)
  })
  it('emit calls emitter.emit and returns its value for object data', () => {
    const emitSpy = jest.spyOn(resource.getEmitter() as any, 'emit').mockReturnValue('ok')
    const payload = { id: 123 }

    const res = resource.emit('evt', payload)

    expect(emitSpy).toHaveBeenCalledWith('evt', payload)
    expect(res).toBe('ok')
  })

  it('emit calls emitter.emit and returns its value for non-empty array data', () => {
    const emitSpy = jest.spyOn(resource.getEmitter() as any, 'emit').mockReturnValueOnce('arrayOk')
    const payload = [{ id: 1 }, { id: 2 }]

    const res = resource.emit('evtArray', payload)

    expect(emitSpy).toHaveBeenCalledWith('evtArray', payload)
    expect(res).toBe('arrayOk')
  })

  it('emit returns undefined when emitter.emit is missing', () => {
    ;(resource.getEmitter() as any).emit = undefined
    const payload = { id: 9 }

    const res = resource.emit('evtMissing', payload)

    expect(res).toBeUndefined()
  })
})
describe('BaseResource maybeEventTransaction additional tests', () => {
  let resource: BaseResource<any>
  let mockRepo: any

  beforeEach(() => {
    jest.clearAllMocks()

    mockRepo = {
      transaction: jest.fn((fn) => fn({})),
      nestedTransaction: jest.fn((fn) => fn({})),
      create: jest.fn(() => Promise.resolve({ id: 1 })),
      update: jest.fn((model, data) => Promise.resolve({ ...model, ...data })),
      destroy: jest.fn(() => Promise.resolve()),
      findById: jest.fn((id) => Promise.resolve({ id })),
      build: jest.fn(),
      findManyPaginated: jest.fn(() =>
        Promise.resolve({
          data: [],
          total: 0,
          limit: 0,
          skip: 0,
          currentPage: 0,
          lastPage: 0,
          from: 0,
          to: 0,
        }),
      ),
    }

    resource = new BaseResource(mockRepo as any)
  })

  it('uses provided eventTransaction when passed and does not call optimizeEvents', async () => {
    const passed: EventTransaction = { events: [], pushEmit: jest.fn() }
    const spyFn = jest.fn(async (t: EventTransaction) => 'ok')

    const res = await resource.maybeEventTransaction(spyFn, passed)

    expect(res).toBe('ok')
    expect(spyFn).toHaveBeenCalledTimes(1)
    expect(spyFn).toHaveBeenCalledWith(passed)
    expect(optimizeEvents).not.toHaveBeenCalled()
  })

  it('creates a new eventTransaction when none provided, calls optimizeEvents and emits pushed events', async () => {
    const mockEmitter = new EventEmitter()
    mockEmitter.emit = jest.fn()

    const spyFn = jest.fn(async (t: EventTransaction) => {
      // push an event into the created transaction
      t.pushEmit(mockEmitter as any, 'evtX', { id: 55 })
      return 'done'
    })

    const res = await resource.maybeEventTransaction(spyFn)

    expect(res).toBe('done')
    expect(spyFn).toHaveBeenCalledTimes(1)

    // Inspect the transaction object that was passed to spyFn
    const createdTx = spyFn.mock.calls[0][0]
    expect(createdTx).toBeDefined()
    expect(Array.isArray(createdTx.events)).toBe(true)
    expect(typeof createdTx.pushEmit).toBe('function')

    // optimizeEvents should have been called as the transaction emits after fn resolves
    expect(optimizeEvents).toHaveBeenCalled()
    // And the emitter used in the pushed event must have been called with the event and payload
    expect(mockEmitter.emit).toHaveBeenCalledWith('evtX', { id: 55 })
  })
})
describe('BaseResource maybeEventTransaction error cases and transaction methods', () => {
  let resource: BaseResource<any>
  let mockRepo: any

  beforeEach(() => {
    jest.clearAllMocks()

    mockRepo = {
      transaction: jest.fn((fn) => fn({})),
      nestedTransaction: jest.fn((fn) => fn({})),
      create: jest.fn(() => Promise.resolve({ id: 1 })),
      update: jest.fn((model, data) => Promise.resolve({ ...model, ...data })),
      destroy: jest.fn(() => Promise.resolve()),
      findById: jest.fn((id) => Promise.resolve({ id })),
      build: jest.fn(),
      findManyPaginated: jest.fn(() =>
        Promise.resolve({
          data: [],
          total: 0,
          limit: 0,
          skip: 0,
          currentPage: 0,
          lastPage: 0,
          from: 0,
          to: 0,
        }),
      ),
    }

    resource = new BaseResource(mockRepo as any)
  })

  it('maybeEventTransaction does not emit and propagates error when fn throws (created transaction)', async () => {
    const mockEmitter = new EventEmitter()
    mockEmitter.emit = jest.fn()

    const failingFn = jest.fn(async (t: EventTransaction) => {
      t.pushEmit(mockEmitter as any, 'errEvent', { id: 999 })
      throw new Error('intentional failure')
    })

    await expect(resource.maybeEventTransaction(failingFn)).rejects.toThrow('intentional failure')

    // When fn throws, eventTransaction should not emit and optimizeEvents should not be called
    expect(optimizeEvents).not.toHaveBeenCalled()
    expect(mockEmitter.emit).not.toHaveBeenCalled()
  })

  it('maybeEventTransaction with provided EventTransaction propagates error and does not call optimizeEvents', async () => {
    const passed: EventTransaction = {
      events: [],
      pushEmit: jest.fn(function (this: any, emitter, event, data) {
        this.events.push([emitter, event, data])
      }),
    }

    const failingFn = jest.fn(async (t: EventTransaction) => {
      t.pushEmit(new EventEmitter() as any, 'x', { id: 1 })
      throw new Error('boom')
    })

    await expect(resource.maybeEventTransaction(failingFn, passed)).rejects.toThrow('boom')

    // Since we provided an EventTransaction, resource should not create/emit it and optimizeEvents must not be called
    expect(optimizeEvents).not.toHaveBeenCalled()
  })

  it('transaction delegates to repository.transaction and returns the function result', () => {
    // Override mockRepo.transaction to pass a specific transaction token
    mockRepo.transaction = jest.fn((fn) => fn('repoTx'))
    resource = new BaseResource(mockRepo as any)

    const fn = jest.fn((tx) => `result:${tx}`)
    const res = resource.transaction(fn)

    expect(mockRepo.transaction).toHaveBeenCalledTimes(1)
    expect(fn).toHaveBeenCalledWith('repoTx')
    expect(res).toBe('result:repoTx')
  })

  it('nestedTransaction delegates to repository.nestedTransaction and returns the function result', () => {
    const spyFn = jest.fn((tx) => `nested:${tx}`)
    mockRepo.nestedTransaction = jest.fn((fn, t) => fn('nestedTx'))
    resource = new BaseResource(mockRepo as any)

    const res = resource.nestedTransaction(spyFn, 'outerTx')

    expect(mockRepo.nestedTransaction).toHaveBeenCalledWith(spyFn, 'outerTx')
    expect(spyFn).toHaveBeenCalledWith('nestedTx')
    expect(res).toBe('nested:nestedTx')
  })
})
// Additional tests for BaseResource methods: build, onCreated, onUpdated, onDestroyed, reload
describe('BaseResource additional methods (build, onCreated/onUpdated/onDestroyed, reload)', () => {
  let resource: BaseResource<any>
  let mockRepo: any

  beforeEach(() => {
    jest.clearAllMocks()

    mockRepo = {
      transaction: jest.fn((fn) => fn({})),
      nestedTransaction: jest.fn((fn) => fn({})),
      create: jest.fn(() => Promise.resolve({ id: 1 })),
      update: jest.fn((model, data) => Promise.resolve({ ...model, ...data })),
      destroy: jest.fn(() => Promise.resolve()),
      findById: jest.fn((id) => Promise.resolve({ id })),
      build: jest.fn(),
      reload: jest.fn(),
      findManyPaginated: jest.fn(() =>
        Promise.resolve({
          data: [],
          total: 0,
          limit: 0,
          skip: 0,
          currentPage: 0,
          lastPage: 0,
          from: 0,
          to: 0,
        }),
      ),
    }

    resource = new BaseResource(mockRepo as any)
  })

  it('build delegates to repository.build with provided args and returns its value', () => {
    const modelData = { foo: 'bar' }
    const opts = { isNewRecord: true }
    mockRepo.build.mockReturnValueOnce({ built: true })

    const res = resource.build(modelData, opts)

    expect(mockRepo.build).toHaveBeenCalledWith(modelData, opts)
    expect(res).toEqual({ built: true })
  })

  it('onCreated delegates to emitter.on with CREATED and returns its value', () => {
    const onSpy = jest.fn().mockReturnValue('createdRegistered')
    ;(resource.getEmitter() as any).on = onSpy

    const listener = jest.fn()
    const res = resource.onCreated(listener)

    expect(onSpy).toHaveBeenCalledWith(CREATED, listener)
    expect(res).toBe('createdRegistered')
  })

  it('onUpdated delegates to emitter.on with UPDATED and returns its value', () => {
    const onSpy = jest.fn().mockReturnValue('updatedRegistered')
    ;(resource.getEmitter() as any).on = onSpy

    const listener = jest.fn()
    const res = resource.onUpdated(listener)

    expect(onSpy).toHaveBeenCalledWith(UPDATED, listener)
    expect(res).toBe('updatedRegistered')
  })

  it('onDestroyed delegates to emitter.on with DESTROYED and returns its value', () => {
    const onSpy = jest.fn().mockReturnValue('destroyedRegistered')
    ;(resource.getEmitter() as any).on = onSpy

    const listener = jest.fn()
    const res = resource.onDestroyed(listener)

    expect(onSpy).toHaveBeenCalledWith(DESTROYED, listener)
    expect(res).toBe('destroyedRegistered')
  })

  it('onCreated does not call emitter.on and returns undefined when emitter.emit is missing', () => {
    const onSpy = jest.fn()
    ;(resource.getEmitter() as any).on = onSpy
    ;(resource.getEmitter() as any).emit = undefined

    const res = resource.onCreated(() => {})

    expect(onSpy).not.toHaveBeenCalled()
    expect(res).toBeUndefined()
  })

  it('reload delegates to repository.reload and returns its promise result', async () => {
    const model = { id: 9 }
    const options = { some: true }
    mockRepo.reload.mockResolvedValueOnce({ ...model, reloaded: true })

    const res = await resource.reload(model, options)

    expect(mockRepo.reload).toHaveBeenCalledWith(model, options)
    expect(res).toEqual({ ...model, reloaded: true })
  })
})
describe('BaseResource repository delegation methods', () => {
  let resource: BaseResource<any>
  let mockRepo: any

  beforeEach(() => {
    jest.clearAllMocks()

    mockRepo = {
      transaction: jest.fn((fn) => fn({})),
      nestedTransaction: jest.fn((fn) => fn({})),
      create: jest.fn(() => Promise.resolve({ id: 1 })),
      update: jest.fn((model, data) => Promise.resolve({ ...model, ...data })),
      destroy: jest.fn(() => Promise.resolve()),
      findById: jest.fn((id, q) => Promise.resolve({ id, q })),
      findManyByIds: jest.fn((ids, q) => Promise.resolve(ids.map((id: any) => ({ id, q })))),
      findManyPaginated: jest.fn((q) =>
        Promise.resolve({
          data: [{ id: 'p1' }],
          total: 1,
          limit: 10,
          skip: 0,
          currentPage: 1,
          lastPage: 1,
          from: 1,
          to: 1,
          query: q,
        }),
      ),
      findMany: jest.fn((q) => Promise.resolve([{ ...q }])),
      count: jest.fn((q) => Promise.resolve(42)),
      max: jest.fn((key, opts) => Promise.resolve(99)),
      findManyWithTotal: jest.fn((q) => Promise.resolve({ data: [], total: 0, query: q })),
      existsById: jest.fn((id, q) => Promise.resolve(!!id)),
      exists: jest.fn((q) => Promise.resolve(true)),
      findOne: jest.fn((q) => Promise.resolve({ found: q })),
      build: jest.fn(),
      reload: jest.fn(),
      findManyPaginated: jest.fn((q) =>
        Promise.resolve({
          data: [],
          total: 0,
          limit: 0,
          skip: 0,
          currentPage: 0,
          lastPage: 0,
          from: 0,
          to: 0,
        }),
      ),
    }

    resource = new BaseResource(mockRepo as any)
  })

  it('findManyPaginated delegates to repository.findManyPaginated and returns its result', async () => {
    const expected = {
      data: [{ id: 'x' }],
      total: 1,
      limit: 10,
      skip: 0,
      currentPage: 1,
      lastPage: 1,
      from: 1,
      to: 1,
    }
    mockRepo.findManyPaginated.mockResolvedValueOnce(expected)

    const query = { page: 1, perPage: 10 }
    const res = await resource.findManyPaginated(query)

    expect(mockRepo.findManyPaginated).toHaveBeenCalledWith(query)
    expect(res).toBe(expected)
  })

  it('findMany delegates to repository.findMany and returns its result', async () => {
    const expected = [{ id: 1 }]
    mockRepo.findMany.mockResolvedValueOnce(expected)

    const q = { where: { a: 1 } }
    const res = await resource.findMany(q)

    expect(mockRepo.findMany).toHaveBeenCalledWith(q)
    expect(res).toBe(expected)
  })

  it('count delegates to repository.count and returns its result', async () => {
    mockRepo.count.mockResolvedValueOnce(7)

    const q = { where: { b: 2 } }
    const res = await resource.count(q)

    expect(mockRepo.count).toHaveBeenCalledWith(q)
    expect(res).toBe(7)
  })

  it('max delegates to repository.max with key and options and returns its result', async () => {
    mockRepo.max.mockResolvedValueOnce(123)

    const res = await resource.max('score', { where: { active: true } })

    expect(mockRepo.max).toHaveBeenCalledWith('score', { where: { active: true } })
    expect(res).toBe(123)
  })

  it('findManyWithTotal delegates to repository.findManyWithTotal and returns its result', async () => {
    const expected = { data: [{ id: 'z' }], total: 1 }
    mockRepo.findManyWithTotal.mockResolvedValueOnce(expected)

    const q = { include: ['rel'] }
    const res = await resource.findManyWithTotal(q)

    expect(mockRepo.findManyWithTotal).toHaveBeenCalledWith(q)
    expect(res).toBe(expected)
  })

  it('findById delegates to repository.findById and returns the found model', async () => {
    const expected = { id: 'abc' }
    mockRepo.findById.mockResolvedValueOnce(expected)

    const res = await resource.findById('abc', { raw: true })

    expect(mockRepo.findById).toHaveBeenCalledWith('abc', { raw: true })
    expect(res).toBe(expected)
  })

  it('findManyByIds delegates to repository.findManyByIds and returns results', async () => {
    const ids = ['1', '2', '3']
    const expected = ids.map((id) => ({ id }))
    mockRepo.findManyByIds.mockResolvedValueOnce(expected)

    const res = await resource.findManyByIds(ids, { attributes: ['id'] })

    expect(mockRepo.findManyByIds).toHaveBeenCalledWith(ids, { attributes: ['id'] })
    expect(res).toBe(expected)
  })

  it('existsById delegates to repository.existsById and returns boolean', async () => {
    mockRepo.existsById.mockResolvedValueOnce(true)

    const res = await resource.existsById('someId', { paranoid: false })

    expect(mockRepo.existsById).toHaveBeenCalledWith('someId', { paranoid: false })
    expect(res).toBe(true)
  })

  it('exists delegates to repository.exists and returns boolean', async () => {
    mockRepo.exists.mockResolvedValueOnce(false)

    const q = { where: { x: 'y' } }
    const res = await resource.exists(q)

    expect(mockRepo.exists).toHaveBeenCalledWith(q)
    expect(res).toBe(false)
  })

  it('findOne delegates to repository.findOne and returns the result', async () => {
    const expected = { id: 'one' }
    mockRepo.findOne.mockResolvedValueOnce(expected)

    const q = { where: { one: true } }
    const res = await resource.findOne(q)

    expect(mockRepo.findOne).toHaveBeenCalledWith(q)
    expect(res).toBe(expected)
  })
})
// TypeScript - tests to append for BaseResource methods: upsert, updateMany, bulkCreate, bulkUpdate, bulkDestroy, updateById, destroyMany, destroyById

describe('BaseResource additional CRUD methods', () => {
  let resource: BaseResource<any>
  let mockRepo: any

  beforeEach(() => {
    jest.clearAllMocks()

    mockRepo = {
      transaction: jest.fn((fn) => fn({})),
      nestedTransaction: jest.fn((fn, t) => fn(t)),
      create: jest.fn(() => Promise.resolve({ id: 1 })),
      update: jest.fn((model, data) => Promise.resolve({ ...model, ...data })),
      destroy: jest.fn(() => Promise.resolve()),
      findById: jest.fn((id) => Promise.resolve({ id })),
      build: jest.fn(),
      findManyPaginated: jest.fn(() =>
        Promise.resolve({
          data: [],
          total: 0,
          limit: 0,
          skip: 0,
          currentPage: 0,
          lastPage: 0,
          from: 0,
          to: 0,
        }),
      ),
      bulkCreate: jest.fn(),
      bulkUpdate: jest.fn(),
      bulkDestroy: jest.fn(),
      upsert: jest.fn(),
      findMany: jest.fn(),
    }

    resource = new BaseResource(mockRepo as any)
  })

  it('upsert delegates to repository.upsert and returns its value', async () => {
    const result = [{ id: 10 }, true]
    mockRepo.upsert.mockResolvedValueOnce(result)

    const res = await resource.upsert({ some: 'data' }, { someOpt: true })

    expect(mockRepo.upsert).toHaveBeenCalledWith({ some: 'data' }, { someOpt: true })
    expect(res).toBe(result)
  })

  it('updateMany calls update for each item and emits UPDATED with array', async () => {
    const models = [
      { id: 1, a: 0 },
      { id: 2, a: 0 },
    ]
    const datas = [{ a: 1 }, { a: 2 }]

    const updateSpy = jest
      .spyOn(resource as any, 'update')
      .mockImplementation((model: any, data: any, opts: any) => Promise.resolve({ ...model, ...data }))

    const emitSpy = jest.spyOn(resource.getEmitter() as any, 'emit')

    const res = await resource.updateMany(models, datas)

    expect(updateSpy).toHaveBeenCalledTimes(models.length)
    models.forEach((m, i) => {
      expect(updateSpy).toHaveBeenCalledWith(m, datas[i], expect.objectContaining({ dontEmit: true }))
    })
    expect(emitSpy).toHaveBeenCalledWith(
      UPDATED,
      expect.arrayContaining([
        { id: 1, a: 1 },
        { id: 2, a: 2 },
      ]),
    )
    expect(res).toEqual(
      expect.arrayContaining([
        { id: 1, a: 1 },
        { id: 2, a: 2 },
      ]),
    )
  })

  it('bulkCreate delegates to repository.bulkCreate and emits CREATED', async () => {
    const data = [{ x: 1 }, { x: 2 }]
    const created = [
      { id: 5, x: 1 },
      { id: 6, x: 2 },
    ]
    mockRepo.bulkCreate.mockResolvedValueOnce(created)

    const emitSpy = jest.spyOn(resource.getEmitter() as any, 'emit')

    const res = await resource.bulkCreate(data)

    expect(mockRepo.bulkCreate).toHaveBeenCalledWith(data, {})
    expect(emitSpy).toHaveBeenCalledWith(CREATED, created)
    expect(res).toBe(created)
  })

  it('bulkUpdate delegates to repository.bulkUpdate with returning true and emits UPDATED', async () => {
    const payload = { some: 'payload' }
    const returned = [{ id: 7 }, { id: 8 }]
    mockRepo.bulkUpdate.mockResolvedValueOnce(returned)

    const emitSpy = jest.spyOn(resource.getEmitter() as any, 'emit')

    const res = await resource.bulkUpdate(payload, { otherOpt: 'v' })

    expect(mockRepo.bulkUpdate).toHaveBeenCalledWith(
      payload,
      expect.objectContaining({ otherOpt: 'v', returning: true }),
    )
    expect(emitSpy).toHaveBeenCalledWith(UPDATED, returned)
    expect(res).toBe(returned)
  })

  it('bulkDestroy collects models via pagination, calls repository.bulkDestroy and emits DESTROYED', async () => {
    // Simulate paginated results: page 1 has two models, page 2 empty
    mockRepo.findManyPaginated = jest.fn((query) =>
      Promise.resolve({
        data: query.page === 1 ? [{ id: 11 }, { id: 12 }] : [],
        total: 2,
        limit: 2,
        skip: 0,
        currentPage: query.page,
        lastPage: 1,
        from: 1,
        to: 2,
      }),
    )

    mockRepo.bulkDestroy.mockResolvedValueOnce(undefined)

    const emitSpy = jest.spyOn(resource.getEmitter() as any, 'emit')

    const options: any = {}
    const res = await resource.bulkDestroy(options)

    // options should have been mutated to include raw = true before calling bulkDestroy
    expect(mockRepo.bulkDestroy).toHaveBeenCalledWith(expect.objectContaining({ raw: true }))
    expect(emitSpy).toHaveBeenCalledWith(DESTROYED, expect.arrayContaining([{ id: 11 }, { id: 12 }]))
    expect(res).toEqual(expect.arrayContaining([{ id: 11 }, { id: 12 }]))
  })

  it('updateById finds by id then updates and returns updated model', async () => {
    const id = 'abc'
    const original = { id, a: 1 }
    const updated = { id, a: 2 }
    mockRepo.findById.mockResolvedValueOnce(original)
    const updateSpy = jest.spyOn(resource as any, 'update').mockResolvedValueOnce(updated)

    const res = await resource.updateById(id, { a: 2 }, { someOpt: true })

    expect(mockRepo.findById).toHaveBeenCalledWith(id, { someOpt: true })
    expect(updateSpy).toHaveBeenCalledWith(original, { a: 2 }, { someOpt: true })
    expect(res).toEqual(updated)
  })

  it('destroyById finds by id then destroys and returns the model', async () => {
    const id = 'z1'
    const model = { id }
    mockRepo.findById.mockResolvedValueOnce(model)
    const destroySpy = jest.spyOn(resource as any, 'destroy').mockResolvedValueOnce(model)

    const res = await resource.destroyById(id, { some: 'opt' })

    expect(mockRepo.findById).toHaveBeenCalledWith(id, { some: 'opt' })
    expect(destroySpy).toHaveBeenCalledWith(model, { some: 'opt' })
    expect(res).toBe(model)
  })
})
describe('BaseResource destroyMany', () => {
  let resource: BaseResource<any>
  let mockRepo: any

  beforeEach(() => {
    jest.clearAllMocks()

    mockRepo = {
      transaction: jest.fn((fn) => fn({})),
      // nestedTransaction will call provided fn with a simulated nested tx token 'repoTx'
      nestedTransaction: jest.fn((fn, t) => fn('repoTx')),
      create: jest.fn(() => Promise.resolve({ id: 1 })),
      update: jest.fn((model, data) => Promise.resolve({ ...model, ...data })),
      destroy: jest.fn(() => Promise.resolve()),
      findById: jest.fn((id) => Promise.resolve({ id })),
      build: jest.fn(),
      reload: jest.fn(),
      // findMany should return two models for destroyMany to process
      findMany: jest.fn(() => Promise.resolve([{ id: 1 }, { id: 2 }])),
      findManyPaginated: jest.fn(() =>
        Promise.resolve({
          data: [],
          total: 0,
          limit: 0,
          skip: 0,
          currentPage: 0,
          lastPage: 0,
          from: 0,
          to: 0,
        }),
      ),
    }

    resource = new BaseResource(mockRepo as any)
  })

  it('uses provided options.transaction for findMany and destroy, delegates to nestedTransaction, emits DESTROYED when dontEmit is false', async () => {
    const query = { where: { active: false } }
    const options = { transaction: 'outerTx' } as any

    const emitSpy = jest.spyOn(resource.getEmitter() as any, 'emit')

    const res = await resource.destroyMany(query, options)

    // nestedTransaction should have been called with a function and the outer transaction
    expect(mockRepo.nestedTransaction).toHaveBeenCalledTimes(1)
    const nestedArgs = mockRepo.nestedTransaction.mock.calls[0]
    expect(typeof nestedArgs[0]).toBe('function')
    expect(nestedArgs[1]).toBe('outerTx')

    // findMany should be called with the query merged with options.transaction
    expect(mockRepo.findMany).toHaveBeenCalledWith({ ...query, transaction: options.transaction })

    // destroy should be called for each model returned by findMany
    expect(mockRepo.destroy).toHaveBeenCalledTimes(2)
    // According to the implementation the destroy opts are { transaction, ...options, dontEmit: true }
    // Since options.transaction exists it will override the transaction value; thus expect transaction === options.transaction
    expect(mockRepo.destroy).toHaveBeenCalledWith(expect.objectContaining({ id: 1 }), {
      transaction: 'outerTx',
      ...options,
      dontEmit: true,
    })
    expect(mockRepo.destroy).toHaveBeenCalledWith(expect.objectContaining({ id: 2 }), {
      transaction: 'outerTx',
      ...options,
      dontEmit: true,
    })

    // Should have emitted DESTROYED with the models array because dontEmit is false by default
    expect(emitSpy).toHaveBeenCalledWith(DESTROYED, expect.arrayContaining([{ id: 1 }, { id: 2 }]))

    // The method returns the models array
    expect(res).toEqual(expect.arrayContaining([{ id: 1 }, { id: 2 }]))
  })

  it('uses nested transaction when options.transaction is not provided', async () => {
    const query = { where: { foo: 'bar' } }
    const options = {} as any

    const emitSpy = jest.spyOn(resource.getEmitter() as any, 'emit')

    const res = await resource.destroyMany(query, options)

    // nestedTransaction should have been called; second arg (outer transaction) should be undefined
    expect(mockRepo.nestedTransaction).toHaveBeenCalledTimes(1)
    const nestedArgs = mockRepo.nestedTransaction.mock.calls[0]
    expect(nestedArgs[1]).toBeUndefined()

    // findMany should have been called with transaction: undefined (options.transaction absent)
    expect(mockRepo.findMany).toHaveBeenCalledWith({ ...query, transaction: undefined })

    // destroy should be called for each model and receive the nested tx token 'repoTx'
    expect(mockRepo.destroy).toHaveBeenCalledTimes(2)
    expect(mockRepo.destroy).toHaveBeenCalledWith(expect.objectContaining({ id: 1 }), {
      transaction: 'repoTx',
      ...options,
      dontEmit: true,
    })
    expect(mockRepo.destroy).toHaveBeenCalledWith(expect.objectContaining({ id: 2 }), {
      transaction: 'repoTx',
      ...options,
      dontEmit: true,
    })

    // Should have emitted DESTROYED with the models array
    expect(emitSpy).toHaveBeenCalledWith(DESTROYED, expect.arrayContaining([{ id: 1 }, { id: 2 }]))

    expect(res).toEqual(expect.arrayContaining([{ id: 1 }, { id: 2 }]))
  })

  it('does not emit when options.dontEmit is true', async () => {
    const query = { where: { z: true } }
    const options = { dontEmit: true } as any

    const emitSpy = jest.spyOn(resource.getEmitter() as any, 'emit')

    const res = await resource.destroyMany(query, options)

    // destroy should still be called for each model (with nested tx 'repoTx')
    expect(mockRepo.destroy).toHaveBeenCalledTimes(2)
    expect(mockRepo.destroy).toHaveBeenCalledWith(expect.objectContaining({ id: 1 }), {
      transaction: 'repoTx',
      ...options,
      dontEmit: true,
    })

    // Because options.dontEmit is true, there should be no emit of DESTROYED
    expect(emitSpy).not.toHaveBeenCalledWith(DESTROYED, expect.anything())

    expect(res).toEqual(expect.arrayContaining([{ id: 1 }, { id: 2 }]))
  })
})
