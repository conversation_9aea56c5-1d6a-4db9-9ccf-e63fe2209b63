jest.mock('../../../../core/services/logs/Logger', () => jest.fn())
jest.mock('../../../../core/services/config/Config', () => jest.fn())
jest.mock('../../../../core/config', () => jest.fn())

jest.mock('typedi', () => {
  const mockOracleStorage = {
    setBucket: (key: string) => jest.fn(),
  }

  const mockConfig = {
    get: jest.fn((key: string) => {
      if (key === 'buckets') return 'bucket1,bucket2'
    }),
  }

  return {
    __esModule: true,
    default: {
      get: jest.fn().mockReturnValue({
        log: jest.fn(),
        captureError: jest.fn(),
        dispatch: jest.fn(),
      }),
    },
    Container: {
      get: jest.fn((service) => {
        if (service === require('../../../../core/services/config/Config')) return mockConfig
        else if (service === require('../../../../core/services/storage/OracleStorage').default)
          return mockOracleStorage
        return {}
      }),
    },
    Service: () => jest.fn(),
    Inject: () => jest.fn(),
  }
})

jest.mock('../../../../core/services/db/sequelize', () => ({
  __esModule: true,
  default: {
    define: jest.fn(() => ({
      prototype: {
        hasPermission: jest.fn(),
      },
      associate: jest.fn(),
      belongsTo: jest.fn(),
    })),
  },
}))

jest.mock('../../../../core/services/ticket/ticketService', () => ({
  __esModule: true,
  summaryTicket: jest.fn().mockReturnValue({ summary: 'This is a ticket summary' }),
}))

jest.mock('../../../../core/resources/messageResource', () => ({}))
jest.mock('../../../../core/resources/ticketTransfersResource', () => ({}))
jest.mock('../../../../core/resources/ticketResource', () => ({}))
jest.mock('../../../../core/resources/serviceResource', () => ({}))

jest.mock('../../../../core/dbSequelize/repositories/accountRepository', () => ({
  __esModule: true,
  default: {
    updateById: jest.fn().mockResolvedValue({
      id: 'mockAccountId',
      name: 'Test Account',
      settings: { drivers: { whatsapp: true }, campaign: {} },
      wizardProgress: 'started',
    }),
  },
}))

jest.mock('../../../../core/services/creditMovement/CreditsControlCacheService', () => jest.fn())
jest.mock('../../../../core/services/crypt/accountCryptor', () => jest.fn())
jest.mock('../../../../core/resources/creditMovementResource', () => jest.fn())
jest.mock('../../../../core/resources/departmentResource', () => jest.fn())
jest.mock('../../../../core/resources/notificationResource', () => jest.fn())
jest.mock('../../../../core/resources/permissionResource', () => jest.fn())
jest.mock('../../../../core/resources/roleResource', () => jest.fn())
jest.mock('../../../../core/resources/planAiHistoryResource', () => jest.fn())
jest.mock('../../../../core/resources/subscriptionResource', () => jest.fn())
jest.mock('../../../../core/resources/tagResource', () => jest.fn())

describe('AccountResource.sendEmailDisable', () => {
  let findAdminUsersMock: jest.Mock
  let sendMFANotificationMock: jest.Mock
  let changeLanguageMock: jest.Mock
  let tMock: jest.Mock

  const loadSUT = () => {
    jest.isolateModules(() => {
      changeLanguageMock = jest.fn()
      tMock = jest.fn((k) => k)
      jest.doMock('../../../../core/i18n', () => ({
        __esModule: true,
        default: {
          changeLanguage: changeLanguageMock,
          t: tMock,
        },
        changeLanguage: changeLanguageMock,
        t: tMock,
      }))

      sendMFANotificationMock = jest.fn().mockResolvedValue(undefined)
      jest.doMock('../../../../core/services/email', () => ({
        __esModule: true,
        sendMFANotification: sendMFANotificationMock,
      }))

      findAdminUsersMock = jest.fn()
      jest.doMock('../../../../core/resources/userResource', () => ({
        __esModule: true,
        default: { findAdminUsers: findAdminUsersMock },
        findAdminUsers: findAdminUsersMock,
      }))

      jest.doMock('../../../../core/resources/BaseResource', () => ({
        __esModule: true,
        default: class {
          getRepository() {
            return {
              transaction: async (cb: any) => cb({}),
            }
          }
        },
      }))

      const mod = require('../../../../core/resources/accountResource')
      ;(global as any).accountResource = mod.default || mod
    })
  }

  beforeEach(() => {
    jest.resetModules()
    jest.clearAllMocks()
    loadSUT()
  })

  it('sends email to each admin with correct texts', async () => {
    const users = [
      { email: '<EMAIL>', language: 'pt-BR' },
      { email: '<EMAIL>', language: 'en-US' },
    ]
    findAdminUsersMock.mockResolvedValue(users)

    await (global as any).accountResource.sendEmailDisable('acc-1')

    expect(findAdminUsersMock).toHaveBeenCalledWith('acc-1')
    expect(changeLanguageMock).toHaveBeenCalledTimes(2)
    expect(changeLanguageMock).toHaveBeenCalledWith('pt-BR')
    expect(changeLanguageMock).toHaveBeenCalledWith('en-US')

    expect(sendMFANotificationMock).toHaveBeenNthCalledWith(1, {
      to: '<EMAIL>',
      titulo: 'account:MFA_TITULO_DISABLE',
      texto: 'account:MFA_TEXTO_DISABLE',
      texto_final: 'account:MFA_TEXTO_FINAL_DISABLE',
      texto_rodape: 'account:MFA_TEXTO_RODAPE_DISABLE',
    })
    expect(sendMFANotificationMock).toHaveBeenNthCalledWith(2, {
      to: '<EMAIL>',
      titulo: 'account:MFA_TITULO_DISABLE',
      texto: 'account:MFA_TEXTO_DISABLE',
      texto_final: 'account:MFA_TEXTO_FINAL_DISABLE',
      texto_rodape: 'account:MFA_TEXTO_RODAPE_DISABLE',
    })
  })

  it('does not fail if there are no admins', async () => {
    findAdminUsersMock.mockResolvedValue([])

    await expect((global as any).accountResource.sendEmailDisable('acc-2')).resolves.toBeUndefined()

    expect(findAdminUsersMock).toHaveBeenCalledWith('acc-2')
    expect(sendMFANotificationMock).not.toHaveBeenCalled()
    expect(changeLanguageMock).not.toHaveBeenCalled()
  })
})

describe('AccountResource.expiresUserAccessForTwoFactorAuth', () => {
  let accountResource: any
  let notificationCreateMock: jest.Mock
  let findManyPaginatedMock: jest.Mock
  let iterateUsers: any

  const FIXED_NOW = new Date('2025-01-01T12:00:00Z')

  const loadSUT = (users: Array<any>) => {
    jest.isolateModules(() => {
      jest.useFakeTimers().setSystemTime(FIXED_NOW)

      findManyPaginatedMock = jest.fn().mockImplementation(async (args) => {
        return { data: users, page: args.page, perPage: args.perPage, totalPages: 1 }
      })
      jest.doMock('../../../../core/resources/userResource', () => ({
        __esModule: true,
        default: { findManyPaginated: findManyPaginatedMock },
        findManyPaginated: findManyPaginatedMock,
      }))

      jest.doMock('../../../../core/utils/iteratePaginated', () => ({
        __esModule: true,
        default: async (pager: any, perItem: (u: any) => Promise<void>) => {
          const resp = await pager({ page: 1 })
          const items = resp?.data ?? []
          for (const u of items) await perItem(u)
          return undefined
        },
      }))

      notificationCreateMock = jest.fn().mockResolvedValue(undefined)
      jest.doMock('../../../../core/resources/notificationResource', () => ({
        __esModule: true,
        default: { create: notificationCreateMock },
        create: notificationCreateMock,
      }))

      jest.doMock('../../../../core/resources/BaseResource', () => ({
        __esModule: true,
        default: class {
          getRepository() {
            return { transaction: async (cb: any) => cb({}) }
          }
        },
      }))

      const mod = require('../../../../core/resources/accountResource')
      accountResource = mod.default || mod
    })
  }

  beforeEach(() => {
    jest.resetModules()
    jest.clearAllMocks()
  })

  afterEach(() => {
    jest.useRealTimers()
  })

  it('does not create notifications when notify=false', async () => {
    loadSUT([
      { id: 'u1', accountId: 'acc-123', status: 'online', language: 'pt-BR' },
      { id: 'u2', accountId: 'acc-123', status: 'away', language: 'en-US' },
    ])

    const ttl = new Date('2025-01-01T14:05:00Z')

    await accountResource.expiresUserAccessForTwoFactorAuth({ id: 'acc-123' }, ttl, /* notify */ false)

    expect(notificationCreateMock).not.toHaveBeenCalled()
  })

  it('creates notifications only for non-offline users with message per language and correct HH:MM', async () => {
    loadSUT([
      { id: 'u1', accountId: 'acc-123', status: 'online', language: 'pt-BR' },
      { id: 'u2', accountId: 'acc-123', status: 'away', language: 'en-US' },
      { id: 'u3', accountId: 'acc-123', status: 'offline', language: 'es' },
    ])

    const ttl = new Date('2025-01-01T14:05:00Z')

    await accountResource.expiresUserAccessForTwoFactorAuth({ id: 'acc-123' }, ttl, true)

    expect(notificationCreateMock).toHaveBeenCalledTimes(2)
    expect(notificationCreateMock).toHaveBeenCalledWith({
      userId: 'u1',
      type: 'warn',
      text: 'Seu acesso expirará em 02:05 minutos. No próximo acesso configure a autenticação de dois fatores (2FA).',
      accountId: 'acc-123',
    })
    expect(notificationCreateMock).toHaveBeenCalledWith({
      userId: 'u2',
      type: 'warn',
      text: 'Your access will expire in 02:05 minutes. On your next access, set up two-factor authentication (2FA).',
      accountId: 'acc-123',
    })
    const calls = notificationCreateMock.mock.calls.flat()
    expect(calls.some((arg: any) => arg?.userId === 'u3')).toBe(false)
    expect(findManyPaginatedMock).toHaveBeenCalledWith(
      expect.objectContaining({
        attributes: ['id', 'accountId', 'status', 'language'],
        where: {
          accountId: 'acc-123',
          otpAuthActive: false,
        },
        page: 1,
        perPage: 500,
      }),
    )
  })

  it('language fallback uses pt-BR when language is not recognized', async () => {
    loadSUT([{ id: 'uX', accountId: 'acc-999', status: 'online', language: 'fr-FR' }])

    const ttl = new Date('2025-01-01T12:30:00Z')
    await accountResource.expiresUserAccessForTwoFactorAuth('acc-999', ttl, true)

    expect(notificationCreateMock).toHaveBeenCalledWith({
      userId: 'uX',
      type: 'warn',
      text: 'Seu acesso expirará em 00:30 minutos. No próximo acesso configure a autenticação de dois fatores (2FA).',
      accountId: 'acc-999',
    })
  })
})

describe('AccountResource.extendGracePeriod', () => {
  let accountResource: any
  let updateMock: jest.Mock
  let sendGracePeriodNotificationMock: jest.Mock
  let reportErrorMock: jest.Mock
  let httpPostMock: jest.Mock

  const FIXED_NOW = new Date('2025-01-10T12:00:00Z')

  const baseAccount = () => ({
    id: 'acc-1',
    correlationId: 'corr-123',
    plan: {
      isOnGracePeriod: true,
      isGracePeriodOnHold: true,
      gracePeriodExtendTimesRemaining: 2,
      gracePeriodEndsAt: new Date('2025-01-09T00:00:00Z'),
      isActive: false,
    },
  })

  const user = { id: 'user-1' }

  const loadSUT = (configMap?: Partial<Record<string, any>>) => {
    jest.isolateModules(() => {
      jest.useFakeTimers().setSystemTime(FIXED_NOW)

      jest.doMock('../../../../core/config', () => {
        const map = {
          gracePeriodWebhookUrl: 'https://example.com/hook',
          gracePeriodWebhookToken: 'secret-token',
          ...(configMap || {}),
        }
        const fn = (key: string) => map[key]
        return { __esModule: true, default: fn }
      })

      sendGracePeriodNotificationMock = jest.fn().mockResolvedValue(undefined)
      jest.doMock('../../../../microServices/workers/jobs/account/GracePeriodJob', () => ({
        __esModule: true,
        sendGracePeriodNotification: sendGracePeriodNotificationMock,
      }))

      reportErrorMock = jest.fn()
      jest.doMock('../../../../core/services/logs/reportError', () => ({
        __esModule: true,
        default: reportErrorMock,
      }))

      updateMock = jest.fn().mockImplementation(async (_account, payload) => ({
        ..._account,
        plan: { ..._account.plan, ...(payload?.plan || {}) },
      }))
      jest.doMock('../../../../core/resources/BaseResource', () => ({
        __esModule: true,
        default: class {
          getRepository() {
            return {}
          }
          update = updateMock
        },
      }))

      const mod = require('../../../../core/resources/accountResource')
      accountResource = mod.default || mod

      httpPostMock = jest.fn().mockResolvedValue(undefined)
      accountResource.getHttpClient = () => ({ post: httpPostMock })
    })
  }

  beforeEach(() => {
    jest.resetModules()
    jest.clearAllMocks()
  })

  afterEach(() => {
    jest.useRealTimers()
  })

  it('throws error when not on grace period', async () => {
    loadSUT()
    const acc = { ...baseAccount(), plan: { ...baseAccount().plan, isOnGracePeriod: false } }

    await expect(accountResource.extendGracePeriod(acc, user)).rejects.toThrow('Not on grace period.')
    expect(sendGracePeriodNotificationMock).not.toHaveBeenCalled()
    expect(updateMock).not.toHaveBeenCalled()
  })

  it('throws error when no extensions remaining', async () => {
    loadSUT()
    const acc = { ...baseAccount(), plan: { ...baseAccount().plan, gracePeriodExtendTimesRemaining: 0 } }

    await expect(accountResource.extendGracePeriod(acc, user)).rejects.toThrow('Cannot extend grace period anymore.')
    expect(sendGracePeriodNotificationMock).not.toHaveBeenCalled()
    expect(updateMock).not.toHaveBeenCalled()
  })

  it('happy path', async () => {
    loadSUT()
    const acc = baseAccount()

    const result = await accountResource.extendGracePeriod(acc, user)

    expect(sendGracePeriodNotificationMock).toHaveBeenCalledWith(acc, user)

    expect(updateMock).toHaveBeenCalledTimes(1)
    const [, payload, options] = updateMock.mock.calls[0]
    expect(options).toEqual({ mergeJson: ['plan'] })
    expect(payload.plan.isActive).toBe(true)
    expect(payload.plan.isGracePeriodOnHold).toBe(false)
    expect(payload.plan.gracePeriodExtendTimesRemaining).toBe(acc.plan.gracePeriodExtendTimesRemaining - 1)
    expect(payload.plan.gracePeriodEndsAt).toBeInstanceOf(Date)
    const expected = new Date(FIXED_NOW.getTime() + 24 * 60 * 60 * 1000).getTime()
    expect((payload.plan.gracePeriodEndsAt as Date).getTime()).toBe(expected)

    expect(httpPostMock).toHaveBeenCalledWith('https://example.com/hook', {
      token: 'secret-token',
      id: acc.correlationId,
    })

    expect(result.plan.isActive).toBe(true)
  })

  it('does not trigger webhook when configs or correlationId are missing', async () => {
    loadSUT({ gracePeriodWebhookUrl: undefined })
    await accountResource.extendGracePeriod(baseAccount(), user)
    expect(httpPostMock).not.toHaveBeenCalled()

    jest.resetModules()
    jest.clearAllMocks()
    loadSUT({ gracePeriodWebhookToken: undefined })
    await accountResource.extendGracePeriod(baseAccount(), user)
    expect(httpPostMock).not.toHaveBeenCalled()

    jest.resetModules()
    jest.clearAllMocks()
    loadSUT()
    const noCorr = { ...baseAccount(), correlationId: undefined }
    await accountResource.extendGracePeriod(noCorr, user)
    expect(httpPostMock).not.toHaveBeenCalled()
  })

  it('logs error when worker fails and when webhook fails', async () => {
    loadSUT()
    sendGracePeriodNotificationMock.mockRejectedValueOnce(new Error('worker down'))
    await accountResource.extendGracePeriod(baseAccount(), user)
    expect(reportErrorMock).toHaveBeenCalled()

    jest.resetModules()
    jest.clearAllMocks()
    loadSUT()
    const acc = baseAccount()
    accountResource.getHttpClient = () => ({ post: jest.fn().mockRejectedValueOnce(new Error('http 500')) })
    const res = await accountResource.extendGracePeriod(acc, user)
    expect(reportErrorMock).toHaveBeenCalled()
    expect(res).toBeTruthy()
  })
})

describe('AccountResource.create', () => {
  let accountResource: any

  let baseTransactionMock: jest.Mock
  let accountCreateMock: jest.Mock
  let accountUpdateByIdMock: jest.Mock
  let departmentCreateMock: jest.Mock
  let roleCreateMock: jest.Mock
  let permissionFindManyMock: jest.Mock
  let permissionFindManyByNamesMock: jest.Mock
  let creditMovementCreateMock: jest.Mock
  let createEncryptedEncryptionKeyMock: jest.Mock

  const loadSUT = () => {
    jest.isolateModules(() => {
      baseTransactionMock = jest.fn(async (cb: any) => cb({}))
      jest.doMock('../../../../core/resources/BaseResource', () => ({
        __esModule: true,
        default: class {
          getRepository() {
            return { transaction: baseTransactionMock }
          }
          constructor(_repo?: any) {}
          emitCreated = jest.fn()
        },
      }))

      jest.doMock('../../../../core/resources/serviceResource', () => ({
        __esModule: true,
        default: {},
        TYPES: [
          'whatsapp',
          'telegram',
          'sms-wavy',
          'email',
          'facebook-messenger',
          'instagram',
          'google-business-message',
          'reclame-aqui',
        ],
      }))

      permissionFindManyMock = jest.fn().mockResolvedValue([{ id: 'p1' }, { id: 'p2' }])
      permissionFindManyByNamesMock = jest.fn().mockResolvedValue([{ id: 'op1' }, { id: 'op2' }])
      jest.doMock('../../../../core/resources/permissionResource', () => ({
        __esModule: true,
        default: {
          findMany: permissionFindManyMock,
          findManyByNames: permissionFindManyByNamesMock,
        },
      }))

      roleCreateMock = jest.fn().mockImplementation(async (payload) => ({
        ...payload,
        setPermissions: jest.fn().mockResolvedValue(undefined),
      }))
      jest.doMock('../../../../core/resources/roleResource', () => ({
        __esModule: true,
        default: { create: roleCreateMock },
        create: roleCreateMock,
      }))

      departmentCreateMock = jest.fn().mockResolvedValue({ id: 'dep-1' })
      jest.doMock('../../../../core/resources/departmentResource', () => ({
        __esModule: true,
        default: { create: departmentCreateMock },
        create: departmentCreateMock,
      }))

      creditMovementCreateMock = jest.fn().mockResolvedValue(undefined)
      jest.doMock('../../../../core/resources/creditMovementResource', () => ({
        __esModule: true,
        default: { create: creditMovementCreateMock },
        create: creditMovementCreateMock,
      }))

      accountCreateMock = jest.fn().mockImplementation(async (payload) => ({
        id: 'acc-1',
        ...payload,
      }))
      accountUpdateByIdMock = jest.fn().mockImplementation(async (_id, payload) => ({
        id: 'acc-1',
        ...payload,
        settings: { ...(payload?.settings || {}) },
      }))
      jest.doMock('../../../../core/dbSequelize/repositories/accountRepository', () => ({
        __esModule: true,
        default: {
          create: accountCreateMock,
          updateById: accountUpdateByIdMock,
        },
      }))

      createEncryptedEncryptionKeyMock = jest.fn().mockReturnValue(Buffer.from('key'))
      jest.doMock('../../../../core/services/crypt/accountCryptor', () => ({
        __esModule: true,
        createEncryptedEncryptionKey: createEncryptedEncryptionKeyMock,
      }))

      const mod = require('../../../../core/resources/accountResource')
      accountResource = mod.default || mod
    })
  }

  beforeEach(() => {
    jest.resetModules()
    jest.clearAllMocks()
    loadSUT()
  })

  it('creates account with defaults, department, roles and trial credits; returns updated account', async () => {
    const input = {
      name: 'Test Account',
      wizardProgress: 'started',
      settings: {
        drivers: { 'facebook-messenger': false },
        campaign: { 'auto-pause-mode': 'enabled' },
        flags: { 'enable-smart-csat-score': true },
      },
      expiresAt: null,
    }

    const result = await accountResource.create(input)

    expect(baseTransactionMock).toHaveBeenCalledTimes(1)

    expect(accountCreateMock).toHaveBeenCalledTimes(1)
    const createArgs = accountCreateMock.mock.calls[0][0]
    expect(createArgs.name).toBe('Test Account')
    expect(createArgs.encryptionKey).toBe('6b6579')
    expect(createArgs.settings.drivers['facebook-messenger']).toBe(false)
    expect(createArgs.settings.campaign['auto-pause-mode']).toBe('enabled')
    expect(createArgs.settings.flags['enable-smart-csat-score']).toBe(true)

    expect(departmentCreateMock).toHaveBeenCalledWith({ accountId: 'acc-1', name: 'Suporte' }, expect.any(Object))

    expect(accountUpdateByIdMock).toHaveBeenCalledTimes(1)
    const [updId, updPayload, updOpts] = accountUpdateByIdMock.mock.calls[0]
    expect(updId).toBe('acc-1')
    expect(updPayload).toMatchObject({
      defaultDepartmentId: 'dep-1',
      settings: expect.objectContaining({ smartCsatScoreEnabledAt: expect.any(Date) }),
    })
    expect(updOpts).toMatchObject({ mergeJson: ['settings'] })
    expect(updOpts).toHaveProperty('transaction')

    expect(roleCreateMock).toHaveBeenCalledTimes(2)
    expect(creditMovementCreateMock).toHaveBeenCalledWith(
      {
        type: 'in',
        serviceType: 'sms-wavy',
        origin: 'trial',
        amount: 20,
        accountId: 'acc-1',
      },
      expect.any(Object),
    )

    expect(result).toMatchObject({
      id: 'acc-1',
      defaultDepartmentId: 'dep-1',
      settings: expect.objectContaining({ smartCsatScoreEnabledAt: expect.any(Date) }),
    })
  })

  it('keeps defaults when no special flag is passed', async () => {
    const result = await accountResource.create({ name: 'No Flags' })
    expect(accountCreateMock).toHaveBeenCalledTimes(1)
    const settingsPayload = accountUpdateByIdMock.mock.calls[0][1].settings
    expect(settingsPayload.smartCsatScoreEnabledAt).toBeUndefined()
    expect(result.defaultDepartmentId).toBe('dep-1')
  })
})

describe('AccountResource.accountClientNotAccess', () => {
  let accountResource: any
  let findManyMock: jest.Mock

  const FIXED_NOW = new Date('2025-01-20T15:00:00Z')

  const loadSUT = () => {
    jest.isolateModules(() => {
      jest.useFakeTimers().setSystemTime(FIXED_NOW)

      const whereImpl = (...args: any[]) => ({ __type: 'where', args })
      const literalImpl = (val: string) => ({ __type: 'literal', val })
      const OpObj = { lt: Symbol('lt'), ne: Symbol('ne') }

      jest.doMock('sequelize', () => ({
        __esModule: true,
        default: { where: whereImpl, literal: literalImpl },
        Op: OpObj,
      }))

      findManyMock = jest.fn().mockResolvedValue([{ id: 'acc-1' }])
      jest.doMock('../../../../core/resources/BaseResource', () => ({
        __esModule: true,
        default: class {
          getRepository() {
            return { findMany: findManyMock }
          }
        },
      }))

      const mod = require('../../../../core/resources/accountResource')
      accountResource = mod.default || mod
    })
  }

  beforeEach(() => {
    jest.resetModules()
    jest.clearAllMocks()
    loadSUT()
  })

  afterEach(() => {
    jest.useRealTimers()
  })

  it('builds the query correctly and returns the repository result', async () => {
    const { Op } = require('sequelize') as { Op: any }
    const expectedDate = new Date(FIXED_NOW.getTime() - 3 * 24 * 60 * 60 * 1000)

    const result = await accountResource.accountClientNotAccess()

    expect(result).toEqual([{ id: 'acc-1' }])

    expect(findManyMock).toHaveBeenCalledTimes(1)
    const [query] = findManyMock.mock.calls[0]

    expect(query.attributes).toEqual(['id', 'name', 'settings', 'agnusSignatureKey', 'createdAt'])

    expect(Array.isArray(query.where.$and)).toBe(true)
    const and0 = query.where.$and[0]
    expect(and0?.__type).toBe('where')
    expect(and0.args?.[0]?.__type).toBe('literal')
    expect(and0.args?.[0]?.val).toBe(`plan->'isTrial'`)
    expect(and0.args?.[1]).toBe('false')

    expect(query.where.createdAt).toBeDefined()
    expect(query.where.createdAt[Op.lt]).toBeInstanceOf(Date)
    expect(query.where.createdAt[Op.lt].getTime()).toBe(expectedDate.getTime())

    expect(query.where.name[Op.ne]).toBe('MandeUmZap')

    expect(query.group).toBe('Account.id')

    expect(query.include).toMatchObject({
      attributes: [],
      model: 'messages',
      group: 'accountId',
    })

    expect(query.having?.__type).toBe('literal')
    expect(query.having?.val).toBe(
      `max(messages."createdAt") >= now() + interval '3' day or MAX(messages."createdAt") is null`,
    )
  })
})

describe('AccountResource.enableFeatureFlags', () => {
  let accountResource: any
  let findByIdMock: jest.Mock
  let updateByIdMock: jest.Mock

  const loadSUT = () => {
    jest.isolateModules(() => {
      jest.resetModules()

      const baseResourcePath = require.resolve('../../../../core/resources/BaseResource')

      findByIdMock = jest.fn()
      updateByIdMock = jest.fn()

      jest.doMock(baseResourcePath, () => ({
        __esModule: true,
        default: class {
          findById = findByIdMock
          updateById = updateByIdMock
        },
      }))

      const mod = require('../../../../core/resources/accountResource')
      accountResource = mod.default || mod
    })
  }

  beforeEach(() => {
    jest.clearAllMocks()
    loadSUT()
  })

  it('merges flags and calls updateById with mergeJson=["settings"]', async () => {
    const accountId = 'acc-123'
    const existingFlags = { alpha: true, beta: false, keep: 'x' }
    const incoming = { beta: true, gamma: 42 }

    findByIdMock.mockResolvedValue({
      id: accountId,
      settings: { flags: existingFlags },
    })

    updateByIdMock.mockResolvedValue({
      id: accountId,
      settings: { flags: { ...existingFlags, ...incoming } },
    })

    const result = await accountResource.enableFeatureFlags(accountId, incoming)

    expect(findByIdMock).toHaveBeenCalledWith(accountId, { attributes: ['settings'] })

    expect(updateByIdMock).toHaveBeenCalledTimes(1)
    const [updId, payload, opts] = updateByIdMock.mock.calls[0]

    expect(updId).toBe(accountId)
    expect(payload).toEqual({
      settings: {
        flags: {
          alpha: true,
          beta: true,
          keep: 'x',
          gamma: 42,
        },
      },
    })
    expect(opts).toEqual({ mergeJson: ['settings'] })

    expect(result).toEqual({
      id: accountId,
      settings: { flags: { alpha: true, beta: true, keep: 'x', gamma: 42 } },
    })
  })

  it('throws error when account does not exist and does not call updateById', async () => {
    const accountId = 'acc-missing'
    findByIdMock.mockResolvedValue(null)

    await expect(accountResource.enableFeatureFlags(accountId, { any: true })).rejects.toThrow(
      `Account: ${accountId} not found`,
    )

    expect(updateByIdMock).not.toHaveBeenCalled()
  })
})

describe('AccountResource.removeIaProducts', () => {
  let accountResource: any

  let findByIdMock: jest.Mock
  let updateByIdMock: jest.Mock
  let creditMovementCreateMock: jest.Mock
  let planAiHistoryCreateMock: jest.Mock

  const loadSUT = () => {
    jest.isolateModules(() => {
      jest.resetModules()

      const baseResourcePath = require.resolve('../../../../core/resources/BaseResource')
      const creditMovementPath = require.resolve('../../../../core/resources/creditMovementResource')
      const planAiHistoryPath = require.resolve('../../../../core/resources/planAiHistoryResource')
      const qamPath = require.resolve('../../../../core/utils/array/queuedAsyncMap')

      findByIdMock = jest.fn()
      updateByIdMock = jest.fn()

      jest.doMock(baseResourcePath, () => ({
        __esModule: true,
        default: class {
          findById = findByIdMock
          updateById = updateByIdMock
        },
      }))

      jest.doMock(qamPath, () => ({
        __esModule: true,
        default: async (keys: string[], fn: (k: string) => Promise<void>) => {
          for (const k of keys) await fn(k)
          return
        },
      }))

      creditMovementCreateMock = jest.fn().mockResolvedValue(undefined)
      jest.doMock(creditMovementPath, () => ({
        __esModule: true,
        default: { create: creditMovementCreateMock },
      }))

      planAiHistoryCreateMock = jest.fn().mockResolvedValue(undefined)
      jest.doMock(planAiHistoryPath, () => ({
        __esModule: true,
        default: { create: planAiHistoryCreateMock },
      }))

      const mod = require('../../../../core/resources/accountResource')
      accountResource = mod.default || mod
    })
  }

  beforeEach(() => {
    jest.clearAllMocks()
    loadSUT()
  })

  it('throws error when account does not exist and does not call update/credit/history', async () => {
    findByIdMock.mockResolvedValue(null)

    await expect(
      accountResource.removeIaProducts({
        accountId: 'missing-acc',
        products: 'PACK-X',
        ais: { summary: 1 },
        origin: 'ops',
      }),
    ).rejects.toThrow('Account: missing-acc not found')

    expect(updateByIdMock).not.toHaveBeenCalled()
    expect(creditMovementCreateMock).not.toHaveBeenCalled()
    expect(planAiHistoryCreateMock).not.toHaveBeenCalled()
  })

  it('updates balances, flags, logs history (when products) and creates credit movements of type out', async () => {
    const accountId = 'acc-1'

    const currentAI = {
      summary: 10,
      'magic-text': 0,
      transcription: 3,
      copilot: 5,
      csat: 1,
      agent: 0,
    }
    findByIdMock.mockResolvedValue({
      id: accountId,
      plan: { ai: { ...currentAI } },
      settings: { flags: { someExisting: true } },
    })

    updateByIdMock.mockResolvedValue(undefined)

    const params = {
      accountId,
      products: 'IA-PACK-BASIC',
      origin: 'foo-origin',
      ais: {
        summary: 4,
        'magic-text': 0,
        transcription: 10,
        copilot: 0,
        csat: 1,
        agent: 0,
      },
    }

    await accountResource.removeIaProducts(params)

    expect(updateByIdMock).toHaveBeenCalledTimes(1)
    const [updId, payload, opts] = updateByIdMock.mock.calls[0]
    expect(updId).toBe(accountId)

    const expectedAI = {
      summary: 6,
      'magic-text': 0,
      transcription: 0,
      copilot: 5,
      csat: 0,
      agent: 0,
    }

    expect(payload).toEqual({
      plan: {
        ai: expectedAI,
        products: 'IA-PACK-BASIC',
      },
      settings: {
        flags: {
          someExisting: true,
          'enable-smart-summary': true,
          'enable-audio-transcription': false,
          'enable-magic-text': false,
          'enable-copilot': true,
          'enable-smart-csat-score': false,
          'enable-bots-v3-ai-node': false,
        },
      },
    })
    expect(opts).toEqual({ mergeJson: ['plan', 'settings'] })

    expect(planAiHistoryCreateMock).toHaveBeenCalledTimes(1)
    expect(planAiHistoryCreateMock).toHaveBeenCalledWith({
      accountId,
      activity: 'change',
      name: 'IA-PACK-BASIC',
      magicText: false,
      summary: true,
      transcription: true,
      copilot: false,
      csat: true,
      agent: false,
    })

    const expectedOuts = [
      { serviceType: 'summary', amount: 4 },
      { serviceType: 'transcription', amount: 10 },
      { serviceType: 'csat', amount: 1 },
    ]
    for (const { serviceType, amount } of expectedOuts) {
      expect(creditMovementCreateMock).toHaveBeenCalledWith({
        type: 'out',
        serviceType,
        origin: 'foo-origin',
        amount,
        accountId,
      })
    }
    const allCalls = creditMovementCreateMock.mock.calls.map((c) => c[0].serviceType).sort()
    expect(allCalls).toEqual(['csat', 'summary', 'transcription'])
  })

  it('does not log history when products is not provided', async () => {
    const accountId = 'acc-2'
    findByIdMock.mockResolvedValue({
      id: accountId,
      plan: { ai: { summary: 2 } },
      settings: { flags: {} },
    })
    updateByIdMock.mockResolvedValue(undefined)

    await accountResource.removeIaProducts({
      accountId,
      ais: { summary: 2 },
      origin: 'ops',
    })

    expect(planAiHistoryCreateMock).not.toHaveBeenCalled()
    expect(creditMovementCreateMock).toHaveBeenCalledWith({
      type: 'out',
      serviceType: 'summary',
      origin: 'ops',
      amount: 2,
      accountId,
    })
  })
})

describe('AccountResource.updateIaProducts', () => {
  let accountResource: any

  let findByIdMock: jest.Mock
  let updateByIdMock: jest.Mock
  let creditMovementCreateMock: jest.Mock
  let planAiHistoryCreateMock: jest.Mock
  let getRenewDateSpy: jest.SpyInstance

  const loadSUT = () => {
    jest.isolateModules(() => {
      jest.resetModules()

      const baseResourcePath = require.resolve('../../../../core/resources/BaseResource')
      const creditMovementPath = require.resolve('../../../../core/resources/creditMovementResource')
      const planAiHistoryPath = require.resolve('../../../../core/resources/planAiHistoryResource')
      const qamPath = require.resolve('../../../../core/utils/array/queuedAsyncMap')

      findByIdMock = jest.fn()
      updateByIdMock = jest.fn()

      jest.doMock(baseResourcePath, () => ({
        __esModule: true,
        default: class {
          findById = findByIdMock
          updateById = updateByIdMock
        },
      }))

      jest.doMock(qamPath, () => ({
        __esModule: true,
        default: async (keys: string[], fn: (k: string) => Promise<void>) => {
          for (const k of keys) await fn(k)
          return
        },
      }))

      creditMovementCreateMock = jest.fn().mockResolvedValue(undefined)
      jest.doMock(creditMovementPath, () => ({
        __esModule: true,
        default: { create: creditMovementCreateMock },
      }))

      planAiHistoryCreateMock = jest.fn().mockResolvedValue(undefined)
      jest.doMock(planAiHistoryPath, () => ({
        __esModule: true,
        default: { create: planAiHistoryCreateMock },
      }))

      const mod = require('../../../../core/resources/accountResource')
      accountResource = mod.default || mod
    })
  }

  beforeEach(() => {
    jest.clearAllMocks()
    loadSUT()
  })

  it('uses getRenewDate when renewDate is not provided, updates plan/settings, logs history and credits (diff > 0)', async () => {
    const accountId = 'acc-1'

    const currentAI = {
      summary: 5,
      'magic-text': 1,
      transcription: 0,
      copilot: 0,
      csat: 3,
      agent: 2,
    }
    findByIdMock.mockResolvedValue({
      id: accountId,
      agnusSignatureKey: 'sig-123',
      plan: { ai: { ...currentAI } },
      settings: {
        flags: {
          'enable-magic-text': false,
          'enable-copilot': false,
          'enable-smart-summary': false,
          'enable-audio-transcription': false,
          'enable-smart-csat-score': true,
          'enable-bots-v3-ai-node': false,
        },
      },
    })

    updateByIdMock.mockResolvedValue(undefined)

    getRenewDateSpy = jest.spyOn(accountResource, 'getRenewDate').mockResolvedValue('2025-02-15')

    const params = {
      accountId,
      products: 'IA-PACK-PRO',
      ais: {
        summary: 7,
        'magic-text': 1,
        transcription: 4,
        copilot: 0,
        csat: 3,
        agent: 1,
      },
    }

    await accountResource.updateIaProducts(params.accountId, params.products, params.ais)

    expect(getRenewDateSpy).toHaveBeenCalledWith('sig-123')

    expect(updateByIdMock).toHaveBeenCalledTimes(1)
    const [updId, payload, opts] = updateByIdMock.mock.calls[0]
    expect(updId).toBe(accountId)

    expect(payload).toMatchObject({
      plan: {
        ai: {
          summary: 7,
          'magic-text': 1,
          transcription: 4,
          copilot: 0,
          csat: 3,
          agent: 1,
        },
        products: 'IA-PACK-PRO',
        renewDate: '2025-02-15',
      },
    })

    expect(payload.settings.flags).toEqual({
      'enable-smart-summary': true,
      'enable-audio-transcription': true,
      'enable-magic-text': true,
      'enable-copilot': false,
      'enable-smart-csat-score': true,
      'enable-bots-v3-ai-node': true,
    })
    expect(payload.settings.flags['enable-bots-v3-ai-node']).toBe(true)

    expect(opts).toEqual({ mergeJson: ['plan', 'settings'] })

    expect(planAiHistoryCreateMock).toHaveBeenCalledTimes(1)
    expect(planAiHistoryCreateMock).toHaveBeenCalledWith({
      accountId,
      activity: 'change',
      name: 'IA-PACK-PRO',
      magicText: true,
      summary: true,
      transcription: true,
      copilot: false,
      csat: true,
      agent: true,
    })

    const expectedIns = [
      { serviceType: 'summary', amount: 2 },
      { serviceType: 'transcription', amount: 4 },
    ]
    for (const { serviceType, amount } of expectedIns) {
      expect(creditMovementCreateMock).toHaveBeenCalledWith({
        type: 'in',
        serviceType,
        origin: 'agnus',
        amount,
        accountId,
      })
    }
    const allIns = creditMovementCreateMock.mock.calls.map((c) => c[0].serviceType).sort()
    expect(allIns).toEqual(['summary', 'transcription'])
  })

  it('does not call getRenewDate when renewDate is explicitly provided; does not log history if products is falsy', async () => {
    const accountId = 'acc-2'
    findByIdMock.mockResolvedValue({
      id: accountId,
      agnusSignatureKey: 'sig-xyz',
      plan: {
        ai: { summary: 0, 'magic-text': 0, transcription: 0, copilot: 0, csat: 0, agent: 0 },
        renewDate: undefined,
      },
      settings: { flags: {} },
    })
    updateByIdMock.mockResolvedValue(undefined)

    const getRenewDate = jest.spyOn(accountResource, 'getRenewDate')

    await accountResource.updateIaProducts(
      accountId,
      undefined as any,
      { summary: 1, 'magic-text': 0, transcription: 0, copilot: 0, csat: 0, agent: 0 },
      '2025-03-01',
    )

    expect(getRenewDate).not.toHaveBeenCalled()

    const [, payload, opts] = updateByIdMock.mock.calls[0]
    expect(payload.plan).toEqual({
      ai: { summary: 1, 'magic-text': 0, transcription: 0, copilot: 0, csat: 0, agent: 0 },
      renewDate: '2025-03-01',
    })
    expect(opts).toEqual({ mergeJson: ['plan', 'settings'] })

    expect(payload.settings.flags).toMatchObject({ 'enable-smart-summary': true })
    expect(payload.settings.flags['enable-audio-transcription']).toBeFalsy()
    expect(payload.settings.flags['enable-magic-text']).toBeFalsy()
    expect(payload.settings.flags['enable-copilot']).toBeFalsy()
    expect(payload.settings.flags['enable-smart-csat-score']).toBeFalsy()
    expect(payload.settings.flags['enable-bots-v3-ai-node']).toBeFalsy()

    expect(planAiHistoryCreateMock).not.toHaveBeenCalled()

    expect(creditMovementCreateMock).toHaveBeenCalledTimes(1)
    expect(creditMovementCreateMock).toHaveBeenCalledWith({
      type: 'in',
      serviceType: 'summary',
      origin: 'agnus',
      amount: 1,
      accountId,
    })
  })
})

describe('AccountResource.getHttpClient', () => {
  let accountResource: any
  let containerGetMock: jest.Mock
  let httpClientInstance: any

  const loadSUT = () => {
    jest.isolateModules(() => {
      jest.resetModules()

      const baseResourcePath = require.resolve('../../../../core/resources/BaseResource')
      const httpClientPath = require.resolve('../../../../core/services/httpClient/HttpClient')

      jest.doMock(httpClientPath, () => {
        class HttpClientToken {}
        return { __esModule: true, HttpClient: HttpClientToken }
      })

      httpClientInstance = { get: jest.fn(), post: jest.fn() }

      containerGetMock = jest.fn((service) => {
        const { HttpClient } = require(httpClientPath)
        if (service === HttpClient) return httpClientInstance
        return {}
      })
      jest.doMock('typedi', () => ({
        __esModule: true,
        Container: { get: containerGetMock },
        default: { get: containerGetMock },
      }))

      jest.doMock(baseResourcePath, () => ({
        __esModule: true,
        default: class {},
      }))

      const mod = require('../../../../core/resources/accountResource')
      accountResource = mod.default || mod
    })
  }

  beforeEach(() => {
    jest.clearAllMocks()
    loadSUT()
  })

  it('calls Container.get with the HttpClient token and returns the same instance', () => {
    const http = accountResource.getHttpClient()

    const httpClientPath = require.resolve('../../../../core/services/httpClient/HttpClient')
    const { HttpClient } = require(httpClientPath)
    expect(containerGetMock).toHaveBeenCalledTimes(1)
    expect(containerGetMock).toHaveBeenCalledWith(HttpClient)

    expect(http).toBe(httpClientInstance)
  })

  it('reuses the same instance (Container controls it)', () => {
    const a = accountResource.getHttpClient()
    const b = accountResource.getHttpClient()

    expect(containerGetMock).toHaveBeenCalledTimes(2)
    expect(a).toBe(httpClientInstance)
    expect(b).toBe(httpClientInstance)
  })
})

describe('AccountResource.getAccountAmounts', () => {
  let accountResource: any

  let containerGetMock: jest.Mock
  let creditsSvcInstance: {
    isCreditsControlEnabled: jest.Mock
    getCredits: jest.Mock
  }
  let serviceGetServicesMock: jest.Mock
  let userGetUsersMock: jest.Mock
  let getAccountPlanSpy: jest.SpyInstance

  const loadSUT = () => {
    jest.isolateModules(() => {
      jest.resetModules()

      const baseResourcePath = require.resolve('../../../../core/resources/BaseResource')
      const typediPath = require.resolve('typedi')
      const serviceResourcePath = require.resolve('../../../../core/resources/serviceResource')
      const userResourcePath = require.resolve('../../../../core/resources/userResource')

      jest.doMock(baseResourcePath, () => ({
        __esModule: true,
        default: class {},
      }))

      creditsSvcInstance = {
        isCreditsControlEnabled: jest.fn(),
        getCredits: jest.fn(),
      }

      containerGetMock = jest.fn(() => creditsSvcInstance)
      jest.doMock(typediPath, () => ({
        __esModule: true,
        Container: { get: containerGetMock },
        default: { get: containerGetMock },
      }))

      serviceGetServicesMock = jest.fn()
      userGetUsersMock = jest.fn()
      jest.doMock(serviceResourcePath, () => ({
        __esModule: true,
        default: { getServices: serviceGetServicesMock },
      }))
      jest.doMock(userResourcePath, () => ({
        __esModule: true,
        default: { getUsers: userGetUsersMock },
      }))

      const mod = require('../../../../core/resources/accountResource')
      accountResource = mod.default || mod
    })
  }

  beforeEach(() => {
    jest.clearAllMocks()
    loadSUT()
  })

  const setupGetAccountPlan = (value: any) => {
    getAccountPlanSpy = jest.spyOn(accountResource, 'getAccountPlan').mockResolvedValue(value)
  }

  it('throws BadRequest when the account does not exist', async () => {
    setupGetAccountPlan(null)

    await expect(accountResource.getAccountAmounts('acc-missing', [])).rejects.toThrow(
      'Account not found for: acc-missing',
    )

    expect(containerGetMock).not.toHaveBeenCalled()
    expect(serviceGetServicesMock).not.toHaveBeenCalled()
    expect(userGetUsersMock).not.toHaveBeenCalled()
  })

  it('throws BadRequest when credits control is disabled', async () => {
    setupGetAccountPlan({ id: 'acc-1', plan: {}, creditsControl: {} })

    creditsSvcInstance.isCreditsControlEnabled.mockResolvedValue(false)

    await expect(accountResource.getAccountAmounts('acc-1', [])).rejects.toThrow(
      'Account credits control is disabled for: acc-1',
    )

    expect(containerGetMock).toHaveBeenCalledTimes(1)
    expect(creditsSvcInstance.isCreditsControlEnabled).toHaveBeenCalledWith('acc-1')
    expect(serviceGetServicesMock).not.toHaveBeenCalled()
    expect(userGetUsersMock).not.toHaveBeenCalled()
  })

  it('happy path without filter: calls all three sources with isRequired=true and returns merged result', async () => {
    const account = { id: 'acc-2', plan: {}, creditsControl: { enabled: true } }
    setupGetAccountPlan(account)

    creditsSvcInstance.isCreditsControlEnabled.mockResolvedValue(true)
    creditsSvcInstance.getCredits.mockResolvedValue({ creditsInfo: { balance: 123 } })
    serviceGetServicesMock.mockResolvedValue({ servicesInfo: { total: 7 } })
    userGetUsersMock.mockResolvedValue({ usersInfo: { total: 3 } })

    const result = await accountResource.getAccountAmounts('acc-2', /* filter */ [])

    const creditsSvcPath = require.resolve('../../../../core/services/creditMovement/CreditsControlCacheService')
    expect(containerGetMock).toHaveBeenCalledTimes(1)

    expect(creditsSvcInstance.getCredits).toHaveBeenCalledWith(account, true)
    expect(serviceGetServicesMock).toHaveBeenCalledWith(account, true)
    expect(userGetUsersMock).toHaveBeenCalledWith(account, true)

    expect(result).toEqual({
      accountId: 'acc-2',
      creditsInfo: { balance: 123 },
      servicesInfo: { total: 7 },
      usersInfo: { total: 3 },
    })
  })

  it('respects filter and blockedFilter: only calls with true when allowed', async () => {
    const account = { id: 'acc-3', plan: {}, creditsControl: { enabled: true } }
    setupGetAccountPlan(account)

    creditsSvcInstance.isCreditsControlEnabled.mockResolvedValue(true)

    creditsSvcInstance.getCredits.mockResolvedValue({ A: 1 })
    serviceGetServicesMock.mockResolvedValue({ B: 2 })
    userGetUsersMock.mockResolvedValue({ C: 3 })

    const filter = ['services.amounts']
    const blocked = ['users.amounts']

    const result = await accountResource.getAccountAmounts('acc-3', filter, blocked)

    expect(creditsSvcInstance.getCredits).toHaveBeenCalledWith(account, false)
    expect(serviceGetServicesMock).toHaveBeenCalledWith(account, true)
    expect(userGetUsersMock).toHaveBeenCalledWith(account, false)

    expect(result).toEqual({
      accountId: 'acc-3',
      A: 1,
      B: 2,
      C: 3,
    })
  })
})

describe('AccountResource.update', () => {
  let accountResource: any

  let superUpdateMock: jest.Mock
  let bulkGenerateTokenMock: jest.Mock
  let setExpirationPwManyUsersMock: jest.Mock

  const FIXED_NOW = new Date('2025-01-15T10:00:00Z')

  const baseModel = () => ({
    id: 'acc-1',
    isActive: true,
    expiresAt: new Date('2025-01-10T00:00:00Z'),
    settings: {
      twoFactorAuthMandatory: false,
      twoFactorAuthMandatorySchedule: new Date('2025-01-20T00:00:00Z'),
      flags: {},
      isPasswordExpirationActive: false,
    },
    plan: {
      isActive: true,
      emailNotifications: {
        expiredNotificationSent: true,
        expirationNotificationSent: true,
      },
      ai: {},
    },
  })

  const loadSUT = () => {
    jest.isolateModules(() => {
      jest.resetModules()
      jest.useFakeTimers().setSystemTime(FIXED_NOW)

      const baseResourcePath = require.resolve('../../../../core/resources/BaseResource')
      const userResourcePath = require.resolve('../../../../core/resources/userResource')
      const configPath = require.resolve('../../../../core/config')

      bulkGenerateTokenMock = jest.fn().mockResolvedValue(undefined)
      setExpirationPwManyUsersMock = jest.fn().mockResolvedValue(undefined)
      jest.doMock(userResourcePath, () => ({
        __esModule: true,
        default: {
          bulkGenerateTokenFromInternalChat: bulkGenerateTokenMock,
          setExpirationPasswordManyUsers: setExpirationPwManyUsersMock,
        },
      }))

      jest.doMock(configPath, () => {
        const fn = (key: string) => (key === 'gracePeriodExtendTimes' ? 3 : undefined)
        return { __esModule: true, default: fn }
      })

      superUpdateMock = jest.fn().mockImplementation((model, payload) => {
        const merged = {
          ...model,
          ...payload,
          settings: { ...model.settings, ...(payload?.settings || {}) },
          plan: { ...model.plan, ...(payload?.plan || {}) },
        }
        return Promise.resolve(merged)
      })
      jest.doMock(baseResourcePath, () => ({
        __esModule: true,
        default: class {
          update(model: any, payload: any, _opts?: any) {
            return superUpdateMock(model, payload, _opts)
          }
        },
      }))

      const mod = require('../../../../core/resources/accountResource')
      accountResource = mod.default || mod
    })
  }

  beforeEach(() => {
    jest.clearAllMocks()
    loadSUT()
  })

  afterEach(() => {
    jest.useRealTimers()
  })

  it('resets emailNotifications when expiresAt changes (in the return)', async () => {
    const model = baseModel()
    const newDate = new Date('2025-02-01T00:00:00Z')

    const updated = await accountResource.update(model, { expiresAt: newDate, plan: {} })

    expect(updated.plan.emailNotifications).toEqual({
      expiredNotificationSent: false,
      expirationNotificationSent: false,
    })
  })

  it('when MFA changes to FALSE, calls sendEmailDisable', async () => {
    const model = { ...baseModel(), settings: { ...baseModel().settings, twoFactorAuthMandatory: true } }
    const spyDisable = jest.spyOn(accountResource, 'sendEmailDisable').mockResolvedValue(undefined)

    await accountResource.update(model, { settings: { twoFactorAuthMandatory: false } })

    expect(spyDisable).toHaveBeenCalledWith(model.id)
  })

  it('when MFA changes to TRUE, calls expiresUserAccessForTwoFactorAuth with notify=true', async () => {
    const model = baseModel()
    const spyExpire = jest.spyOn(accountResource, 'expiresUserAccessForTwoFactorAuth').mockResolvedValue(undefined)

    const ttl = new Date('2025-01-18T00:00:00Z')
    const updated = await accountResource.update(model, {
      settings: { twoFactorAuthMandatory: true, twoFactorAuthMandatorySchedule: ttl },
    })

    expect(spyExpire).toHaveBeenCalledWith(updated, ttl, true)
  })

  it('enable-smart-csat-score transition adds smartCsatScoreEnabledAt (in the return)', async () => {
    const model = baseModel()
    const updated = await accountResource.update(model, {
      settings: { flags: { 'enable-smart-csat-score': true } },
    })

    expect(updated.settings.smartCsatScoreEnabledAt).toBeInstanceOf(Date)
  })

  it('numeric plan.services becomes plan.services.whatsapp (in the return)', async () => {
    const model = baseModel()

    const updated = await accountResource.update(model, { plan: { services: 12 } })

    expect(updated.plan.services.whatsapp).toBe(12)
  })

  it('deactivation: adds grace period fields when data.isActive=false & data.plan.isActive=false & model.isActive=true', async () => {
    const model = baseModel()

    const updated = await accountResource.update(model, {
      isActive: false,
      plan: { isActive: false },
    })

    expect(updated.plan.isOnGracePeriod).toBe(true)
    expect(updated.plan.isGracePeriodOnHold).toBe(true)
    expect(updated.plan.gracePeriodExtendTimesRemaining).toBe(3)
    expect(updated.plan.gracePeriodEndsAt).toBeInstanceOf(Date)
  })

  it('internal-chat enabled triggers bulkGenerateTokenFromInternalChat', async () => {
    const model = baseModel()

    await accountResource.update(model, { settings: { flags: { 'internal-chat': true } } })

    expect(bulkGenerateTokenMock).toHaveBeenCalledWith(model.id)
  })

  it('isPasswordExpirationActive true calls setExpirationPasswordManyUsers', async () => {
    const model = baseModel()

    const updated = await accountResource.update(model, { settings: { isPasswordExpirationActive: true } })

    expect(setExpirationPwManyUsersMock).toHaveBeenCalledWith(expect.objectContaining({ id: updated.id }), {
      setExpirationToAllUsers: false,
    })
  })

  it('when account remains active, sent grace period fields are omitted from the return', async () => {
    const model = { ...baseModel(), isActive: true, plan: { ...baseModel().plan, isActive: true } }

    const updated = await accountResource.update(model, {
      plan: {
        isOnGracePeriod: true,
        isGracePeriodOnHold: true,
        gracePeriodExtendTimesRemaining: 9,
        gracePeriodEndsAt: new Date(),
      },
    })

    expect(updated.plan.isOnGracePeriod).toBeUndefined()
    expect(updated.plan.isGracePeriodOnHold).toBeUndefined()
    expect(updated.plan.gracePeriodExtendTimesRemaining).toBeUndefined()
    expect(updated.plan.gracePeriodEndsAt).toBeUndefined()
  })

  it('returns null if super.update does not return account', async () => {
    const model = baseModel()

    superUpdateMock.mockResolvedValueOnce(null)

    const result = await accountResource.update(model, { settings: {} })

    expect(result).toBeNull()
  })
})

describe('AccountResource.destroy', () => {
  let accountResource: any

  let txSpy: jest.Mock
  let superDestroyMock: jest.Mock

  let roleDestroyManyMock: jest.Mock
  let userDestroyManyMock: jest.Mock
  let serviceDestroyManyMock: jest.Mock
  let tagDestroyManyMock: jest.Mock
  let subscriptionDestroyManyMock: jest.Mock

  const account = { id: 'acc-1' }
  const extraOptions = { foo: 123 }

  const loadSUT = () => {
    jest.isolateModules(() => {
      jest.resetModules()

      const baseResourcePath = require.resolve('../../../../core/resources/BaseResource')
      const roleResPath = require.resolve('../../../../core/resources/roleResource')
      const userResPath = require.resolve('../../../../core/resources/userResource')
      const serviceResPath = require.resolve('../../../../core/resources/serviceResource')
      const tagResPath = require.resolve('../../../../core/resources/tagResource')
      const subResPath = require.resolve('../../../../core/resources/subscriptionResource')

      jest.unmock(baseResourcePath)
      jest.unmock(roleResPath)
      jest.unmock(userResPath)
      jest.unmock(serviceResPath)
      jest.unmock(tagResPath)
      jest.unmock(subResPath)

      roleDestroyManyMock = jest.fn().mockResolvedValue(undefined)
      userDestroyManyMock = jest.fn().mockResolvedValue(undefined)
      serviceDestroyManyMock = jest.fn().mockResolvedValue(undefined)
      tagDestroyManyMock = jest.fn().mockResolvedValue(undefined)
      subscriptionDestroyManyMock = jest.fn().mockResolvedValue(undefined)

      jest.doMock(roleResPath, () => ({ __esModule: true, default: { destroyMany: roleDestroyManyMock } }))
      jest.doMock(userResPath, () => ({ __esModule: true, default: { destroyMany: userDestroyManyMock } }))
      jest.doMock(serviceResPath, () => ({ __esModule: true, default: { destroyMany: serviceDestroyManyMock } }))
      jest.doMock(tagResPath, () => ({ __esModule: true, default: { destroyMany: tagDestroyManyMock } }))
      jest.doMock(subResPath, () => ({ __esModule: true, default: { destroyMany: subscriptionDestroyManyMock } }))

      const txObj = { id: 'tx-1' }
      txSpy = jest.fn(async (cb: any) => cb(txObj))
      superDestroyMock = jest.fn().mockResolvedValue('destroyed-ok')

      jest.doMock(baseResourcePath, () => ({
        __esModule: true,
        default: class {
          getRepository() {
            return { transaction: txSpy }
          }
          destroy(account: any, options: any) {
            return superDestroyMock(account, options)
          }
        },
      }))

      const mod = require('../../../../core/resources/accountResource')
      accountResource = mod.default || mod

      accountResource.getRepository = () => ({ transaction: txSpy })
    })
  }

  beforeEach(() => {
    jest.clearAllMocks()
    loadSUT()
  })

  it('executes cleanup in transaction and calls super.destroy with the same transaction', async () => {
    const result = await accountResource.destroy(account, extraOptions)

    expect(txSpy).toHaveBeenCalledTimes(1)

    const check = (mock: jest.Mock) => {
      expect(mock).toHaveBeenCalledTimes(1)
      const [whereArg, optsArg] = mock.mock.calls[0]
      expect(whereArg).toEqual({ where: { accountId: 'acc-1' } })
      expect(optsArg).toMatchObject({ foo: 123, transaction: { id: 'tx-1' } })
    }

    ;[
      roleDestroyManyMock,
      userDestroyManyMock,
      serviceDestroyManyMock,
      tagDestroyManyMock,
      subscriptionDestroyManyMock,
    ].forEach(check)

    expect(superDestroyMock).toHaveBeenCalledTimes(1)
    const [accArg, optsArg] = superDestroyMock.mock.calls[0]
    expect(accArg).toBe(account)
    expect(optsArg).toMatchObject({ foo: 123, transaction: { id: 'tx-1' } })

    expect(result).toBe('destroyed-ok')
  })

  it('propagates error if any destroyMany fails and does not call super.destroy', async () => {
    serviceDestroyManyMock.mockRejectedValueOnce(new Error('boom'))

    await expect(accountResource.destroy(account, extraOptions)).rejects.toThrow('boom')

    expect(superDestroyMock).not.toHaveBeenCalled()
    expect(txSpy).toHaveBeenCalledTimes(1)
  })

  it('returns null when super.destroy returns null (covers the !updatedAccount branch)', async () => {
    roleDestroyManyMock.mockResolvedValueOnce(undefined)
    userDestroyManyMock.mockResolvedValueOnce(undefined)
    serviceDestroyManyMock.mockResolvedValueOnce(undefined)
    tagDestroyManyMock.mockResolvedValueOnce(undefined)
    subscriptionDestroyManyMock.mockResolvedValueOnce(undefined)

    superDestroyMock.mockResolvedValueOnce(null)

    const res = await accountResource.destroy(account, extraOptions)
    expect(res).toBeNull()
    expect(txSpy).toHaveBeenCalledTimes(1)
  })
})

describe('AccountResource.accountTrialNotFirstAccess', () => {
  let accountResource: any
  let findManyMock: jest.Mock

  const FIXED_NOW = new Date('2025-01-20T15:00:00Z')

  const loadSUT = () => {
    jest.isolateModules(() => {
      jest.useFakeTimers().setSystemTime(FIXED_NOW)

      const whereImpl = (...args: any[]) => ({ __type: 'where', args })
      const literalImpl = (val: string) => ({ __type: 'literal', val })
      const OpObj = { lt: Symbol('lt'), ne: Symbol('ne'), eq: Symbol('eq') }

      jest.doMock('sequelize', () => ({
        __esModule: true,
        default: { where: whereImpl, literal: literalImpl },
        Op: OpObj,
      }))

      findManyMock = jest.fn().mockResolvedValue([{ id: 'acc-trial-1' }])
      jest.doMock('../../../../core/resources/BaseResource', () => ({
        __esModule: true,
        default: class {
          getRepository() {
            return { findMany: findManyMock }
          }
        },
      }))

      const mod = require('../../../../core/resources/accountResource')
      accountResource = mod.default || mod
    })
  }

  beforeEach(() => {
    jest.resetModules()
    jest.clearAllMocks()
    loadSUT()
  })

  afterEach(() => {
    jest.useRealTimers()
  })

  it('builds the query correctly (trial, wizard started, >1 day, name != MandeUmZap, include users first login) and returns the result', async () => {
    const { Op } = require('sequelize') as { Op: any }
    const expectedDate = new Date(FIXED_NOW.getTime() - 1 * 24 * 60 * 60 * 1000)

    const result = await accountResource.accountTrialNotFirstAccess()

    expect(result).toEqual([{ id: 'acc-trial-1' }])

    expect(findManyMock).toHaveBeenCalledTimes(1)
    const [query] = findManyMock.mock.calls[0]

    expect(query.attributes).toEqual(['id', 'name', 'settings', 'agnusSignatureKey', 'createdAt'])

    expect(Array.isArray(query.where.$and)).toBe(true)
    const and0 = query.where.$and[0]
    expect(and0?.__type).toBe('where')
    expect(and0.args?.[0]?.__type).toBe('literal')
    expect(and0.args?.[0]?.val).toBe(`plan->'isTrial'`)
    expect(and0.args?.[1]).toBe('true')

    expect(query.where.wizardProgress[Op.eq]).toBe('started')

    expect(query.where.createdAt[Op.lt]).toBeInstanceOf(Date)
    expect(query.where.createdAt[Op.lt].getTime()).toBe(expectedDate.getTime())

    expect(query.where.name[Op.ne]).toBe('MandeUmZap')

    expect(query.include).toEqual({
      attributes: [],
      model: 'users',
      where: { isFirstLogin: true },
    })
  })
})

describe('AccountResource.accountTrialNotCompletWizard', () => {
  let accountResource: any
  let findManyMock: jest.Mock

  const FIXED_NOW = new Date('2025-01-20T15:00:00Z')

  const loadSUT = () => {
    jest.isolateModules(() => {
      jest.useFakeTimers().setSystemTime(FIXED_NOW)

      const whereImpl = (...args: any[]) => ({ __type: 'where', args })
      const literalImpl = (val: string) => ({ __type: 'literal', val })
      const OpObj = { lt: Symbol('lt'), ne: Symbol('ne') }

      jest.doMock('sequelize', () => ({
        __esModule: true,
        default: { where: whereImpl, literal: literalImpl },
        Op: OpObj,
      }))

      findManyMock = jest.fn().mockResolvedValue([{ id: 'acc-trial-not-finished-1' }])
      jest.doMock('../../../../core/resources/BaseResource', () => ({
        __esModule: true,
        default: class {
          getRepository() {
            return { findMany: findManyMock }
          }
        },
      }))

      const mod = require('../../../../core/resources/accountResource')
      accountResource = mod.default || mod
    })
  }

  beforeEach(() => {
    jest.resetModules()
    jest.clearAllMocks()
    loadSUT()
  })

  afterEach(() => {
    jest.useRealTimers()
  })

  it('builds the query correctly (trial, wizard != finished, >1 day, name != MandeUmZap, include users isFirstLogin=false) and returns the result', async () => {
    const { Op } = require('sequelize') as { Op: any }
    const expectedDate = new Date(FIXED_NOW.getTime() - 1 * 24 * 60 * 60 * 1000)

    const result = await accountResource.accountTrialNotCompletWizard()

    expect(result).toEqual([{ id: 'acc-trial-not-finished-1' }])

    expect(findManyMock).toHaveBeenCalledTimes(1)
    const [query] = findManyMock.mock.calls[0]

    expect(query.attributes).toEqual(['id', 'name', 'settings', 'agnusSignatureKey', 'createdAt'])

    expect(query.where.wizardProgress[Op.ne]).toBe('finished')

    expect(Array.isArray(query.where.$and)).toBe(true)
    const and0 = query.where.$and[0]
    expect(and0?.__type).toBe('where')
    expect(and0.args?.[0]?.__type).toBe('literal')
    expect(and0.args?.[0]?.val).toBe(`plan->'isTrial'`)
    expect(and0.args?.[1]).toBe('true')

    expect(query.where.createdAt[Op.lt]).toBeInstanceOf(Date)
    expect(query.where.createdAt[Op.lt].getTime()).toBe(expectedDate.getTime())

    expect(query.where.name[Op.ne]).toBe('MandeUmZap')

    expect(query.include).toEqual({
      attributes: [],
      model: 'users',
      where: { isFirstLogin: false },
    })
  })
})

describe('AccountResource.accountTrialNotAccess', () => {
  let accountResource: any
  let findManyMock: jest.Mock

  const FIXED_NOW = new Date('2025-01-20T15:00:00Z')

  const loadSUT = () => {
    jest.isolateModules(() => {
      jest.useFakeTimers().setSystemTime(FIXED_NOW)

      const whereImpl = (...args: any[]) => ({ __type: 'where', args })
      const literalImpl = (val: string) => ({ __type: 'literal', val })
      const OpObj = { lt: Symbol('lt'), ne: Symbol('ne'), eq: Symbol('eq') }

      jest.doMock('sequelize', () => ({
        __esModule: true,
        default: { where: whereImpl, literal: literalImpl },
        Op: OpObj,
      }))

      findManyMock = jest.fn().mockResolvedValue([{ id: 'acc-trial-finished-no-access-1' }])
      jest.doMock('../../../../core/resources/BaseResource', () => ({
        __esModule: true,
        default: class {
          getRepository() {
            return { findMany: findManyMock }
          }
        },
      }))

      const mod = require('../../../../core/resources/accountResource')
      accountResource = mod.default || mod
    })
  }

  beforeEach(() => {
    jest.resetModules()
    jest.clearAllMocks()
    loadSUT()
  })

  afterEach(() => {
    jest.useRealTimers()
  })

  it('builds the query correctly (trial, wizard finished, >1 day, name != MandeUmZap, group + include messages with 1 day having) and returns the result', async () => {
    const { Op } = require('sequelize') as { Op: any }
    const expectedDate = new Date(FIXED_NOW.getTime() - 1 * 24 * 60 * 60 * 1000)

    const result = await accountResource.accountTrialNotAccess()

    expect(result).toEqual([{ id: 'acc-trial-finished-no-access-1' }])

    expect(findManyMock).toHaveBeenCalledTimes(1)
    const [query] = findManyMock.mock.calls[0]

    expect(query.attributes).toEqual(['id', 'name', 'settings', 'agnusSignatureKey', 'createdAt'])

    expect(Array.isArray(query.where.$and)).toBe(true)
    const and0 = query.where.$and[0]
    expect(and0?.__type).toBe('where')
    expect(and0.args?.[0]?.__type).toBe('literal')
    expect(and0.args?.[0]?.val).toBe(`plan->'isTrial'`)
    expect(and0.args?.[1]).toBe('true')

    expect(query.where.wizardProgress[Op.eq]).toBe('finished')

    expect(query.where.createdAt[Op.lt]).toBeInstanceOf(Date)
    expect(query.where.createdAt[Op.lt].getTime()).toBe(expectedDate.getTime())

    expect(query.where.name[Op.ne]).toBe('MandeUmZap')

    expect(query.group).toBe('Account.id')

    expect(query.include).toMatchObject({
      attributes: [],
      model: 'messages',
      group: 'accountId',
    })
    expect(query.include.having?.__type).toBe('literal')
    expect(query.include.having?.val).toBe(
      `max(messages."createdAt") >= now() + interval '1' day or MAX(messages."createdAt") is null`,
    )
  })
})

describe('AccountResource.incrementUsedHsm', () => {
  let accountResource: any
  let superUpdateMock: jest.Mock

  const loadSUT = () => {
    jest.isolateModules(() => {
      jest.resetModules()

      const baseResourcePath = require.resolve('../../../../core/resources/BaseResource')

      superUpdateMock = jest.fn().mockImplementation(async (model, payload, _opts) => ({
        ...model,
        plan: { ...model.plan, ...(payload?.plan || {}) },
      }))

      jest.doMock(baseResourcePath, () => ({
        __esModule: true,
        default: class {
          update(model: any, payload: any, opts?: any) {
            return superUpdateMock(model, payload, opts)
          }
        },
      }))

      const mod = require('../../../../core/resources/accountResource')
      accountResource = mod.default || mod
    })
  }

  beforeEach(() => {
    jest.clearAllMocks()
    loadSUT()
  })

  it('returns null and does not call super.update when "disable-hsm-limit" flag is active', async () => {
    const account = {
      id: 'acc-1',
      plan: { hsmUsedLimit: 5 },
      settings: { flags: { 'disable-hsm-limit': true } },
    }

    const res = await accountResource.incrementUsedHsm(account)

    expect(res).toBeNull()
    expect(superUpdateMock).not.toHaveBeenCalled()
  })

  it('increments hsmUsedLimit by 1 and calls super.update with mergeJson=["plan"] and dontEmit=true', async () => {
    const account = {
      id: 'acc-2',
      plan: { hsmUsedLimit: 5 },
      settings: {
        flags: {},
      },
    }

    const updated = await accountResource.incrementUsedHsm(account)

    expect(superUpdateMock).toHaveBeenCalledTimes(1)
    const [modelArg, payloadArg, optsArg] = superUpdateMock.mock.calls[0]
    expect(modelArg).toBe(account)
    expect(payloadArg).toEqual({ plan: { hsmUsedLimit: 6 } })
    expect(optsArg).toEqual({ mergeJson: ['plan'], dontEmit: true })

    expect(updated.plan.hsmUsedLimit).toBe(6)
  })
})
describe('AccountResource.decrementUsedHsm', () => {
  let accountResource: any
  let superUpdateMock: jest.Mock

  const loadSUT = () => {
    jest.isolateModules(() => {
      jest.resetModules()

      const baseResourcePath = require.resolve('../../../../core/resources/BaseResource')

      superUpdateMock = jest.fn().mockImplementation(async (model, payload, _opts) => ({
        ...model,
        plan: { ...model.plan, ...(payload?.plan || {}) },
      }))

      jest.doMock(baseResourcePath, () => ({
        __esModule: true,
        default: class {
          update(model: any, payload: any, opts?: any) {
            return superUpdateMock(model, payload, opts)
          }
        },
      }))

      const mod = require('../../../../core/resources/accountResource')
      accountResource = mod.default || mod
    })
  }

  beforeEach(() => {
    jest.clearAllMocks()
    loadSUT()
  })

  it('returns null and does not call super.update when "disable-hsm-limit" flag is active', async () => {
    const account = {
      id: 'acc-1',
      plan: { hsmUsedLimit: 8 },
      settings: { flags: { 'disable-hsm-limit': true } },
    }

    const res = await accountResource.decrementUsedHsm(account)

    expect(res).toBeNull()
    expect(superUpdateMock).not.toHaveBeenCalled()
  })

  it('decrements hsmUsedLimit by 1 and calls super.update with mergeJson=["plan"] and dontEmit=true', async () => {
    const account = {
      id: 'acc-2',
      plan: { hsmUsedLimit: 8 },
      settings: { flags: {} },
    }

    const updated = await accountResource.decrementUsedHsm(account)

    expect(superUpdateMock).toHaveBeenCalledTimes(1)
    const [modelArg, payloadArg, optsArg] = superUpdateMock.mock.calls[0]
    expect(modelArg).toBe(account)
    expect(payloadArg).toEqual({ plan: { hsmUsedLimit: 7 } })
    expect(optsArg).toEqual({ mergeJson: ['plan'], dontEmit: true })

    expect(updated.plan.hsmUsedLimit).toBe(7)
  })
})

describe('AccountResource.resetHsmLimit', () => {
  let accountResource: any
  let superUpdateMock: jest.Mock

  const loadSUT = () => {
    jest.isolateModules(() => {
      jest.resetModules()

      const baseResourcePath = require.resolve('../../../../core/resources/BaseResource')

      superUpdateMock = jest.fn().mockImplementation(async (model, payload, _opts) => ({
        ...model,
        plan: { ...model.plan, ...(payload?.plan || {}) },
      }))

      jest.doMock(baseResourcePath, () => ({
        __esModule: true,
        default: class {
          update(model: any, payload: any, opts?: any) {
            return superUpdateMock(model, payload, opts)
          }
        },
      }))

      const mod = require('../../../../core/resources/accountResource')
      accountResource = mod.default || mod
    })
  }

  beforeEach(() => {
    jest.clearAllMocks()
    loadSUT()
  })

  it('returns null and does not call super.update when "disable-hsm-limit" flag is active', async () => {
    const account = {
      id: 'acc-3',
      plan: { hsmUsedLimit: 12 },
      settings: { flags: { 'disable-hsm-limit': true } },
    }

    const res = await accountResource.resetHsmLimit(account)

    expect(res).toBeNull()
    expect(superUpdateMock).not.toHaveBeenCalled()
  })

  it('resets hsmUsedLimit to 0 and calls super.update with mergeJson=["plan"]', async () => {
    const account = {
      id: 'acc-4',
      plan: { hsmUsedLimit: 12 },
      settings: { flags: {} },
    }

    const updated = await accountResource.resetHsmLimit(account)

    expect(superUpdateMock).toHaveBeenCalledTimes(1)
    const [modelArg, payloadArg, optsArg] = superUpdateMock.mock.calls[0]
    expect(modelArg).toBe(account)
    expect(payloadArg).toEqual({ plan: { hsmUsedLimit: 0 } })
    expect(optsArg).toEqual({ mergeJson: ['plan'] })

    expect(updated.plan.hsmUsedLimit).toBe(0)
  })
})

describe('AccountResource.setDataBySignatureId', () => {
  let accountResource: any
  let repoUpdateMock: jest.Mock

  const loadSUT = () => {
    jest.isolateModules(() => {
      jest.resetModules()

      const baseResourcePath = require.resolve('../../../../core/resources/BaseResource')

      repoUpdateMock = jest.fn().mockResolvedValue({ ok: true, kind: 'updated' })
      jest.doMock(baseResourcePath, () => ({
        __esModule: true,
        default: class {
          getRepository() {
            return { update: repoUpdateMock }
          }
        },
      }))

      const mod = require('../../../../core/resources/accountResource')
      accountResource = mod.default || mod
    })
  }

  beforeEach(() => {
    jest.clearAllMocks()
    loadSUT()
  })

  it('calls getRepository().update with data.term payload (terms and nextCheckDate) and mergeJson=["data"]', async () => {
    const account = {
      id: 'acc-1',
      data: {
        term: {
          terms: ['t1', 't2'],
          nextCheckDate: new Date('2025-02-01T00:00:00Z'),
        },
      },
    }

    const result = await accountResource.setDataBySignatureId(account)

    expect(repoUpdateMock).toHaveBeenCalledTimes(1)
    const [accArg, payloadArg, optsArg] = repoUpdateMock.mock.calls[0]

    expect(accArg).toBe(account)

    expect(payloadArg).toEqual({
      data: {
        term: {
          terms: ['t1', 't2'],
          nextCheckDate: new Date('2025-02-01T00:00:00Z'),
        },
      },
    })

    expect(optsArg).toEqual({ mergeJson: ['data'] })

    expect(result).toEqual({ ok: true, kind: 'updated' })
  })

  it('works even if terms/nextCheckDate are undefined (pass-through)', async () => {
    const account = {
      id: 'acc-2',
      data: { term: { terms: undefined, nextCheckDate: undefined } },
    }

    await accountResource.setDataBySignatureId(account)

    const [, payloadArg, optsArg] = repoUpdateMock.mock.calls[0]
    expect(payloadArg).toEqual({
      data: { term: { terms: undefined, nextCheckDate: undefined } },
    })
    expect(optsArg).toEqual({ mergeJson: ['data'] })
  })
})

describe('AccountResource.getAgnusData', () => {
  let accountResource: any

  let agnusGetSignatureMock: jest.Mock
  let agnusGetContractMock: jest.Mock
  let reportErrorMock: jest.Mock

  const FIXED_NOW = new Date('2025-01-20T15:00:00Z')

  const loadSUT = () => {
    jest.isolateModules(() => {
      jest.resetModules()
      jest.useFakeTimers().setSystemTime(FIXED_NOW)

      const baseResourcePath = require.resolve('../../../../core/resources/BaseResource')
      const agnusApiPath = require.resolve('../../../../core/services/agnus/AgnusApi')
      const reportErrorPath = require.resolve('../../../../core/services/logs/reportError')

      jest.doMock(baseResourcePath, () => ({
        __esModule: true,
        default: class {},
      }))

      reportErrorMock = jest.fn()
      jest.doMock(reportErrorPath, () => ({
        __esModule: true,
        default: reportErrorMock,
      }))

      agnusGetSignatureMock = jest.fn()
      agnusGetContractMock = jest.fn()
      class FakeAgnusApi {
        getSignature = agnusGetSignatureMock
        getContract = agnusGetContractMock
      }
      jest.doMock(agnusApiPath, () => ({
        __esModule: true,
        default: FakeAgnusApi,
      }))

      const mod = require('../../../../core/resources/accountResource')
      accountResource = mod.default || mod
    })
  }

  beforeEach(() => {
    jest.clearAllMocks()
    loadSUT()
  })

  afterEach(() => {
    jest.useRealTimers()
  })

  it('returns contract when getSignature has contract_id', async () => {
    agnusGetSignatureMock.mockResolvedValue({ contract_id: 'c-123' })
    const contract = { contract_id: 'c-123', due_day: '12', foo: 'bar' }
    agnusGetContractMock.mockResolvedValue(contract)

    const out = await accountResource.getAgnusData('sig-abc')

    expect(agnusGetSignatureMock).toHaveBeenCalledWith('sig-abc')
    expect(agnusGetContractMock).toHaveBeenCalledWith('c-123')
    expect(reportErrorMock).not.toHaveBeenCalled()
    expect(out).toEqual(contract)
  })

  it('returns null when signature does not have contract_id', async () => {
    agnusGetSignatureMock.mockResolvedValue({})

    const out = await accountResource.getAgnusData('sig-no-contract')

    expect(agnusGetSignatureMock).toHaveBeenCalledWith('sig-no-contract')
    expect(agnusGetContractMock).not.toHaveBeenCalled()
    expect(out).toBeNull()
  })

  it('on error, logs and returns object with due_day as current day (DD)', async () => {
    agnusGetSignatureMock.mockRejectedValueOnce(new Error('agnus down'))

    const out = await accountResource.getAgnusData('sig-boom')

    expect(reportErrorMock).toHaveBeenCalled()
    expect(out).toEqual({ due_day: '20' })
  })
})

describe('AccountResource.getRenewDate', () => {
  let accountResource: any
  let getAgnusDataSpy: jest.SpyInstance

  const FIXED_NOW = new Date('2025-01-20T15:00:00Z')

  const loadSUT = () => {
    jest.isolateModules(() => {
      jest.resetModules()
      jest.useFakeTimers().setSystemTime(FIXED_NOW)

      const baseResourcePath = require.resolve('../../../../core/resources/BaseResource')

      jest.doMock(baseResourcePath, () => ({
        __esModule: true,
        default: class {},
      }))

      const mod = require('../../../../core/resources/accountResource')
      accountResource = mod.default || mod
    })
  }

  beforeEach(() => {
    jest.clearAllMocks()
    loadSUT()
  })

  afterEach(() => {
    jest.useRealTimers()
  })

  it('when due_day <= current day, returns YYYY-MM-(due_day) of the same month', async () => {
    getAgnusDataSpy = jest.spyOn(accountResource, 'getAgnusData').mockResolvedValue({ due_day: '05' })

    const out = await accountResource.getRenewDate('sig-1')
    expect(getAgnusDataSpy).toHaveBeenCalledWith('sig-1')
    expect(out).toBe('2025-01-05')
  })

  it('when due_day > current day, subtracts one month: (YYYY-MM-due_day) - 1 month', async () => {
    getAgnusDataSpy = jest.spyOn(accountResource, 'getAgnusData').mockResolvedValue({ due_day: '28' })

    const out = await accountResource.getRenewDate('sig-2')
    expect(out).toBe('2024-12-28')
  })

  it('fallback: if getAgnusData returns null, uses current day (DD)', async () => {
    getAgnusDataSpy = jest.spyOn(accountResource, 'getAgnusData').mockResolvedValue(null as any)

    const out = await accountResource.getRenewDate('sig-null')
    expect(out).toBe('2025-01-20')
  })

  it('throws error if due_day is empty (empty string)', async () => {
    getAgnusDataSpy = jest.spyOn(accountResource, 'getAgnusData').mockResolvedValue({ due_day: '' })

    await expect(accountResource.getRenewDate('sig-empty')).rejects.toThrow(
      'Resource: We have a problem to get the due day from agnus',
    )
  })
})

describe('AccountResource.creditsControlEnabledMany', () => {
  let accountResource: any
  let findManyMock: jest.Mock

  const FIXED_NOW = new Date('2025-04-01T00:00:00Z')

  const loadSUT = () => {
    jest.isolateModules(() => {
      jest.resetModules()
      jest.useFakeTimers().setSystemTime(FIXED_NOW)

      const baseResourcePath = require.resolve('../../../../core/resources/BaseResource')

      findManyMock = jest.fn().mockResolvedValue([{ id: 'a1' }, { id: 'a2' }])
      jest.doMock(baseResourcePath, () => ({
        __esModule: true,
        default: class {
          getRepository() {
            return { findMany: findManyMock }
          }
        },
      }))

      const mod = require('../../../../core/resources/accountResource')
      accountResource = mod.default || mod
    })
  }

  beforeEach(() => {
    jest.clearAllMocks()
    loadSUT()
  })
  afterEach(() => {
    jest.useRealTimers()
  })

  it('builds query with filters (enabled, isActive, not expired) and merges options', async () => {
    const extra = { where: { region: 'BR' }, attributes: ['id'] }

    const out = await accountResource.creditsControlEnabledMany(extra)

    expect(findManyMock).toHaveBeenCalledTimes(1)
    const [query] = findManyMock.mock.calls[0]

    expect(query.attributes).toEqual(extra.attributes)

    expect(query.where).toMatchObject({
      region: 'BR',
      'creditsControl.enabled': true,
      isActive: true,
    })

    const or = query.where.$or
    expect(Array.isArray(or)).toBe(true)
    expect(or.some((c: any) => c.expiresAt === null)).toBe(true)
    const hasGte = or.some((c: any) => c.expiresAt && c.expiresAt.$gte instanceof Date)
    expect(hasGte).toBe(true)

    expect(out).toEqual([{ id: 'a1' }, { id: 'a2' }])
  })
})

describe('AccountResource.creditsControlEnabledById', () => {
  let accountResource: any
  let ccManySpy: jest.SpyInstance

  const loadSUT = () => {
    jest.isolateModules(() => {
      jest.resetModules()

      const baseResourcePath = require.resolve('../../../../core/resources/BaseResource')

      jest.doMock(baseResourcePath, () => ({
        __esModule: true,
        default: class {},
      }))

      const mod = require('../../../../core/resources/accountResource')
      accountResource = mod.default || mod
    })
  }

  beforeEach(() => {
    jest.clearAllMocks()
    loadSUT()
  })

  it('calls creditsControlEnabledMany with where.id and returns the first item', async () => {
    ccManySpy = jest
      .spyOn(accountResource, 'creditsControlEnabledMany')
      .mockResolvedValue([{ id: 'acc-123' }, { id: 'acc-123-b' }])

    const out = await accountResource.creditsControlEnabledById('acc-123', { where: { foo: 1 } })

    expect(ccManySpy).toHaveBeenCalledWith(
      expect.objectContaining({
        attributes: ['id', 'creditsControl'],
        where: expect.objectContaining({ id: 'acc-123', foo: 1 }),
      }),
    )
    expect(out).toEqual({ id: 'acc-123' })
  })

  it('returns undefined when no results are found', async () => {
    jest.spyOn(accountResource, 'creditsControlEnabledMany').mockResolvedValue([])
    const out = await accountResource.creditsControlEnabledById('acc-x')
    expect(out).toBeUndefined()
  })
})

describe('AccountResource.accountUpdateCreditsControl', () => {
  let accountResource: any
  let updateByIdMock: jest.Mock

  const loadSUT = () => {
    jest.isolateModules(() => {
      jest.resetModules()

      const baseResourcePath = require.resolve('../../../../core/resources/BaseResource')

      updateByIdMock = jest.fn().mockResolvedValue({ ok: true })
      jest.doMock(baseResourcePath, () => ({
        __esModule: true,
        default: class {
          updateById(id: any, payload: any, opts?: any) {
            return updateByIdMock(id, payload, opts)
          }
        },
      }))

      const mod = require('../../../../core/resources/accountResource')
      accountResource = mod.default || mod
    })
  }

  beforeEach(() => {
    jest.clearAllMocks()
    loadSUT()
  })

  it('calls super.updateById with payload {creditsControl} and mergeJson=["creditsControl"]', async () => {
    const accountId = 'acc-77'
    const creditsControlPatch = {
      enabled: true,
      threshold: 150,
      alerts: { email: true },
    }

    const res = await accountResource.accountUpdateCreditsControl(accountId, creditsControlPatch)

    expect(updateByIdMock).toHaveBeenCalledTimes(1)
    const [idArg, payloadArg, optsArg] = updateByIdMock.mock.calls[0]
    expect(idArg).toBe(accountId)
    expect(payloadArg).toEqual({ creditsControl: creditsControlPatch })
    expect(optsArg).toEqual({ mergeJson: ['creditsControl'] })
    expect(res).toEqual({ ok: true })
  })
})

describe('AccountResource.getAccountPlan', () => {
  let accountResource: any
  let findOneMock: jest.Mock

  const FIXED_NOW = new Date('2025-01-20T15:00:00Z')

  const loadSUT = () => {
    jest.isolateModules(() => {
      jest.useFakeTimers().setSystemTime(FIXED_NOW)

      findOneMock = jest.fn().mockResolvedValue({
        id: 'acc-42',
        plan: { isActive: true },
        creditsControl: { enabled: true },
      })

      jest.doMock('../../../../core/resources/BaseResource', () => ({
        __esModule: true,
        default: class {
          getRepository() {
            return { findOne: findOneMock }
          }
        },
      }))

      const mod = require('../../../../core/resources/accountResource')
      accountResource = mod.default || mod
    })
  }

  beforeEach(() => {
    jest.resetModules()
    jest.clearAllMocks()
    loadSUT()
  })

  afterEach(() => {
    jest.useRealTimers()
  })

  it('builds the query correctly (attributes and active/not expired filters) and returns the result', async () => {
    const accountId = 'acc-42'
    const result = await accountResource.getAccountPlan(accountId)

    expect(result).toEqual({
      id: 'acc-42',
      plan: { isActive: true },
      creditsControl: { enabled: true },
    })

    expect(findOneMock).toHaveBeenCalledTimes(1)
    const [query] = findOneMock.mock.calls[0]

    expect(query.attributes).toEqual(['id', 'plan', 'creditsControl'])

    expect(query.where.id).toBe(accountId)

    expect(Array.isArray(query.where.$and)).toBe(true)
    expect(query.where.$and).toHaveLength(2)

    const orActive = query.where.$and[0].$or
    const orNotExpired = query.where.$and[1].$or

    expect(orActive).toEqual(expect.arrayContaining([{ isActive: true }, { 'plan.isActive': true }]))

    expect(Array.isArray(orNotExpired)).toBe(true)
    const hasNull = orNotExpired.some((c: any) => c?.expiresAt === null)
    const gteClause = orNotExpired.find((c: any) => c?.expiresAt && c.expiresAt.$gte instanceof Date)
    expect(hasNull).toBe(true)
    expect(gteClause).toBeTruthy()
    expect(gteClause.expiresAt.$gte.getTime()).toBe(FIXED_NOW.getTime())
  })
})

describe('AccountResource.accountUpdatePlan', () => {
  let accountResource: any
  let updateByIdMock: jest.Mock

  const loadSUT = () => {
    jest.isolateModules(() => {
      jest.resetModules()

      const baseResourcePath = require.resolve('../../../../core/resources/BaseResource')

      updateByIdMock = jest.fn().mockResolvedValue({ ok: true, merged: 'plan' })
      jest.doMock(baseResourcePath, () => ({
        __esModule: true,
        default: class {
          updateById(id: any, payload: any, opts?: any) {
            return updateByIdMock(id, payload, opts)
          }
        },
      }))

      const mod = require('../../../../core/resources/accountResource')
      accountResource = mod.default || mod
    })
  }

  beforeEach(() => {
    jest.clearAllMocks()
    loadSUT()
  })

  it('calls super.updateById with {plan} and mergeJson=["plan"]', async () => {
    const accountId = 'acc-55'
    const planPatch = {
      isActive: true,
      ai: { summary: 10, csat: 2 },
      emailNotifications: { expiredNotificationSent: false, expirationNotificationSent: true },
    }

    const res = await accountResource.accountUpdatePlan(accountId, planPatch)

    expect(updateByIdMock).toHaveBeenCalledTimes(1)
    const [idArg, payloadArg, optsArg] = updateByIdMock.mock.calls[0]

    expect(idArg).toBe(accountId)
    expect(payloadArg).toEqual({ plan: planPatch })
    expect(optsArg).toEqual({ mergeJson: ['plan'] })

    expect(res).toEqual({ ok: true, merged: 'plan' })
  })

  it('works with partial plan (does not require full object)', async () => {
    const accountId = 'acc-partial'
    const planPatch = { renewDate: '2025-06-01' }

    await accountResource.accountUpdatePlan(accountId, planPatch)

    const [idArg, payloadArg, optsArg] = updateByIdMock.mock.calls[0]
    expect(idArg).toBe(accountId)
    expect(payloadArg).toEqual({ plan: { renewDate: '2025-06-01' } })
    expect(optsArg).toEqual({ mergeJson: ['plan'] })
  })
})

describe('AccountResource.generateNewExpirationDate', () => {
  let accountResource: any
  let findByIdMock: jest.Mock

  const FIXED_NOW = new Date('2025-03-10T09:00:00Z')

  const loadSUT = () => {
    jest.isolateModules(() => {
      jest.resetModules()
      jest.useFakeTimers().setSystemTime(FIXED_NOW)

      const baseResourcePath = require.resolve('../../../../core/resources/BaseResource')

      findByIdMock = jest.fn()
      jest.doMock(baseResourcePath, () => ({
        __esModule: true,
        default: class {
          findById = findByIdMock
        },
      }))

      const mod = require('../../../../core/resources/accountResource')
      accountResource = mod.default || mod
    })
  }

  beforeEach(() => {
    jest.clearAllMocks()
    loadSUT()
  })

  afterEach(() => {
    jest.useRealTimers()
  })

  it('when receives OBJECT and (isFirstLogin && changeUserPasswordOnFirstAccess) ⇒ returns now', async () => {
    const accObj = {
      id: 'acc-obj',
      settings: {
        changeUserPasswordOnFirstAccess: true,
        isPasswordExpirationActive: false,
        expirationPasswordTime: 0,
      },
    }

    const out = await accountResource.generateNewExpirationDate(accObj, {}, true)

    expect(findByIdMock).not.toHaveBeenCalled()
    expect(out).toBeInstanceOf(Date)
    expect((out as Date).getTime()).toBe(FIXED_NOW.getTime())
  })

  it('when receives STRING and isPasswordExpirationActive=true ⇒ now + expirationPasswordTime days', async () => {
    findByIdMock.mockResolvedValue({
      id: 'acc-2',
      settings: {
        isPasswordExpirationActive: true,
        expirationPasswordTime: 10,
        changeUserPasswordOnFirstAccess: false,
      },
    })

    const options = { some: 'opt' }
    const out = await accountResource.generateNewExpirationDate('acc-2', options, false)

    expect(findByIdMock).toHaveBeenCalledWith(
      'acc-2',
      expect.objectContaining({
        attributes: ['id', 'settings'],
        some: 'opt',
      }),
    )

    const expected = new Date(FIXED_NOW.getTime() + 10 * 24 * 60 * 60 * 1000).getTime()
    expect((out as Date).getTime()).toBe(expected)
  })

  it('returns null when expiration is disabled and no special first-login', async () => {
    findByIdMock.mockResolvedValue({
      id: 'acc-3',
      settings: {
        isPasswordExpirationActive: false,
        expirationPasswordTime: 0,
        changeUserPasswordOnFirstAccess: false,
      },
    })

    const out = await accountResource.generateNewExpirationDate('acc-3')
    expect(out).toBeNull()
  })
})
