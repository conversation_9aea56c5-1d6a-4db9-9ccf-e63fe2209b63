jest.mock('../../../../core/dbSequelize/repositories/cardRepository', () => ({
  findById: jest
    .fn()
    .mockResolvedValue({ id: '1', pipelineId: 'p1', pipelineStageId: 'ps1', accountId: 'a1', order: 1 }),
  updateOrder: jest.fn().mockResolvedValue(true),
}))
jest.mock('../../../../core/dbSequelize/repositories/userRepository', () => ({
  findMany: jest.fn().mockResolvedValue([
    {
      id: '1',
      name: 'Owner One',
      permissions: [{ name: 'pipelines.opportunity.update' }],
    },
    {
      id: '2',
      name: 'Owner Two',
      permissions: [{ name: 'other.permission' }],
    },
  ]),
}))
jest.mock('../../../../core/dbSequelize/repositories/cardProductRepository', () => ({
  create: jest.fn().mockResolvedValue({ id: 'prod1', cardId: '1' }),
  updateById: jest.fn().mockResolvedValue({ id: 'prod1', cardId: '1' }),
  findMany: jest.fn().mockResolvedValue([{ id: 'prod1', cardId: '1' }]),
  destroy: jest.fn().mockResolvedValue(true),
}))
jest.mock('../../../../core/dbSequelize/repositories/cardCommentRepository', () => ({
  create: jest.fn().mockResolvedValue({ id: 'c1', cardId: '1', text: 'test' }),
  updateById: jest.fn().mockResolvedValue({ id: 'c1', cardId: '1', text: 'test' }),
  findById: jest.fn().mockResolvedValue({ id: 'c1', cardId: '1', text: 'test' }),
  destroy: jest.fn().mockResolvedValue(true),
}))
jest.mock('../../../../core/resources/cardMovementResource', () => ({
  move: jest.fn().mockResolvedValue(true),
}))
jest.mock('../../../../core/resources/contactResource', () => ({}))
jest.mock('../../../../core/resources/pipelineResource', () => ({
  findById: jest.fn().mockResolvedValue({ id: 'p1', stages: [{ id: 'stage1' }] }),
  emit: jest.fn(),
}))
jest.mock('../../../../core/transformers/cardTransformer', () => jest.fn((card) => card))
jest.mock('../../../../core/services/db/sequelize', () => ({
  literal: jest.fn(),
}))
jest.mock('../../../../core/utils/array/queuedAsyncMap', () => async (arr, fn, concurrency) => {
  const results = []
  for (let i = 0; i < (arr || []).length; i++) {
    results.push(await fn(arr[i], i))
  }
  return results
})
jest.mock('../../../../core/resources/BaseResource', () => {
  return {
    __esModule: true,
    default: class {
      constructor() {}
      findOne() {
        return { id: '1', pipelineId: 'p1', pipelineStageId: 'ps1', accountId: 'a1', order: 1 }
      }
      create(data) {
        return { ...data, id: '1' }
      }
      updateById(id, data) {
        return { ...data, id }
      }
      update(card, data, options) {
        return { ...card, ...data }
      }
      findMany() {
        return [{ id: '1', pipelineId: 'p1', pipelineStageId: 'ps1', accountId: 'a1', order: 1 }]
      }
      count() {
        return 1
      }
      emitUpdated() {
        return 'ok'
      }
    },
    CREATED: 'CREATED',
    UPDATED: 'UPDATED',
    DESTROYED: 'DESTROYED',
  }
})

import CardResource from '../../../../core/resources/cardResource'

describe('CardResource', () => {
  afterEach(() => {
    jest.restoreAllMocks()
  })

  describe('Consultation and creation', () => {
    it('should find card by id', async () => {
      const result = await CardResource.findById('1')
      expect(result).toEqual(expect.objectContaining({ id: '1' }))
    })

    it('should get first stage of pipeline', async () => {
      const stageId = await CardResource.getFirsStage('p1')
      expect(stageId).toBe('stage1')
    })

    it('should create a card', async () => {
      CardResource.count = jest.fn().mockResolvedValue(1)
      CardResource.addProducts = jest.fn().mockResolvedValue([{ id: 'prod1', cardId: '1' }])
      const card = await CardResource.create(
        { pipelineId: 'p1', pipelineStageId: 'ps1', userId: 'u1', products: [{ id: 'prod1' }] },
        {},
      )
      expect(card).toBeDefined()
      expect(CardResource.addProducts).toHaveBeenCalled()
    })
  })

  describe('Update', () => {
    it('should update a card', async () => {
      CardResource.findById = jest.fn().mockResolvedValue({
        id: '1',
        pipelineId: 'p1',
        pipelineStageId: 'ps1',
        accountId: 'a1',
        order: 1,
        totalValue: 100,
      })
      const spyDestroy = jest.spyOn(CardResource, 'destroyProducts').mockResolvedValue(true)
      CardResource.addProducts = jest.fn().mockResolvedValue([{ id: 'prod1', cardId: '1' }])
      const updated = await CardResource.updateById('1', { products: [{ id: 'prod1', new: false }] }, {})
      expect(updated).toBeDefined()
      expect(spyDestroy).toHaveBeenCalled()
      expect(CardResource.addProducts).toHaveBeenCalled()
    })

    it('should update card order', async () => {
      const updated = await CardResource.updateOrder('1', { order: 2 })
      expect(updated).toBeDefined()
    })
  })

  describe('Products', () => {
    it('should add a product (new)', async () => {
      const prod = await CardResource.addProduct({ cardId: '1' })
      expect(prod).toEqual(expect.objectContaining({ cardId: '1' }))
    })

    it('should add a product (update)', async () => {
      const prod = await CardResource.addProduct({ id: 'prod1', cardId: '1' })
      expect(prod).toEqual(expect.objectContaining({ id: 'prod1' }))
    })

    it('should add multiple products', async () => {
      const products = await CardResource.addProducts('1', [{ cardId: '1' }])
      expect(products[0]).toEqual(expect.objectContaining({ cardId: '1' }))
    })

    it('should destroy products', async () => {
      const cardProductRepository = require('../../../../core/dbSequelize/repositories/cardProductRepository')
      const spyDestroy = jest.spyOn(cardProductRepository, 'destroy').mockResolvedValue(true)
      await CardResource.destroyProducts('1', ['1'])
      expect(spyDestroy).toHaveBeenCalled()
    })

    it('should destroy a product', async () => {
      const result = await CardResource.destroyProduct({ id: 'prod1', cardId: '1' })
      expect(result).toBe(true)
    })
  })

  describe('Comments', () => {
    it('should add a comment (new)', async () => {
      const comment = await CardResource.addComment({ cardId: '1', text: 'test' }, 'u1')
      expect(comment).toEqual(expect.objectContaining({ cardId: '1', text: 'test' }))
    })

    it('should add a comment (update)', async () => {
      const comment = await CardResource.addComment({ id: 'c1', cardId: '1', text: 'test' }, 'u1')
      expect(comment).toEqual(expect.objectContaining({ id: 'c1', cardId: '1', text: 'test' }))
    })

    it('should destroy a comment', async () => {
      const result = await CardResource.destroyComment('c1')
      expect(result).toBe(true)
    })

    it('should call cardCommentRepository.destroy with comment', async () => {
      const cardCommentRepository = require('../../../../core/dbSequelize/repositories/cardCommentRepository')
      cardCommentRepository.findById.mockResolvedValueOnce({ id: 'c1', cardId: '1', text: 'test' })
      const spyDestroy = jest.spyOn(cardCommentRepository, 'destroy').mockResolvedValue(true)
      const result = await CardResource.destroyComment('c1')
      expect(spyDestroy).toHaveBeenCalledWith({ id: 'c1', cardId: '1', text: 'test' }, {})
      expect(result).toBe(true)
      spyDestroy.mockRestore()
    })
  })

  describe('List consultation', () => {
    it('should find many cards', async () => {
      CardResource.findMany = jest.fn().mockResolvedValue([{ id: '1' }])
      const cards = await CardResource.findMany({})
      expect(cards[0].id).toBe('1')
    })

    it('should get cards', async () => {
      CardResource.findMany = jest.fn().mockResolvedValue([{ id: '1' }])
      const cards = await CardResource.getCards({ order: [['column', 'desc']] })
      expect(Array.isArray(cards)).toBe(true)
    })
  })

  describe('Edge cases and branches', () => {
    it('should update a card without products', async () => {
      CardResource.findById = jest.fn().mockResolvedValue({
        id: '1',
        pipelineId: 'p1',
        pipelineStageId: 'ps1',
        accountId: 'a1',
        order: 1,
        totalValue: 100,
      })
      const updated = await CardResource.updateById('1', {}, {})
      expect(updated).toBeDefined()
    })

    it('should add multiple products with empty array', async () => {
      jest.spyOn(CardResource, 'addProducts').mockImplementation(Object.getPrototypeOf(CardResource).addProducts)

      const cardProductRepository = require('../../../../core/dbSequelize/repositories/cardProductRepository')
      jest.spyOn(cardProductRepository, 'findMany').mockImplementation(() => [])

      let products = await CardResource.addProducts('1', [])
      expect(products).toEqual([])

      products = await CardResource.addProducts('1', [{}])
      expect(products).toEqual([])
    })

    it('should add multiple products with a valid array', async () => {
      jest.spyOn(CardResource, 'addProducts').mockImplementation(Object.getPrototypeOf(CardResource).addProducts)
      CardResource.addProduct = jest.fn().mockResolvedValue({ id: 'prod1', cardId: '1' })
      const cardProductRepository = require('../../../../core/dbSequelize/repositories/cardProductRepository')
      jest.spyOn(cardProductRepository, 'findMany').mockImplementation(() => [])

      let products = await CardResource.addProducts('1', [{ id: '1', name: 'prod1' }])
      expect(products).toEqual([{ cardId: '1', id: 'prod1' }])
    })

    it('should destroy a comment when not found', async () => {
      const cardCommentRepository = require('../../../../core/dbSequelize/repositories/cardCommentRepository')
      cardCommentRepository.findById.mockResolvedValueOnce(null)
      const result = await CardResource.destroyComment('notfound')
      expect(result).toBe(undefined)
    })

    it('should find many cards without attributes', async () => {
      CardResource.findMany = jest.fn().mockResolvedValue([{ id: '1' }])
      const cards = await CardResource.findMany({})
      expect(cards[0].id).toBe('1')
    })

    it('should get cards without attributes', async () => {
      CardResource.findMany = jest.fn().mockResolvedValue([{ id: '1' }])
      const cards = await CardResource.getCards({})
      expect(Array.isArray(cards)).toBe(true)
    })

    it('should call cardTransformer for each card', async () => {
      const cardTransformer = require('../../../../core/transformers/cardTransformer')
      CardResource.findMany = Object.getPrototypeOf(CardResource).findMany
      const cards = await CardResource.findMany({})
      expect(Array.isArray(cards)).toBe(true)
      expect(cardTransformer).toHaveBeenCalled()
    })

    it('should return formatted owners with permission labels', async () => {
      const scenarios = [
        {
          language: 'pt-BR',
          expectedLabel: 'Sem permissão',
        },
        {
          language: 'en-US',
          expectedLabel: 'No permission',
        },
        {
          language: 'es',
          expectedLabel: 'Sin permiso',
        },
      ]

      for (const { language, expectedLabel } of scenarios) {
        const result = await CardResource.getOwners({
          accountId: 'a1',
          language,
        })

        expect(result).toEqual([
          {
            id: '1',
            name: 'Owner One',
            disabled: false,
          },
          {
            id: '2',
            name: `Owner Two - ${expectedLabel}`,
            disabled: true,
          },
        ])
      }
    })
  })
})
