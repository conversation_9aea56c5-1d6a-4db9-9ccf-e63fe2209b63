jest.mock('../../../../core/resources/BaseResource', () => {
  return {
    __esModule: true,
    default: class {
      getEvents() {
        return []
      }

      constructor(repository) {}

      getRepository() {
        return {
          findOne: jest.fn(),
          findById: async () => ({ id: '123' }),
          findMany: jest.fn(),
          create: () => {
            return {
              setDepartments: jest.fn(),
            }
          },
          update: jest.fn(),
          destroy: jest.fn(),
          updateById: () => {
            return {
              setDepartments: jest.fn(),
            }
          },
          transaction: async (fn, object) => {
            return await fn()
          },
        }
      }

      findOne() {
        return Promise.resolve(null)
      }

      findById() {
        return Promise.resolve(null)
      }

      findMany() {
        return Promise.resolve(null)
      }

      reload() {
        return Promise.resolve({
          id: '789',
          name: 'Test Contact',
          accountId: '123',
          serviceId: '456',
          tags: [{ label: 'tag1' }],
          tagIds: ['id1', 'id2'],
          setTags: () => true,
        })
      }

      create() {
        return Promise.resolve({
          id: '789',
          name: 'Test Contact',
          accountId: '123',
          serviceId: '456',
          tags: [{ label: 'tag1' }],
          tagIds: ['id1', 'id2'],
          setTags: () => true,
        })
      }

      update() {
        return Promise.resolve(null)
      }

      destroy() {
        return Promise.resolve(null)
      }

      bulkUpdate() {
        return Promise.resolve([{ id: '123' }])
      }

      bulkDestroy() {
        return Promise.resolve(null)
      }

      emitUpdated() {
        return Promise.resolve(null)
      }

      emitCreated() {
        return Promise.resolve(null)
      }

      emit() {
        return Promise.resolve(null)
      }

      async nestedTransaction(fn, object) {
        return await fn()
      }
    },
    CREATED: 'CREATED',
    UPDATED: 'UPDATED',
    DESTROYED: 'DESTROYED',
  }
})
jest.mock('../../../../core/services/haystackIa', () => jest.fn())
jest.mock('../../../../core/dbSequelize/repositories/serviceRepository', () => jest.fn())
jest.mock('../../../../core/dbSequelize/repositories/messageRepository', () => {
  return {
    __esModule: true,
    default: {
      findMany: () => [
        {
          type: 'abc',
          text: 'abc',
          id: 'msg-123',
        },
      ],
    },
  }
})
jest.mock('../../../../core/transformers/messageTransformer', () => jest.fn((message) => message))
jest.mock('../../../../core/dbSequelize/repositories/ServicesHistoryRepository', () => jest.fn())
jest.mock('../../../../core/dbSequelize/repositories/tagRepository', () => {
  return {
    __esModule: true,
    default: {
      findOrCreate: jest.fn().mockReturnValue({ label: 'tag1' }),
      findMany: jest.fn().mockReturnValue({ label: 'tag1' }),
    },
  }
})
jest.mock('../../../../core/dbSequelize/repositories/ticketRepository', () => ({
  getTicketsCount: jest.fn(),
}))
jest.mock('../../../../core/resources/scheduleResource', () => {
  return {
    __esModule: true,
    default: {
      bulkDestroy: jest.fn().mockReturnValue(true),
      destroyMany: jest.fn(),
    },
  }
})
jest.mock('../../../../core/resources/campaignResource', () => jest.fn())
jest.unmock('../../../../core/resources/messageResource')
jest.mock('../../../../core/resources/messageResource', () => {
  return {
    __esModule: true,
    default: {
      userHasPermissionToTicketTransfer: jest.fn().mockReturnValue(true),
      userHasPermissionToOpenTickets: jest.fn().mockReturnValue(true),
    },
  }
})
jest.mock('../../../../core/resources/ticketResource', () => {
  return {
    __esModule: true,
    default: {
      findManyPaginated: jest.fn(),
    },
  }
})
jest.mock('../../../../core/resources/customFieldsResource', () => jest.fn())
jest.mock('../../../../core/services/db/sequelize', () => ({
  query: jest.fn(),
  literal: jest.fn(),
}))
jest.mock('../../../../core/resources/personResource', () => {
  return {
    __esModule: true,
    default: {
      findOne: jest.fn(),
    },
  }
})
jest.mock('../../../../core/resources/fileResource', () => ({
  create: jest.fn(),
  destroy: jest.fn(),
  destroyMany: jest.fn(),
}))
jest.mock('../../../../core/resources/ticketTransfersResource', () => {
  return {
    __esModule: true,
    default: {
      getTicketTransfer: () => [{ id: 'transfer-123' }],
    },
  }
})
jest.mock('../../../../core/resources/serviceResource', () => {
  return {
    __esModule: true,
    default: {
      findById: jest.fn(),
      findOne: jest.fn(),
    },
  }
})
jest.mock('../../../../core/dbSequelize/repositories/contactRepository', () => {
  return {
    __esModule: true,
    default: {
      create: jest.fn(),
      update: jest.fn(),
      findById: async () => ({ id: '123' }),
      findManyPaginated: jest.fn(),
      findMedias: jest.fn(),
      bulkCreate: jest.fn(),
      bulkUpdate: jest.fn(),
      findAll: jest.fn(),
      numberIsBlockedBySomeBlocklist: jest.fn(),
    },
  }
})
jest.mock('../../../../core/services/ticket/ticketService', () => {
  return {
    __esModule: true,
    default: {
      closeTicket: jest.fn(),
    },
  }
})
jest.mock('../../../../core/services/SmartSummary/SmartSummaryService', () => jest.fn())
jest.mock('../../../../core/services/driverService', () => {
  return {
    getValidId: () => 'service-123',
    syncGroupParticipants: () => [{ id: 'Participan-1' }],
    loadEarlierMessages: () => [{ id: 'Message-1' }],
    getServiceRpc: () => ({ syncGroupParticipants: jest.fn(), syncGroupById: jest.fn() }),
    syncContact: jest.fn(),
  }
})
jest.unmock('../../../../core/resources/contactResource')
jest.unmock('typedi')

jest.mock('typedi', () => {
  const mockTypediContainer = {
    get: jest.fn().mockReturnValue({
      log: jest.fn(),
      captureError: jest.fn(),
      dispatch: () => 'ok',
      set: jest.fn(),
      get: async () => true,
      limitReached: () => false,
      start: () => false,
    }),
    set: jest.fn(),
  }

  return {
    __esModule: true,
    default: mockTypediContainer,
    Container: mockTypediContainer,
    Service: () => () => {},
    Inject: () => () => {},
  }
})
jest.mock('../../../../core/queues/tickets', () => {
  return {
    getCloseTicketQueue: () => ({ run: (fn) => fn() }),
  }
})

import contactResource, { checkIfChanged } from '../../../../core/resources/contactResource'
import personResource from '../../../../core/resources/personResource'
import ticketResource from '../../../../core/resources/ticketResource'
import BaseResource from '../../../../core/resources/BaseResource'
import contactRepository from '../../../../core/dbSequelize/repositories/contactRepository'
import ticketRepository from '../../../../core/dbSequelize/repositories/ticketRepository'
import fileResource from '../../../../core/resources/fileResource'
import serviceResource from '../../../../core/resources/serviceResource'
import ticketService from '../../../../core/services/ticket/ticketService'
import RedisCacheStorage from '../../../../core/services/cache/RedisCacheStorage'
import sequelize from '../../../../core/services/db/sequelize'
import driverService from '../../../../core/services/driverService'
import SmartSummaryService from '../../../../core/services/SmartSummary/SmartSummaryService'
import { id } from 'date-fns/locale'

describe('ContactResource', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('create', () => {
    it('should create a contact successfully', async () => {
      const data = {
        name: 'Test Contact',
        accountId: '123',
        serviceId: '456',
        tags: [{ label: 'tag1' }],
        tagIds: ['id1', 'id2'],
      }

      serviceResource.findById.mockResolvedValue({ id: '456', type: 'whatsapp' })
      let result = await contactResource.create(data, { include: true })
      expect(result.id).toBe('789')

      result = await contactResource.create(data)
      expect(result.id).toBe('789')
    })

    it('should throw error for invalid service', async () => {
      const data = {
        name: 'Test Contact',
        serviceId: 'invalid',
      }

      serviceResource.findById.mockResolvedValueOnce(null)

      await expect(contactResource.create(data)).rejects.toThrow()
    })

    it('should throw error for create contact', async () => {
      const data = {
        name: 'Test Contact',
        accountId: '123',
        serviceId: '456',
        tags: [{ label: 'tag1' }],
        tagIds: ['id1', 'id2'],
      }

      serviceResource.findById.mockResolvedValueOnce({ id: '456', type: 'whatsapp' })
      jest
        .spyOn(BaseResource.prototype, 'create')
        .mockRejectedValueOnce(
          new Error('duplicate key value violates unique constraint "contacts_idfromservice_without_nine_idx"'),
        )

      await expect(contactResource.create(data)).rejects.toThrow(Error)
    })

    it('should return contact', async () => {
      const data = {
        name: 'Test Contact',
        accountId: '123',
        serviceId: '456',
        tags: [{ label: 'tag1' }],
        tagIds: ['id1', 'id2'],
        setTags: () => true,
      }

      serviceResource.findById.mockResolvedValueOnce({ id: '456', type: 'whatsapp' })
      jest
        .spyOn(BaseResource.prototype, 'create')
        .mockRejectedValueOnce(
          new Error('duplicate key value violates unique constraint "contacts_idfromservice_without_nine_idx"'),
        )

      jest.spyOn(contactResource, 'findOne').mockResolvedValueOnce(data)

      expect(await contactResource.create(data)).toBe(data)
    })
  })

  describe('update', () => {
    it('should update contact successfully', async () => {
      const mockContact = {
        id: '123',
        name: 'Old Name',
        tags: [{ label: 'tag1' }],
        tagIds: ['id1', 'id2'],
        changed: () => {},
      }

      const updateData = {
        name: 'New Name',
        personId: 'person-123',
      }

      jest.spyOn(BaseResource.prototype, 'update').mockResolvedValueOnce({ ...mockContact, ...updateData })
      jest.spyOn(BaseResource.prototype, 'reload').mockResolvedValueOnce({
        id: '789',
        name: 'New Name',
      })

      const result = await contactResource.update(mockContact, updateData, { include: true })

      expect(result.name).toBe('New Name')
    })
  })

  describe('addTags', () => {
    it('should add tags to contact', async () => {
      const mockContact = {
        id: '123',
        addTags: jest.fn(),
      }

      const mockTags = ['tag1', 'tag2']
      await contactResource.addTags(mockContact, mockTags)
      expect(mockContact.addTags).toHaveBeenCalledWith(mockTags)
    })
  })

  describe('closeTicket', () => {
    it('should close ticket successfully', async () => {
      const mockContact = {
        id: '123',
        ticket: { id: '456' },
      }

      ticketService.closeTicket.mockResolvedValue(true)
      const result = await contactResource.closeTicket(mockContact)
      expect(result).toBe(true)
    })
  })

  describe('findMediasByContact', () => {
    it('should find media for contact', async () => {
      const mockMedias = [
        { id: '1', type: 'image' },
        { id: '2', type: 'document' },
        { id: '1', type: 'link' },
        { id: '1', type: 'unknown' },
      ]

      contactRepository.findMedias.mockResolvedValue(mockMedias)
      let result = await contactResource.findMediasByContact(
        '123',
        {},
        {
          cursor: 'abc',
          order: [['createdAt', 'desc']],
          perPage: 10,
          type: 'media',
          name: 'abc',
        },
      )

      expect(result?.data?.[0]?.attachedId).toEqual('msg-123')

      result = await contactResource.findMediasByContact(
        '123',
        {},
        {
          cursor: 'abc',
          order: [['createdAt', 'desc']],
          perPage: 10,
          type: 'document',
          name: 'abc',
        },
      )

      expect(result?.data?.[0]?.attachedId).toEqual('msg-123')

      result = await contactResource.findMediasByContact(
        '123',
        {},
        {
          cursor: 'abc',
          order: [['createdAt', 'desc']],
          perPage: 10,
          type: 'link',
          name: 'abc',
        },
      )

      expect(result?.data?.[0]?.attachedId).toEqual('msg-123')

      result = await contactResource.findMediasByContact(
        '123',
        {},
        {
          cursor: 'abc',
          order: [['createdAt', 'desc']],
          perPage: 10,
          type: 'abc',
          name: 'abc',
        },
      )

      expect(result?.data?.[0]?.attachedId).toEqual('msg-123')
    })
  })

  describe('markRead', () => {
    it('should mark contact as read', async () => {
      const mockContact = {
        id: '123',
        unread: true,
        changed: () => {},
      }

      jest.spyOn(BaseResource.prototype, 'update').mockResolvedValueOnce({ ...mockContact, unread: false })
      contactRepository.update.mockResolvedValue({ ...mockContact, unread: false })
      const result = await contactResource.markRead(mockContact, {})
      expect(result.unread).toBe(false)
    })
  })

  describe('importFromGroup', () => {
    const mockGroup = {
      id: 'group1',
      accountId: '123',
      serviceId: '123',
      groups: [{ id: '456' }, { id: '789' }],
      participants: ['456', '789'],
    }

    it('should import contacts from group', async () => {
      const mockService = {
        id: 'service1',
        type: 'whatsapp',
        data: { status: { isConnected: true } },
      }

      serviceResource.findOne.mockResolvedValue(mockService)
      jest
        .spyOn(BaseResource.prototype, 'findMany')
        .mockResolvedValueOnce([{ id: '456', toJSON: () => ({ id: '456' }) }])

      const result = await contactResource.importFromGroup(mockGroup)
      expect(result?.[0]?.[0]?.id).toEqual('123')
    })

    it('should throw error service could not be found', async () => {
      serviceResource.findOne.mockResolvedValue(null)
      expect(contactResource.importFromGroup(mockGroup)).rejects.toThrow('Service (123) not found.')
    })

    it('should throw error service is disconnected', async () => {
      const mockService = {
        id: 'service1',
        type: 'whatsapp',
        data: { status: { isConnected: false } },
      }
      serviceResource.findOne.mockResolvedValue(mockService)
      expect(contactResource.importFromGroup(mockGroup)).rejects.toThrow('Service (123) disconnected.')
    })
  })

  describe('setMarkForNoBan', () => {
    it('should mark contact for no ban', async () => {
      const accountId = '123'
      const contactId = '456'

      jest.spyOn(RedisCacheStorage.prototype, 'set').mockResolvedValueOnce(null)
      await contactResource.setMarkForNoBan(accountId, [contactId])
    })
  })

  describe('getContactName', () => {
    it('should return correct contact name priority', () => {
      const mockContact = {
        internalName: 'Internal',
        name: 'Regular',
        alternativeName: 'Alternative',
      }

      const result = contactResource.getContactName(mockContact)
      expect(result).toBe('Internal')
    })
  })

  describe('attachToOtherContactsOfTheSameNumber', () => {
    it('should attach to other contacts with same number', async () => {
      const mockContact = {
        idFromService: '123456',
        id: '123',
        number: '123456',
        service: { type: 'whatsapp' },
      }

      contactRepository.findAll.mockResolvedValue([])
      jest.spyOn(BaseResource.prototype, 'bulkUpdate').mockResolvedValueOnce([])

      personResource.findOne.mockResolvedValue({ id: 'person-123' })
      const result = await contactResource.attachToOtherContactsOfTheSameNumber(mockContact)

      expect(BaseResource.prototype.bulkUpdate).toHaveBeenCalled()
      expect(result).toBe(mockContact)
    })
  })

  describe('getOrInsertContactByVcard', () => {
    const mockVCards = [
      {
        vcard: 'BEGIN:VCARD\nVERSION:3.0\nFN:John Doe\nTEL;waid=*************:+55 11 99999-9999\nEND:VCARD',
      },
      {
        vcard: 'BEGIN:VCARD\nVERSION:3.0\nFN:Jane Smith\nTEL;waid=*************:+55 11 88888-8888\nEND:VCARD',
      },
    ]

    const serviceId = 'service-123'
    const accountId = 'account-123'

    it('should return existing contacts when all vcards are found', async () => {
      const existingContacts = [
        {
          id: 'contact-1',
          data: { number: '*************' },
          serviceId,
          accountId,
        },
        {
          id: 'contact-2',
          data: { number: '*************' },
          serviceId,
          accountId,
        },
      ]

      jest.spyOn(contactResource, 'findMany').mockResolvedValueOnce(existingContacts)

      const result = await contactResource.getOrInsertContactByVcard(serviceId, accountId, mockVCards)

      expect(result).toEqual(['contact-1', 'contact-2'])
      expect(contactResource.findMany).toHaveBeenCalledWith({
        where: {
          data: {
            number: {
              $in: ['*************', '*************'],
            },
          },
          serviceId,
          accountId,
        },
      })
    })

    it('should create new contacts when vcards are not found', async () => {
      jest.spyOn(contactResource, 'findMany').mockResolvedValueOnce([])

      jest
        .spyOn(contactResource, 'create')
        .mockResolvedValueOnce({ id: 'new-contact-1' })
        .mockResolvedValueOnce({ id: 'new-contact-2' })

      const result = await contactResource.getOrInsertContactByVcard(serviceId, accountId, mockVCards)

      expect(result).toEqual(['new-contact-1', 'new-contact-2'])
      expect(contactResource.create).toHaveBeenCalledTimes(2)
      expect(contactResource.create).toHaveBeenCalledWith({
        serviceId,
        number: '*************',
        name: 'John Doe',
        alternativeName: 'John Doe',
        accountId,
        visible: true,
      })
    })

    it('should handle mixed scenario - some existing and some new contacts', async () => {
      const existingContacts = [
        {
          id: 'contact-1',
          data: { number: '*************' },
          serviceId,
          accountId,
        },
      ]

      jest.spyOn(contactResource, 'findMany').mockResolvedValueOnce(existingContacts)
      jest.spyOn(contactResource, 'create').mockResolvedValueOnce({ id: 'new-contact-2' })

      const result = await contactResource.getOrInsertContactByVcard(serviceId, accountId, mockVCards)

      expect(result).toEqual(['contact-1', 'new-contact-2'])
      expect(contactResource.create).toHaveBeenCalledTimes(1)
    })

    it('should handle vcard without valid number', async () => {
      const invalidVCard = [
        {
          vcard: 'BEGIN:VCARD\nVERSION:3.0\nFN:Invalid Card\nEND:VCARD',
        },
      ]

      jest.spyOn(contactResource, 'findMany').mockResolvedValueOnce([])

      const result = await contactResource.getOrInsertContactByVcard(serviceId, accountId, invalidVCard)

      expect(result).toEqual([])
      expect(contactResource.create).not.toHaveBeenCalled()
    })

    it('should filter out null results from invalid vcards', async () => {
      const mixedVCards = [...mockVCards, { vcard: 'BEGIN:VCARD\nVERSION:3.0\nFN:Invalid Card\nEND:VCARD' }]

      jest.spyOn(contactResource, 'findMany').mockResolvedValueOnce([])
      jest
        .spyOn(contactResource, 'create')
        .mockResolvedValueOnce({ id: 'new-contact-1' })
        .mockResolvedValueOnce({ id: 'new-contact-2' })

      const result = await contactResource.getOrInsertContactByVcard(serviceId, accountId, mixedVCards)

      expect(result).toEqual(['new-contact-1', 'new-contact-2'])
      expect(contactResource.create).toHaveBeenCalledTimes(2)
    })
  })

  describe('countContactMedias', () => {
    it('should count medias correctly for a contact', async () => {
      const mockSequelizeResponse = [
        [
          { type_group: 'media', count_per_group: '5' },
          { type_group: 'document', count_per_group: '3' },
          { type_group: 'link', count_per_group: '2' },
          { type_group: null, count_per_group: '10' },
        ],
      ]
      sequelize.query = jest.fn().mockResolvedValue(mockSequelizeResponse)
      const contactId = 'contact-123'
      const result = await contactResource.countContactMedias(contactId)

      expect(result).toEqual({
        media: 5,
        document: 3,
        link: 2,
        total: 10,
      })

      expect(sequelize.query).toHaveBeenCalledWith(expect.stringContaining(`m."contactId" = '${contactId}'`))
    })
  })

  describe('updateContactList', () => {
    it('should update contact list with selected ids', async () => {
      const params = {
        defaultDepartmentId: 'dept-123',
        defaultUserId: 'user-123',
        selectedIds: { 'contact-1': true, 'contact-2': true },
        accountId: 'acc-123',
        allContactsSelected: false,
      }

      jest.spyOn(BaseResource.prototype, 'bulkUpdate').mockResolvedValueOnce([])
      const result = await contactResource.updateContactList(params)
      expect(result).toBe(true)
    })

    it('should update all contacts when allContactsSelected is true', async () => {
      const params = {
        defaultDepartmentId: 'dept-123',
        defaultUserId: 'user-123',
        selectedIds: {},
        accountId: 'acc-123',
        allContactsSelected: true,
        filters: {
          where: { status: 'active' },
        },
      }

      jest.spyOn(contactResource, 'findManyPaginated').mockResolvedValue({
        data: [{ id: '1' }, { id: '2' }],
        total: 2,
        limit: 10,
        skip: 0,
        currentPage: 1,
        lastPage: 1,
        from: 0,
        to: 2,
      })

      const result = await contactResource.updateContactList(params)
      expect(result).toBe(true)
    })
  })

  describe('internalCreate', () => {
    it('should create contact with block list control', async () => {
      const data = {
        number: '123456',
        serviceId: 'service-123',
        accountId: 'acc-123',
        isGroup: true,
      }

      contactRepository.numberIsBlockedBySomeBlocklist.mockResolvedValue({
        userId: 'user-123',
        reason: 'blocked',
        id: 'block-123',
      })

      contactRepository.create.mockResolvedValue(data)

      const result = await contactResource.internalCreate(data)
      expect(result).toBe(data)
    })

    it('should create contact with avatar and thumbAvatar', async () => {
      const data = {
        serviceId: 'service-123',
        accountId: 'acc-123',
        avatar: {
          data: 'base64-data',
          mimetype: 'image/jpeg',
        },
        thumbAvatar: {
          data: 'thumb-data',
          mimetype: 'image/jpeg',
        },
      }

      jest.spyOn(fileResource, 'create').mockResolvedValue({})
      await contactResource.internalCreate(data)
      expect(fileResource.create).toHaveBeenCalledTimes(2)
    })
  })

  describe('internalUpdate', () => {
    it('should update contact with avatar changes', async () => {
      let mockContact = {
        id: '123',
        name: 'contact-name',
        getAvatar: jest.fn().mockResolvedValue({ checksum: 'old-checksum' }),
        getThumbAvatar: jest.fn().mockResolvedValue({ checksum: 'old-checksum' }),
      }

      const data = {
        lastMessage: { id: 'message-123', data: { isNew: true } },
        avatar: {
          data: 'new-avatar-data',
          mimetype: 'image/jpeg',
        },
        thumbAvatar: {
          data: 'new-avatar-data',
          mimetype: 'image/jpeg',
        },
      }

      const options = {
        ignoreNameUpdate: true,
        onlyIfChanged: true,
      }

      jest.spyOn(fileResource, 'destroy').mockResolvedValue({})
      jest.spyOn(fileResource, 'create').mockResolvedValue({})

      await contactResource.internalUpdate(mockContact, data, options)
      expect(fileResource.destroy).toHaveBeenCalled()
      expect(fileResource.create).toHaveBeenCalled()

      await contactResource.internalUpdate(mockContact, { ...data, destroyAvatar: true }, options)
      expect(fileResource.destroy).toHaveBeenCalled()

      await contactResource.internalUpdate(mockContact, { ...data, destroyAvatar: true }, options)
      expect(fileResource.destroy).toHaveBeenCalled()
    })

    it('should handle first message not from me', async () => {
      const mockContact = {
        id: '123',
        lastMessageId: null,
        getAvatar: jest.fn().mockResolvedValue(null),
        getThumbAvatar: jest.fn().mockResolvedValue(null),
      }

      const result = await contactResource.internalUpdate(mockContact, mockContact)
      expect(result?.id).toBe('123')
    })
  })

  describe('loadEarlierMessages', () => {
    it('should load messages using workers go for supported services', async () => {
      const mockContact = {
        id: '123',
        accountId: 'acc-123',
        serviceId: 'service-123',
        service: { type: 'whatsapp' },
      }

      process.env.WORKERS_GO_SERVICES = 'whatsapp,telegram'
      jest.spyOn(driverService, 'loadEarlierMessages').mockResolvedValue([])

      const result = await contactResource.loadEarlierMessages(mockContact, '2023-01-01')
      expect(result).toBe('ok')

      process.env.WORKERS_GO_SERVICES = null
      await contactResource.loadEarlierMessages(mockContact, '2023-01-01')
      expect(driverService.loadEarlierMessages).toHaveBeenCalled()
    })
  })

  describe('loadEarlierMessagesById', () => {
    it('should call loadEarlierMessages', async () => {
      jest.spyOn(contactResource, 'findById').mockResolvedValue({ id: '123' })
      jest.spyOn(contactResource, 'loadEarlierMessages').mockResolvedValue(true)
      await contactResource.loadEarlierMessagesById('123', '2023-01-01', {})
      expect(contactResource.loadEarlierMessages).toHaveBeenCalled()
    })
  })

  describe('isMarkedForNoBan', () => {
    it('should return true when contact is marked for no ban', async () => {
      const result = await contactResource.isMarkedForNoBan('acc-123', 'contact-123')
      expect(result).toBe(true)
    })
  })

  describe('getTicketsCount', () => {
    it('should return ticket counts for user', async () => {
      const mockCounts = [{ queueCount: '5', mineCount: '3' }]
      ticketRepository.getTicketsCount.mockResolvedValue(mockCounts)

      const result = await contactResource.getTicketsCount('acc-123', 'user-123', ['dept-1', 'dept-2'], {
        'tickets.view.all': true,
      })

      expect(result).toEqual({
        counts: [5, 3],
      })
    })
  })

  describe('includeTicketTransfer', () => {
    it('should include ticket transfer data when has permission', async () => {
      const mockContact = {
        id: '123',
        lastMessage: {
          id: 'msg-123',
          ticketId: 'ticket-123',
        },
      }

      const context = {
        user: { permissions: ['tickets.transfer.view'] },
        account: { id: 'acc-123' },
      }

      jest.spyOn(ticketResource, 'findManyPaginated').mockReturnValue({ data: [{ id: 'ticket-123' }], lastPage: 1 })
      const result = await contactResource.includeTicketTransfer(mockContact, context)
      expect(result?.[0]?.id).toEqual('123')
    })
  })

  describe('checkIfChanged', () => {
    it('should detect basic field changes', () => {
      let contact = {
        name: 'John Doe',
        internalName: 'JD',
        alternativeName: 'Johnny',
      }

      let newData = {
        name: 'John Doe',
        internalName: 'JD',
        alternativeName: 'Johnny',
      }

      expect(checkIfChanged(contact, newData)).toBe(false)

      contact = {
        name: 'John Doe',
        internalName: 'JD',
        alternativeName: 'Johnny',
      }

      newData = {
        name: 'John Doe',
        internalName: 'JD',
        alternativeName: 'Johnny',
        thumbAvatar: { id: 'avatar-123' },
      }

      expect(checkIfChanged(contact, newData)).toBe('thumbAvatar')

      contact = {
        name: 'John Doe',
        internalName: 'JD',
        alternativeName: 'Johnny',
        avatar: { id: 'avatar-123', checksum: 456 },
        thumbAvatar: { id: 'avatar-123', checksum: 456 },
      }

      newData = {
        name: 'John Doe',
        internalName: 'JD',
        alternativeName: 'Johnny',
        avatar: { id: 'avatar-123', checksum: 123 },
        thumbAvatar: { id: 'avatar-123', checksum: 123 },
      }

      expect(checkIfChanged(contact, newData)).toBe('avatar')

      contact = {
        name: 'John Doe',
        internalName: 'JD',
        alternativeName: 'Johnny',
        avatar: { id: 'avatar-123', checksum: 123 },
        thumbAvatar: { id: 'avatar-123', checksum: 456 },
      }

      newData = {
        name: 'John Doe',
        internalName: 'JD',
        alternativeName: 'Johnny',
        avatar: { id: 'avatar-123', checksum: 123 },
        thumbAvatar: { id: 'avatar-123', checksum: 123 },
      }

      expect(checkIfChanged(contact, newData)).toBe('avatar')
    })
  })

  describe('emitFirstSyncBatch', () => {
    jest.spyOn(contactResource, 'emit').mockResolvedValue(true)
    contactResource.emitFirstSyncBatch('contact-123', 'acc-123')
    expect(contactResource.emit).toHaveBeenCalled()
  })

  describe('bulkDestroy', () => {
    it('should call bulkDestroy', async () => {
      jest.spyOn(BaseResource.prototype, 'bulkDestroy').mockResolvedValue(true)
      jest.spyOn(BaseResource.prototype, 'findMany').mockResolvedValue([{ id: '123' }, { id: '456' }])
      await contactResource.bulkDestroy({ conditions: [] })
      expect(BaseResource.prototype.bulkDestroy).toHaveBeenCalled()
    })
  })

  describe('destroy', () => {
    it('should call destroy', async () => {
      jest.spyOn(BaseResource.prototype, 'destroy').mockResolvedValue(true)
      jest.spyOn(BaseResource.prototype, 'findById').mockResolvedValue({
        id: '123',
        currentTicket: { id: 'ticket-123', currentTicketTransfer: { id: 'transfer-123' } },
      })
      await contactResource.destroy('123')
      expect(BaseResource.prototype.destroy).toHaveBeenCalled()
    })
  })

  describe('destroyById', () => {
    it('should call destroy', async () => {
      jest.spyOn(contactRepository, 'findById').mockResolvedValue({ then: () => ({ id: '123' }) })
      jest.spyOn(contactResource, 'destroy').mockResolvedValue(true)
      await contactResource.destroyById('123')
      expect(contactResource.destroy).toHaveBeenCalled()
    })
  })

  describe('closeTicketById', () => {
    it('should throw error when contact cannot be found', () => {
      jest.spyOn(BaseResource.prototype, 'findById').mockResolvedValue(null)
      expect(contactResource.closeTicketById('123', {})).rejects.toThrow('Contact #123 is required to close ticket.')
    })

    it('should throw error when currentTicket cannot be found', () => {
      jest.spyOn(BaseResource.prototype, 'findById').mockResolvedValue({ id: '123', currentTicketId: '456' })
      expect(contactResource.closeTicketById('123', {})).rejects.toThrow('Ticket #456 is required to close ticket.')
    })

    it('should throw error when currentTicketTransfer cannot be found', () => {
      jest
        .spyOn(BaseResource.prototype, 'findById')
        .mockResolvedValue({ id: '123', currentTicket: { currentTicketTransferId: '456' } })
      expect(contactResource.closeTicketById('123', {})).rejects.toThrow(
        'CurrentTicketTransfer #456 is required to close ticket.',
      )
    })
  })

  describe('getContactsWhatsApp', () => {
    it('should call destroy', async () => {
      jest.spyOn(contactResource, 'findMany').mockResolvedValue([{ id: '123' }])
      const result = await contactResource.getContactsWhatsApp()
      expect(result).toEqual([{ id: '123' }])
    })
  })

  describe('migrateIdFromServiceFromTelegramGroups', () => {
    it('should call update', async () => {
      jest.spyOn(contactResource, 'findOne').mockResolvedValue({ id: '123' })
      jest.spyOn(contactResource, 'update').mockResolvedValue(true)
      await contactResource.migrateIdFromServiceFromTelegramGroups('previousIdFromService', 'newIdFromService')
      expect(contactResource.update).toHaveBeenCalled()
    })

    it('should call update', async () => {
      jest.spyOn(contactResource, 'findOne').mockResolvedValue(null)
      await expect(
        contactResource.migrateIdFromServiceFromTelegramGroups('previousIdFromService', 'newIdFromService'),
      ).resolves.toBeUndefined()
    })
  })

  describe('syncFromServiceById', () => {
    it('should call syncContact', async () => {
      jest.spyOn(driverService, 'syncContact').mockResolvedValue(true)
      await contactResource.syncFromServiceById('contact-123')
      expect(driverService.syncContact).toHaveBeenCalled()
    })
  })

  describe('summaryTicketById', () => {
    it('should generate summary for admin user', async () => {
      const contactId = '123'
      const isAdmin = true

      const mockContact = {
        id: contactId,
        currentTicket: {
          id: 'ticket-123',
          comments: 'Test comment',
          status: 'open',
          department: { name: 'Support' },
          user: { name: 'Agent' },
          ticketTopics: [{ name: 'Topic 1' }],
          currentTicketTransfer: {
            comments: 'Transfer comment',
            user: { name: 'Previous Agent' },
            department: { name: 'Previous Dept' },
          },
        },
      }

      jest.spyOn(contactResource, 'findById').mockResolvedValue(mockContact)

      const result = await contactResource.summaryTicketById(contactId, isAdmin)

      expect(result).toEqual({ success: true })
    })

    it('should thow exception when contact not be found', async () => {
      const contactId = '123'
      const isAdmin = true

      jest.spyOn(BaseResource.prototype, 'findById').mockResolvedValue(null)

      expect(contactResource.summaryTicketById(contactId, isAdmin)).rejects.toThrow(
        `Contact #123 is required to summary ticket.`,
      )
    })

    it('should thow exception when currentTickt not be found', async () => {
      const contactId = '123'
      const isAdmin = true

      jest.spyOn(BaseResource.prototype, 'findById').mockResolvedValue({ id: contactId, currentTicketId: '123' })

      expect(contactResource.summaryTicketById(contactId, isAdmin)).rejects.toThrow(
        `Ticket #123 is required to summary ticket.`,
      )
    })
  })

  describe('markMentionForMeReadById', () => {
    it('should return contact', async () => {
      jest.spyOn(contactResource, 'findById').mockResolvedValue({ id: '123', unread: 0 })
      const result = contactResource.markMentionForMeReadById('123', true, {})
      expect(result).resolves.toEqual({ id: '123', unread: 0 })
    })

    it('should call markMentionForMeRead', async () => {
      jest.spyOn(contactResource, 'findById').mockResolvedValue({ id: '123' })
      jest.spyOn(contactResource, 'markMentionForMeRead').mockResolvedValue(true)
      await contactResource.markMentionForMeReadById('123', false, {})
      expect(contactResource.markMentionForMeRead).toHaveBeenCalled()
    })
  })

  describe('markReadById', () => {
    it('should call markMentionForMeRead', async () => {
      jest
        .spyOn(contactResource, 'findById')
        .mockResolvedValue({ id: '123', unread: 0, isGroup: true, data: { hasUnreadMentionForMe: true } })
      jest.spyOn(contactResource, 'markMentionForMeRead').mockResolvedValue(true)
      await contactResource.markReadById('123', {})
      expect(contactResource.markMentionForMeRead).toHaveBeenCalled()
    })

    it('should return contact', async () => {
      jest
        .spyOn(contactResource, 'findById')
        .mockResolvedValue({ id: '123', unread: 0, isGroup: false, data: { hasUnreadMentionForMe: true } })
      jest.spyOn(contactResource, 'markMentionForMeRead').mockResolvedValue(true)
      const result = await contactResource.markReadById('123', {})
      expect(result?.id).toEqual('123')
    })

    it('should call markRead', async () => {
      jest
        .spyOn(contactResource, 'findById')
        .mockResolvedValue({ id: '123', unread: 1, isGroup: false, data: { hasUnreadMentionForMe: true } })
      jest.spyOn(contactResource, 'markRead').mockResolvedValue(true)
      const result = await contactResource.markReadById('123', {})
      expect(result?.id).toEqual('123')
      expect(contactResource.markRead).toHaveBeenCalled()
    })
  })
})
