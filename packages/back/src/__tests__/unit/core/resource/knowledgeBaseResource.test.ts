import { Container } from 'typedi'
import { KnowledgeBaseResource } from '../../../../core/resources/knowledgeBaseResource'
import accountResource from '../../../../core/resources/accountResource'
import knowledgeBaseRepository from '../../../../core/dbSequelize/repositories/knowledgeBaseRepository'
import knowledgeBaseItemRepository from '../../../../core/dbSequelize/repositories/knowledgeBaseItemRepository'
import HaystackIaApi from '../../../../core/services/haystackIa'
import messageResource from '../../../../core/resources/messageResource'
import copilotTranscriptionResource from '../../../../core/resources/copilotTranscriptionResource'
import creditMovementResource from '../../../../core/resources/creditMovementResource'
import QueueJobsDispatcher from '../../../../core/services/jobs/queue/QueueJobsDispatcher'
import config from '../../../../core/config'

jest.mock('../../../../core/dbSequelize/repositories/knowledgeBaseRepository')
jest.mock('../../../../core/dbSequelize/repositories/knowledgeBaseItemRepository')
jest.mock('../../../../core/services/haystackIa')
jest.mock('../../../../core/resources/messageResource', () => jest.fn())
jest.mock('../../../../core/resources/accountResource', () => jest.fn())
jest.mock('../../../../core/resources/copilotTranscriptionResource', () => ({
  create: jest.fn(),
}))
jest.mock('../../../../core/resources/creditMovementResource', () => jest.fn())
jest.mock('../../../../core/config', () => jest.fn())
jest.mock('../../../../core/resources/BaseResource', () => jest.fn())

jest.mock('../../../../core/dbSequelize/repositories/knowledgeBaseRepository', () => ({
  findById: jest.fn(),
  create: jest.fn(),
  destroy: jest.fn(),
}))

jest.mock('../../../../core/dbSequelize/repositories/knowledgeBaseItemRepository', () => ({
  findById: jest.fn(),
  create: jest.fn(),
  destroy: jest.fn(),
}))

const mockDispatch = jest.fn()

Container.get = jest.fn().mockImplementation((cls) => {
  if (cls === QueueJobsDispatcher) return { dispatch: mockDispatch }
  return { log: jest.fn() }
})

const fakeHaystack = new HaystackIaApi()
let resource = new KnowledgeBaseResource(fakeHaystack, knowledgeBaseRepository)

describe('KnowledgeBaseResource', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    resource = new KnowledgeBaseResource(fakeHaystack, knowledgeBaseRepository)
  })

  afterEach(() => {
    jest.restoreAllMocks()
  })

  describe('suggestResponseByMessage', () => {
    it('should return response from haystackIaApi', async () => {
      config.mockReturnValue(false)
      const mockResult = { message: 'response' }
      fakeHaystack.suggestResponseByMessage = jest.fn().mockResolvedValue(mockResult)

      const result = await resource.suggestResponseByMessage('msg-id', 'acc-id', false)

      expect(result).toEqual(mockResult)
      expect(fakeHaystack.suggestResponseByMessage).toHaveBeenCalled()
    })

    it('should return limit reached', async () => {
      config.mockReturnValue(true)

      const result = await resource.suggestResponseByMessage('msg-id', 'acc-id', false)

      expect(result).toEqual({ message: 'COPILOT_LIMIT_REACHED' })

      resource.limitReached = jest.fn().mockResolvedValue(false)

      expect(await resource.checkCreditLimit('account')).toEqual(null)
    })
  })

  describe('getMessagesByTicket', () => {
    it('should return decrypted messages', async () => {
      const fakeMessages = [{ dataValues: { id: '1', text: 'encrypted', type: 'chat' } }]
      messageResource.findMany = jest.fn().mockResolvedValue(fakeMessages)
      messageResource.decryptMessageText = jest.fn().mockResolvedValue('decrypted')

      const result = await resource.getMessagesByTicket('ticket-id', 'acc-id')

      expect(result[0].text).toBe('decrypted')
    })
  })

  describe('suggestResponseByTicket', () => {
    it('should return AUDIO_TRANSCRIPTION_DISABLED if audio and transcription disabled', async () => {
      config.mockReturnValue(false)

      messageResource.findMany = jest
        .fn()
        .mockResolvedValue([{ dataValues: { id: '1', type: 'audio', text: '', ticketId: 't1', accountId: 'a1' } }])
      messageResource.decryptMessageText = jest.fn().mockResolvedValue('')
      accountResource.findById = jest.fn().mockResolvedValue({ settings: { flags: {} } })

      const result = await resource.suggestResponseByTicket('ticket-id', 'acc-id', 'user-id', false)

      expect(result).toEqual({ message: 'AUDIO_TRANSCRIPTION_DISABLED' })
    })

    it('should return limit reached', async () => {
      config.mockReturnValue(true)

      const result = await resource.suggestResponseByTicket('ticket-id', 'acc-id', 'user-id', false)

      expect(result).toEqual({ message: 'COPILOT_LIMIT_REACHED' })
    })

    it('should throw error when there is no messages', async () => {
      config.mockReturnValue(false)

      messageResource.findMany = jest.fn().mockResolvedValue([])
      messageResource.decryptMessageText = jest.fn().mockResolvedValue('')
      accountResource.findById = jest.fn().mockResolvedValue({ settings: { flags: {} } })

      expect(resource.suggestResponseByTicket('ticket-id', 'acc-id', 'user-id', false)).rejects.toThrow(
        'THERE_ARE_NO_MESSAGES',
      )
    })

    it('should return message TRANSCRIPTION_LIMIT_REACHED when there is no credit to transcribe', async () => {
      config.mockReturnValue(false)

      messageResource.findMany = jest
        .fn()
        .mockResolvedValue([
          { dataValues: { id: '1', text: '', ticketId: 't1', accountId: 'a1', transcribeError: true } },
        ])
      messageResource.decryptMessageText = jest.fn().mockResolvedValue('')
      accountResource.findById = jest.fn().mockResolvedValue({ settings: { flags: {} } })

      const result = await resource.suggestResponseByTicket('ticket-id', 'acc-id', 'user-id', false)

      expect(result).toEqual({ message: 'TRANSCRIPTION_LIMIT_REACHED' })
    })

    it('should call dispatchTranscriptions if transcription enabled', async () => {
      config.mockReturnValue(false)
      messageResource.findMany = jest
        .fn()
        .mockResolvedValue([{ dataValues: { id: '1', type: 'audio', text: '', ticketId: 't1', accountId: 'a1' } }])
      messageResource.decryptMessageText = jest.fn().mockResolvedValue('')
      accountResource.findById = jest.fn().mockResolvedValue({
        settings: { flags: { 'enable-audio-transcription': true } },
      })

      const spy = jest.spyOn(resource, 'dispatchTranscriptions').mockResolvedValue({ message: 'ok' })

      const result = await resource.suggestResponseByTicket('ticket-id', 'acc-id', 'user-id', false)

      expect(spy).toHaveBeenCalled()
      expect(result).toEqual({ message: 'ok' })
    })

    it('should return sugestion', async () => {
      config.mockReturnValue(false)

      messageResource.findMany = jest
        .fn()
        .mockResolvedValue([{ dataValues: { id: '1', text: 'ABC', ticketId: 't1', accountId: 'a1' } }])
      messageResource.decryptMessageText = jest.fn().mockResolvedValue('')
      accountResource.findById = jest.fn().mockResolvedValue({ settings: { flags: {} } })

      const result = await resource.suggestResponseByTicket('ticket-id', 'acc-id', 'user-id', false)

      expect(fakeHaystack.suggestResponseByTicket).toHaveBeenCalled()
      expect(result).toEqual({ hasAudioMessage: false, hadEnoughCreditsToTranscribe: true })
    })

    it('should dispatch error from HaystackApi', async () => {
      config.mockReturnValue(false)

      messageResource.findMany = jest
        .fn()
        .mockResolvedValue([{ dataValues: { id: '1', text: 'ABC', ticketId: 't1', accountId: 'a1' } }])
      messageResource.decryptMessageText = jest.fn().mockResolvedValue('')
      accountResource.findById = jest.fn().mockResolvedValue({ settings: { flags: {} } })

      fakeHaystack.suggestResponseByTicket = jest.fn().mockRejectedValue({ response: { data: { detail: 'Error' } } })

      await expect(resource.suggestResponseByTicket('ticket-id', 'acc-id', 'user-id', false)).rejects.toThrow('Error')

      fakeHaystack.suggestResponseByTicket = jest.fn().mockRejectedValue({ message: 'Error' })

      await expect(resource.suggestResponseByTicket('ticket-id', 'acc-id', 'user-id', false)).rejects.toThrow(
        'Failed to suggest a response: Error',
      )
    })
  })

  describe('dispatchTranscriptions', () => {
    it('should dispatch jobs and create transcription record', async () => {
      const messages = [{ id: 'm1', ticketId: 't1', accountId: 'a1' }]
      jest.spyOn(copilotTranscriptionResource, 'create')

      await resource.dispatchTranscriptions(messages, 'user-id')

      expect(mockDispatch).toHaveBeenCalledWith(
        'queued-audio-transcribe',
        { idMessage: 'm1', notCharge: false, origin: 'copilot' },
        { hashKey: 'm1' },
      )
      expect(copilotTranscriptionResource.create).toHaveBeenCalled()
    })
  })

  describe('limitReached', () => {
    it('should return true if no renewDate', async () => {
      accountResource.findById = jest.fn().mockResolvedValue({})
      const result = await resource.limitReached('acc-id')
      expect(result).toBe(true)
    })

    it('should return false if enough credits', async () => {
      accountResource.findById = jest.fn().mockResolvedValue({ plan: { renewDate: '2025-01-01' } })
      creditMovementResource.balance = jest.fn().mockResolvedValue(10)

      const result = await resource.limitReached('acc-id')
      expect(result).toBe(false)
    })
  })

  describe('destroy', () => {
    it('should delete docs and items', async () => {
      knowledgeBaseItemRepository.findMany = jest.fn().mockResolvedValue([
        {
          docs: [{ docId: 'd1' }],
          accountId: 'a1',
        },
      ])
      fakeHaystack.deleteKnowledgeDocs = jest.fn()
      knowledgeBaseItemRepository.destroy = jest.fn()
      knowledgeBaseRepository.destroy = jest.fn()

      await resource.destroy({ id: 'kb1' })

      expect(fakeHaystack.deleteKnowledgeDocs).toHaveBeenCalled()
      expect(knowledgeBaseItemRepository.destroy).toHaveBeenCalled()
      expect(knowledgeBaseRepository.destroy).toHaveBeenCalled()
    })
  })
})
