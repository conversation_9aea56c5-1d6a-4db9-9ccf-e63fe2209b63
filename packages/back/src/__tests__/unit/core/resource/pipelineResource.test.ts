import pipelineResource, { PipelineResource } from '../../../../core/resources/pipelineResource'
import pipelineRepository from '../../../../core/dbSequelize/repositories/pipelineRepository'
import pipelineStageRepository from '../../../../core/dbSequelize/repositories/pipelineStageRepository'
import pipelineStageStatusRepository from '../../../../core/dbSequelize/repositories/pipelineStageStatusRepository'
import pipelineAutomationsRepository from '../../../../core/dbSequelize/repositories/pipelineAutomationsRepository'
import pipelineNotificationsRepository from '../../../../core/dbSequelize/repositories/pipelineNotificationsRepository'
import departmentResource from '../../../../core/resources/departmentResource'
import cardResource from '../../../../core/resources/cardResource'
import pipelineStageReasonRepository from '../../../../core/dbSequelize/repositories/pipelineStageReasonRepository'
import BaseResource from '../../../../core/resources/BaseResource'
import BadRequestHttpError from '../../../../core/utils/error/BadRequestHttpError'
import { UserInstance } from '../../../../core/dbSequelize/models/User'
import { userStub } from '../../../stubs/core/dbSequelize/models/userStub'
import { AccountInstance } from '../../../../core/dbSequelize/models/Account'
import { PipelineStageInstance } from '../../../../core/dbSequelize/models/PipelineStage'
import { PipelineAutomationsInstance } from '../../../../core/dbSequelize/models/PipelineAutomations'
import { PipelineNotificationsInstance } from '../../../../core/dbSequelize/models/PipelineNotifications'

const mockBaseResourceFindOne = {
  where: { id: 'pipeline-id' },
  include: [
    expect.objectContaining({
      model: 'stages',
      include: ['statuses', 'reasons'],
      attributes: {
        include: [
          [expect.any(Object), 'totalCards'],
          [expect.any(Object), 'totalCardsValue'],
          [expect.any(Object), 'totalWonCardsValue'],
          [expect.any(Object), 'totalLostCardsValue'],
        ],
      },
      order: [['position', 'ASC']],
    }),
  ],
}

jest.mock('../../../../core/services/db/sequelize', () => {
  return {
    __esModule: true,
    default: {
      query: jest.fn(),
    },
  }
})
jest.mock('../../../../core/resources/departmentResource', () => {
  return {
    __esModule: true,
    default: {
      findManyByIds: jest.fn(),
      findById: jest.fn(),
    },
  }
})
jest.mock('../../../../microServices/api/controllers/BaseResourceController', () => jest.fn())
jest.mock('../../../../core/dbSequelize/repositories/pipelineRepository')
jest.mock('../../../../core/resources/BaseResource', () => {
  return {
    __esModule: true,
    default: class {
      getEvents() {
        return []
      }

      constructor(repository) {}

      getRepository() {
        return {
          findOne: jest.fn(),
          findById: jest.fn(),
          findMany: jest.fn(),
          create: () => {
            return {
              setDepartments: jest.fn(),
            }
          },
          update: jest.fn(),
          destroy: jest.fn(),
          updateById: () => {
            return {
              setDepartments: jest.fn(),
            }
          },
        }
      }

      findOne() {
        return Promise.resolve(null)
      }

      findById() {
        return Promise.resolve(mockBaseResourceFindOne)
      }

      findMany() {
        return Promise.resolve(null)
      }

      create() {
        return Promise.resolve(null)
      }

      update() {
        return Promise.resolve(null)
      }

      emitUpdated() {
        return Promise.resolve(null)
      }

      emitCreated() {
        return Promise.resolve(null)
      }

      emit() {
        return Promise.resolve(null)
      }

      nestedTransaction() {
        return Promise.resolve(null)
      }
    },
    CREATED: 'CREATED',
    UPDATED: 'UPDATED',
    DESTROYED: 'DESTROYED',
  }
})
jest.mock('../../../../core/dbSequelize/models/index', () => jest.fn())
jest.mock('../../../../core/dbSequelize/models/Pipeline', () => jest.fn())
jest.mock('../../../../core/dbSequelize/models/PipelineStage', () => jest.fn())
jest.mock('../../../../core/dbSequelize/models/PipelineStageStatus', () => jest.fn())
jest.mock('../../../../core/dbSequelize/models/PipelineStageReason', () => jest.fn())
jest.mock('../../../../core/dbSequelize/models/Card', () => jest.fn())
jest.mock('../../../../core/dbSequelize/models/CustomField', () => jest.fn())
jest.mock('../../../../core/dbSequelize/models/CustomFieldValue', () => jest.fn())
jest.mock('../../../../core/dbSequelize/models/Contact', () => jest.fn())
jest.mock('../../../../core/dbSequelize/models/PipelineAutomations', () => jest.fn())
jest.mock('../../../../core/dbSequelize/models/PipelineNotifications', () => jest.fn())
jest.mock('../../../../core/dbSequelize/models/Department', () => jest.fn())
jest.mock('../../../../core/dbSequelize/models/Account', () => jest.fn())
jest.mock('../../../../core/dbSequelize/models/ContractedCredit', () => jest.fn())
jest.mock('../../../../core/dbSequelize/models/CreditMovement', () => jest.fn())
jest.mock('../../../../core/dbSequelize/models/Campaign', () => jest.fn())
jest.mock('../../../../core/dbSequelize/models/CampaignMessageProgress', () => jest.fn())
jest.mock('../../../../core/dbSequelize/models/CampaignMessage', () => jest.fn())
jest.mock('../../../../core/dbSequelize/models/User', () => jest.fn())
jest.mock('../../../../core/dbSequelize/models/NotificationRead', () => jest.fn())
jest.mock('../../../../core/dbSequelize/models/Schedule', () => jest.fn())
jest.mock('../../../../core/dbSequelize/models/Notification', () => jest.fn())
jest.mock('../../../../core/dbSequelize/models/Message', () => jest.fn())
jest.mock('../../../../core/dbSequelize/models/Permission', () => jest.fn())
jest.mock('../../../../core/dbSequelize/models/Role', () => jest.fn())
jest.mock('../../../../core/dbSequelize/models/Service', () => jest.fn())
jest.mock('../../../../core/dbSequelize/models/Tag', () => jest.fn())
jest.mock('../../../../core/dbSequelize/models/ServicesHealthHistory', () => jest.fn())
jest.mock('../../../../core/dbSequelize/models/ServerPod', () => jest.fn())
jest.mock('../../../../core/dbSequelize/models/PlanAiHistory', () => jest.fn())
jest.mock('../../../../core/dbSequelize/models/Subscription', () => jest.fn())
jest.mock('../../../../core/dbSequelize/models/Ticket', () => jest.fn())
jest.mock('../../../../core/dbSequelize/models/TicketTransfer', () => jest.fn())
jest.mock('../../../../core/dbSequelize/models/Answer', () => jest.fn())
jest.mock('../../../../core/dbSequelize/models/Question', () => jest.fn())
jest.mock('../../../../core/dbSequelize/models/BotsSession', () => jest.fn())
jest.mock('../../../../core/dbSequelize/models/ClientFeedback', () => jest.fn())
jest.mock('../../../../core/dbSequelize/models/Summary', () => jest.fn())
jest.mock('../../../../core/dbSequelize/models/CardComment', () => jest.fn())
jest.mock('../../../../core/dbSequelize/models/CardProduct', () => jest.fn())
jest.mock('../../../../core/dbSequelize/models/CardMovement', () => jest.fn())
jest.mock('../../../../core/dbSequelize/repositories/pipelineStageRepository', () => {
  return {
    __esModule: true,
    default: {
      create: jest.fn(),
      update: jest.fn(),
      findOne: jest.fn((data) => data),
      findById: jest.fn(),
      findMany: jest.fn(),
      destroy: jest.fn(),
    },
  }
})
jest.mock('../../../../core/dbSequelize/repositories/pipelineStageStatusRepository', () => {
  return {
    __esModule: true,
    default: {
      create: jest.fn(),
      update: jest.fn(),
      findOne: jest.fn(),
      findById: jest.fn(),
      findMany: jest.fn(),
      destroy: jest.fn(),
    },
  }
})
jest.mock('../../../../core/dbSequelize/repositories/pipelineAutomationsRepository', () => {
  return {
    __esModule: true,
    default: {
      create: jest.fn(),
      update: jest.fn(),
      findOne: jest.fn(),
      findById: jest.fn(),
      destroy: jest.fn(),
    },
  }
})
jest.mock('../../../../core/dbSequelize/repositories/pipelineNotificationsRepository', () => {
  return {
    __esModule: true,
    default: {
      create: jest.fn(),
      findOne: jest.fn(),
    },
  }
})
jest.mock('../../../../core/dbSequelize/repositories/pipelineStageReasonRepository')
jest.mock('../../../../core/resources/cardResource', () => {
  return {
    __esModule: true,
    default: {
      bulkUpdate: jest.fn(),
      findMany: jest.fn(),
      destroy: jest.fn(),
      updateById: jest.fn(),
      count: jest.fn(),
    },
  }
})
jest.mock('../../../../core/services/crypt/accountCryptor', () => ({
  getAccountEncryptionKey: jest.fn((data) => data),
  encryptTextForAccount: jest.fn((data) => data),
  decryptTextForAccount: jest.fn((data) => data),
  encryptBufferForAccountWithConcatenatedIv: jest.fn((data) => data),
  decryptBufferForAccountWithConcatenatedIv: jest.fn((data) => data),
}))

const mockPipeline = {
  id: '123e4567-e89b-12d3-a456-************',
  name: 'Pipeline de Teste',
  goBack: false,
  accountId: 'acc-123',
  account: {} as AccountInstance,
  stages: [] as PipelineStageInstance[],
  automations: [] as PipelineAutomationsInstance[],
  notifications: [] as PipelineNotificationsInstance[],
  createdAt: new Date(),
  updatedAt: new Date(),
  deletedAt: null,
  archivedAt: null,
  setDepartments: [],
}
const mockStage = { id: 1, name: 'Stage', statuses: [], reasons: [], position: 1 }
const mockStatus = { id: 1, name: 'Status', position: 1 }
const mockReason = { id: 1, name: 'Reason', position: 1 }
const mockAutomation = { id: 1, pipelineId: 1, type: 'auto', config: {} }
const mockNotification = { pipelineId: 1, pipelineAutomationId: 1 }

describe('PipelineResource', () => {
  let pipelineRes: PipelineResource

  beforeEach(() => {
    pipelineRes = pipelineResource
  })

  afterEach(() => {
    jest.clearAllMocks()
  })

  describe('constructor', () => {
    it('should initialize user as null', () => {
      expect(pipelineRes.user).toBeUndefined()
    })
  })

  describe('validations', () => {
    it('should throw if another pipeline with same name exists', async () => {
      pipelineRes['user'] = userStub as UserInstance
      jest.spyOn(BaseResource.prototype, 'findOne').mockResolvedValueOnce(mockPipeline)
      await expect(pipelineRes.validations(mockPipeline, mockPipeline.id)).rejects.toThrow(BadRequestHttpError)
    })
    it('should throw if duplicate stage names', async () => {
      pipelineRes['user'] = userStub as UserInstance
      jest.spyOn(BaseResource.prototype, 'findOne').mockResolvedValueOnce(null)
      const stages = [{ name: 'A' }, { name: 'A' }]
      await expect(pipelineRes.validations({ name: 'Test', stages }, '1')).rejects.toThrow(BadRequestHttpError)
    })
    it('should throw if a stage has more than 10 statuses', async () => {
      pipelineRes['user'] = userStub as UserInstance
      jest.spyOn(BaseResource.prototype, 'findOne').mockResolvedValueOnce(null)
      const stages = [{ name: 'A', statuses: Array(11).fill({ name: 's' }) }]
      await expect(pipelineRes.validations({ name: 'Test', stages }, '1')).rejects.toThrow(BadRequestHttpError)
    })
    it('should throw if duplicate status names in a stage', async () => {
      pipelineRes['user'] = userStub as UserInstance
      jest.spyOn(BaseResource.prototype, 'findOne').mockResolvedValueOnce(null)
      const stages = [{ name: 'A', statuses: [{ name: 's' }, { name: 's' }] }]
      await expect(pipelineRes.validations({ name: 'Test', stages }, '1')).rejects.toThrow(BadRequestHttpError)
    })
    it('should throw if a stage has more than 10 reasons', async () => {
      pipelineRes['user'] = userStub as UserInstance
      jest.spyOn(BaseResource.prototype, 'findOne').mockResolvedValueOnce(null)
      const stages = [{ name: 'A', statuses: [], reasons: Array(11).fill({ name: 'r' }) }]
      await expect(pipelineRes.validations({ name: 'Test', stages }, '1')).rejects.toThrow(BadRequestHttpError)
    })
    it('should throw if duplicate reason names in a stage', async () => {
      pipelineRes['user'] = userStub as UserInstance
      jest.spyOn(BaseResource.prototype, 'findOne').mockResolvedValueOnce(null)
      const stages = [{ name: 'A', statuses: [], reasons: [{ name: 'r' }, { name: 'r' }] }]
      await expect(pipelineRes.validations({ name: 'Test', stages }, '1')).rejects.toThrow(BadRequestHttpError)
    })
    it('should not throw if all is valid', async () => {
      pipelineRes['user'] = userStub as UserInstance
      jest.spyOn(BaseResource.prototype, 'findOne').mockResolvedValueOnce(null)
      const stages = [{ name: 'A', statuses: [{ name: 's' }], reasons: [{ name: 'r' }] }]
      await expect(pipelineRes.validations({ name: 'Test', stages }, '1')).resolves.toBeUndefined()
    })
  })

  describe('create', () => {
    it('should create pipeline and related stages/departments', async () => {
      pipelineRes['user'] = userStub as UserInstance
      jest.spyOn(pipelineRes, 'nestedTransaction').mockImplementation(async (fn) => fn('trx'))
      jest.spyOn(BaseResource.prototype, 'create').mockResolvedValue({ ...mockPipeline, setDepartments: jest.fn() })
      departmentResource.findManyByIds.mockResolvedValue([{ id: 1 }])
      pipelineStageRepository.create.mockResolvedValue({})
      jest.spyOn(pipelineRes, 'findById').mockResolvedValue(mockPipeline)
      const data = { user: pipelineRes['user'], stages: [mockStage], departments: [{ id: 1 }] }
      const result = await pipelineRes.create(data, {})
      expect(result).toEqual(mockPipeline)
    })
    it('should create default stages if no stages provided', async () => {
      pipelineRes['user'] = userStub as UserInstance
      jest.spyOn(pipelineRes, 'nestedTransaction').mockImplementation(async (fn) => fn('trx'))
      jest.spyOn(pipelineRes, 'validations').mockResolvedValue(undefined)
      jest.spyOn(BaseResource.prototype, 'create').mockResolvedValue({ ...mockPipeline, setDepartments: jest.fn() })
      departmentResource.findManyByIds.mockResolvedValue([{ id: 1 }])
      pipelineStageRepository.create.mockResolvedValue({})
      jest.spyOn(pipelineRes, 'findById').mockResolvedValue(mockPipeline)

      let data = { user: pipelineRes['user'], departments: [{ id: 1 }] }
      let result = await pipelineRes.create(data, {})
      expect(result).toEqual(mockPipeline)

      pipelineRes['user'] = { ...userStub, language: 'en-US' } as UserInstance
      data = { user: pipelineRes['user'], departments: [{ id: 1 }] }
      result = await pipelineRes.create(data, {})
      expect(result).toEqual(mockPipeline)

      pipelineRes['user'] = { ...userStub, language: 'es' } as UserInstance
      data = { user: pipelineRes['user'], departments: [{ id: 1 }] }
      result = await pipelineRes.create(data, {})
      expect(result).toEqual(mockPipeline)
    })
  })

  describe('createAutomation', () => {
    it('should create automation and emit UPDATED', async () => {
      pipelineRes['user'] = userStub as UserInstance
      pipelineAutomationsRepository.create.mockResolvedValue(mockAutomation)
      jest.spyOn(pipelineRes, 'findById').mockResolvedValue(mockPipeline)
      jest.spyOn(pipelineRes, 'emit').mockImplementation(() => {})
      jest.spyOn(pipelineRes, 'emit').mockImplementation(() => {})
      const result = await pipelineRes.createAutomation(mockAutomation)
      expect(result).toEqual(mockAutomation)
    })
    it('should throw if data invalid', async () => {
      pipelineRes['user'] = userStub as UserInstance
      await expect(pipelineRes.createAutomation({})).rejects.toThrow('Automation data is invalid')
    })
  })

  describe('updateAutomation', () => {
    it('should update automation and emit UPDATED', async () => {
      pipelineRes['user'] = userStub as UserInstance
      pipelineAutomationsRepository.findById.mockResolvedValue(mockAutomation)
      pipelineAutomationsRepository.update.mockResolvedValue(mockAutomation)
      jest.spyOn(pipelineRes, 'findById').mockResolvedValue(mockPipeline)
      jest.spyOn(pipelineRes, 'emit').mockImplementation(() => {})
      const result = await pipelineRes.updateAutomation(1, mockAutomation)
      expect(result).toEqual(mockAutomation)
    })
    it('should throw if not found', async () => {
      pipelineRes['user'] = userStub as UserInstance
      pipelineAutomationsRepository.findById.mockResolvedValue(null)
      await expect(pipelineRes.updateAutomation(1, mockAutomation)).rejects.toThrow('Automation not found')
    })
  })

  describe('getAutomation', () => {
    it('should return automation or empty array', async () => {
      pipelineRes['user'] = userStub as UserInstance
      pipelineAutomationsRepository.findOne.mockResolvedValue(mockAutomation)
      const result = await pipelineRes.getAutomation(1)
      expect(result).toEqual(mockAutomation)
      pipelineAutomationsRepository.findOne.mockResolvedValue(null)
      const result2 = await pipelineRes.getAutomation(1)
      expect(result2).toEqual([])
    })
  })

  describe('destroyAutomation', () => {
    it('should destroy automation', async () => {
      pipelineRes['user'] = userStub as UserInstance
      pipelineAutomationsRepository.findById.mockResolvedValue(mockAutomation)
      pipelineAutomationsRepository.destroy.mockResolvedValue()
      await expect(pipelineRes.destroyAutomation(1)).resolves.toBeUndefined()
    })
    it('should throw if not found', async () => {
      pipelineRes['user'] = userStub as UserInstance
      pipelineAutomationsRepository.findById.mockResolvedValue(null)
      await expect(pipelineRes.destroyAutomation(1)).rejects.toThrow('Automations not found')
    })
  })

  describe('createNotification', () => {
    it('should create notification', async () => {
      pipelineRes['user'] = userStub as UserInstance
      pipelineNotificationsRepository.create.mockResolvedValue(mockNotification)
      const result = await pipelineRes.createNotification(mockNotification)
      expect(result).toEqual(mockNotification)
    })
  })

  describe('createStage', () => {
    it('should create stage if not exists and under limit', async () => {
      pipelineRes['user'] = userStub as UserInstance
      jest.spyOn(pipelineRes, 'findById').mockResolvedValue({ ...mockPipeline, stages: [] })
      pipelineStageRepository.create.mockResolvedValue(mockStage)
      jest.spyOn(pipelineRes, 'emitUpdated').mockImplementation(() => {})
      const result = await pipelineRes.createStage({ pipelineId: 1, name: 'A' }, pipelineRes['user'])
      expect(result).toEqual(mockStage)
    })
    it('should return STAGES_LIMIT if over limit', async () => {
      pipelineRes['user'] = userStub as UserInstance
      jest.spyOn(pipelineRes, 'findById').mockResolvedValue({ ...mockPipeline, stages: Array(10).fill({}) })
      const result = await pipelineRes.createStage({ pipelineId: 1, name: 'A' }, pipelineRes['user'])
      expect(result).toBe('STAGES_LIMIT')
    })
    it('should return STAGE_ALREADY_EXIST if name exists', async () => {
      pipelineRes['user'] = userStub as UserInstance
      jest.spyOn(pipelineRes, 'findById').mockResolvedValue({ ...mockPipeline, stages: [{ name: 'A' }] })
      const result = await pipelineRes.createStage({ pipelineId: 1, name: 'A' }, pipelineRes['user'])
      expect(result).toBe('STAGE_ALREADY_EXIST')
    })
  })

  describe('updateStage', () => {
    it('should update stage if name not duplicate', async () => {
      pipelineRes['user'] = userStub as UserInstance
      jest.spyOn(pipelineRes, 'findById').mockResolvedValue({ ...mockPipeline, stages: [{ id: 2, name: 'B' }] })
      pipelineStageRepository.findById.mockResolvedValue(mockStage)
      pipelineStageRepository.update.mockResolvedValue(mockStage)
      jest.spyOn(pipelineRes, 'emitUpdated').mockImplementation(() => {})
      const result = await pipelineRes.updateStage(1, { pipelineId: 1, name: 'A', id: 1 })
      expect(result).toEqual(mockStage)
    })
    it('should return STAGE_ALREADY_EXIST if name exists', async () => {
      pipelineRes['user'] = userStub as UserInstance
      jest.spyOn(pipelineRes, 'findById').mockResolvedValue({ ...mockPipeline, stages: [{ id: 2, name: 'A' }] })
      const result = await pipelineRes.updateStage(1, { pipelineId: 1, name: 'A', id: 1 })
      expect(result).toBe('STAGE_ALREADY_EXIST')
    })
  })

  describe('destroyStage', () => {
    it('should move cards if typeAction is move', async () => {
      pipelineRes['user'] = userStub as UserInstance
      pipelineStageRepository.findById.mockResolvedValue({
        pipeline: { stages: [{}, {}] },
        cards: [{}],
      })
      cardResource.bulkUpdate.mockResolvedValue()
      pipelineStageRepository.destroy.mockResolvedValue()
      jest.spyOn(pipelineRes, 'emitUpdated').mockImplementation(() => {})
      let result = await pipelineRes.destroyStage(1, { typeAction: 'move', idNewStage: 2 })
      expect(result).toBe('PIPELINES_MIN_STAGES')

      pipelineStageRepository.findById.mockResolvedValue({
        pipeline: { stages: [{}, {}, {}] },
        cards: [{}],
      })
      result = await pipelineRes.destroyStage(1, { typeAction: 'move', idNewStage: 2 })
      expect(result).toBe('MESSAGE_STAGE_DELETED')
    })
    it('should archive cards if not move', async () => {
      pipelineRes['user'] = userStub as UserInstance
      pipelineStageRepository.findById.mockResolvedValue({
        pipeline: { stages: [{ position: 1 }, { position: 2 }, { position: 3 }] },
        cards: [{}],
      })
      cardResource.bulkUpdate.mockResolvedValue()
      pipelineStageRepository.destroy.mockResolvedValue()
      jest.spyOn(pipelineRes, 'emitUpdated').mockImplementation(() => {})
      const result = await pipelineRes.destroyStage(1, { typeAction: 'archive', idNewStage: 2 })
      expect(result).toBe('MESSAGE_STAGE_DELETED')
    })
    it('should return PIPELINES_MIN_STAGES if only 2 stages', async () => {
      pipelineRes['user'] = userStub as UserInstance
      pipelineStageRepository.findById.mockResolvedValue({
        pipeline: { stages: [{}, {}] },
      })
      const result = await pipelineRes.destroyStage(1, { typeAction: 'move', idNewStage: 2 })
      expect(result).toBe('PIPELINES_MIN_STAGES')
    })
  })

  describe('getStatuses', () => {
    it('should return statuses', async () => {
      pipelineRes['user'] = userStub as UserInstance
      pipelineStageStatusRepository.findMany.mockResolvedValue([mockStatus])
      const result = await pipelineRes.getStatuses(1)
      expect(result).toEqual([mockStatus])
    })
  })

  describe('archive', () => {
    it('should update archivedAt', async () => {
      pipelineRes['user'] = userStub as UserInstance
      jest.spyOn(pipelineRes, 'findById').mockResolvedValue(mockPipeline)
      jest.spyOn(BaseResource.prototype, 'update').mockResolvedValue({})
      const result = await pipelineRes.archive(1, { archive: true }, {})
      expect(result).toEqual(mockPipeline)
    })
  })

  describe('findManyWithTotals', () => {
    it('should call getTotals for each pipeline', async () => {
      pipelineRes['user'] = userStub as UserInstance
      jest.spyOn(BaseResource.prototype, 'findMany').mockResolvedValue([mockPipeline])
      pipelineRepository.getTotals.mockResolvedValue({ total: 1 })
      let result = await pipelineRes.findManyWithTotals({ where: { $and: [{}] } })
      expect(result.data[0].totals).toEqual({ total: 1 })
      result = await pipelineRes.findManyWithTotals({ where: { $and: [{ createdAt: Date() }] } })
      expect(result.data[0].totals).toEqual({ total: 1 })
    })
  })

  describe('duplicate', () => {
    it('should duplicate pipeline, stages, statuses, reasons, automations', async () => {
      pipelineRes['user'] = userStub as UserInstance
      jest.spyOn(BaseResource.prototype, 'findById').mockResolvedValue({
        ...mockPipeline,
        stages: [{ ...mockStage, statuses: [mockStatus], reasons: [mockReason] }],
        automations: [mockAutomation],
      })
      jest.spyOn(pipelineRes, 'nestedTransaction').mockImplementation(async (fn) => fn('trx'))
      jest.spyOn(BaseResource.prototype, 'create').mockResolvedValue({ ...mockPipeline, id: 2 })
      pipelineStageRepository.create.mockResolvedValue({ id: 10 })
      pipelineStageStatusRepository.create.mockResolvedValue({})
      pipelineStageReasonRepository.create.mockResolvedValue({})
      pipelineAutomationsRepository.create.mockResolvedValue({})
      const result = await pipelineRes.duplicate(1, {})
      expect(result.id).toBe(2)
    })
    it('should throw if pipeline not found', async () => {
      pipelineRes['user'] = userStub as UserInstance
      jest.spyOn(BaseResource.prototype, 'findById').mockResolvedValue(null)
      await expect(pipelineRes.duplicate(1, {})).rejects.toThrow(BadRequestHttpError)
    })
  })

  describe('update', () => {
    let pipelineRes: PipelineResource
    const mockTransaction = 'trx'
    const mockOptions = { transaction: mockTransaction }
    const mockPipeline = {
      id: 'pipeline-id',
      setDepartments: jest.fn(),
    }
    const mockDepartments = [{ id: 'dep-1' }]
    const mockStages = [
      { id: 'stage-1', name: 'Stage 1', new: false },
      { name: 'Stage 2', new: true, position: 2 },
    ]
    const mockStageToRemove = { id: 'stage-3' }
    const mockCard = { id: 'card-1' }

    beforeEach(() => {
      pipelineRes = pipelineResource
      jest.spyOn(pipelineRes, 'validations').mockResolvedValue(undefined)
      jest.spyOn(pipelineRes, 'nestedTransaction').mockImplementation(async (fn) => fn(mockTransaction))
      jest.spyOn(pipelineRes, 'findById').mockResolvedValue(mockPipeline as any)
      jest.spyOn(pipelineRes, 'emitUpdated').mockImplementation(() => {})
      jest
        .spyOn(BaseResource.prototype, 'getRepository')
        .mockReturnValue({ updateById: jest.fn().mockResolvedValue(mockPipeline) })
      departmentResource.findManyByIds.mockResolvedValue(mockDepartments)
      pipelineStageRepository.create.mockResolvedValue({})
      pipelineStageRepository.findById.mockResolvedValue({})
      pipelineStageRepository.update.mockResolvedValue({})
      pipelineStageRepository.findMany.mockResolvedValue([mockStageToRemove])
      cardResource.findMany.mockResolvedValue([mockCard])
      cardResource.updateById.mockResolvedValue({})
      pipelineStageRepository.destroy.mockResolvedValue({})
    })

    afterEach(() => {
      jest.clearAllMocks()
    })

    it('should call validations with correct params', async () => {
      await pipelineRes.update('pipeline-id', { stages: mockStages }, mockOptions)
      expect(pipelineRes.validations).toHaveBeenCalledWith({ stages: mockStages }, 'pipeline-id')
    })

    it('should update pipeline and set departments', async () => {
      await pipelineRes.update('pipeline-id', { stages: mockStages, departments: mockDepartments }, mockOptions)
      expect(departmentResource.findManyByIds).toHaveBeenCalledWith(['dep-1'], {})
      expect(mockPipeline.setDepartments).toHaveBeenCalledWith(mockDepartments, { transaction: mockTransaction })
    })

    it('should create new stages if stage.new is true', async () => {
      await pipelineRes.update('pipeline-id', { stages: mockStages }, mockOptions)
      expect(pipelineStageRepository.create).toHaveBeenCalledWith(
        expect.objectContaining({ name: 'Stage 2', position: 2, pipelineId: mockPipeline.id }),
        { transaction: mockTransaction },
      )
    })

    it('should update existing stages if stage.new is false', async () => {
      await pipelineRes.update('pipeline-id', { stages: mockStages }, mockOptions)
      expect(pipelineStageRepository.findById).toHaveBeenCalledWith('stage-1', { transaction: mockTransaction })
      expect(pipelineStageRepository.update).toHaveBeenCalled()
    })

    it('should remove stages not present in update data and move cards', async () => {
      await pipelineRes.update(
        'pipeline-id',
        { stages: [{ id: 'stage-1', new: false }], userId: 'user-1' },
        mockOptions,
      )
      expect(pipelineStageRepository.findMany).toHaveBeenCalledWith({
        where: { pipelineId: 'pipeline-id', id: { $notIn: ['stage-1'] } },
      })
      expect(cardResource.findMany).toHaveBeenCalledWith({
        where: { pipelineId: 'pipeline-id', pipelineStageId: { $in: [mockStageToRemove.id] } },
      })
      expect(cardResource.updateById).toHaveBeenCalledWith(
        mockCard.id,
        { userId: 'user-1', pipelineStageId: 'stage-1' },
        { transaction: mockTransaction },
      )
    })

    it('should destroy removed stages', async () => {
      await pipelineRes.update('pipeline-id', { stages: [{ id: 'stage-1', new: false }] }, mockOptions)
      expect(pipelineStageRepository.destroy).toHaveBeenCalledWith(mockStageToRemove, { transaction: mockTransaction })
    })

    it('should emitUpdated unless dontEmit is true', async () => {
      await pipelineRes.update('pipeline-id', { stages: mockStages }, mockOptions)
      expect(pipelineRes.emitUpdated).toHaveBeenCalledWith(mockPipeline)
      pipelineRes.emitUpdated.mockClear() // Limpa o histórico de chamadas
      await pipelineRes.update('pipeline-id', { stages: mockStages }, { ...mockOptions, dontEmit: true })
      expect(pipelineRes.emitUpdated).not.toHaveBeenCalledWith(mockPipeline)
    })

    it('should handle empty stages array gracefully', async () => {
      await expect(pipelineRes.update('pipeline-id', { stages: [] }, mockOptions)).resolves.toEqual(mockPipeline)
    })
  })

  describe('destroyStatus', () => {
    it('should return success', async () => {
      pipelineStageStatusRepository.findById.mockResolvedValue(mockStatus)
      expect(await pipelineRes.destroyStatus('id', { action: 'keep-without-status' })).toBe('MESSAGE_STATUS_DELETED')
    })

    it('should return success when status was renamed', async () => {
      pipelineStageStatusRepository.findById.mockResolvedValue(mockStatus)
      expect(await pipelineRes.destroyStatus('id', { newStatusName: 'UNKNOWN', action: 'UNKNOWN' })).toBe(
        'MESSAGE_STATUS_RENAMED',
      )
    })

    it('should throw exception when newStatusName is undefined', async () => {
      pipelineStageStatusRepository.findById.mockResolvedValue(mockStatus)
      await expect(pipelineRes.destroyStatus('id', {})).rejects.toThrow('ERROR_NO_NEW_STATUS_NAME')
    })
  })

  describe('getCreatedFilter', () => {
    let localPipelineRes: PipelineResource

    beforeEach(() => {
      localPipelineRes = pipelineResource
    })

    it('should return filter for both createdFrom and createdUntil', () => {
      const query = {
        createdFrom: '2024-06-01',
        createdUntil: '2024-06-10',
      }
      const result = localPipelineRes.getCreatedFilter(query)
      expect(result).toBe(`AND card."createdAt" between '2024-06-01 00:00:00' AND '2024-06-10 23:59:59'`)
    })

    it('should return filter for only createdFrom', () => {
      const query = {
        createdFrom: '2024-06-01',
      }
      const result = localPipelineRes.getCreatedFilter(query)
      expect(result).toBe(`AND card."createdAt" >= '2024-06-01 00:00:00'`)
    })

    it('should return filter for only createdUntil', () => {
      const query = {
        createdUntil: '2024-06-10',
      }
      const result = localPipelineRes.getCreatedFilter(query)
      expect(result).toBe(`AND card."createdAt" <= '2024-06-10 23:59:59'`)
    })

    it('should return empty string if neither createdFrom nor createdUntil', () => {
      const query = {}
      const result = localPipelineRes.getCreatedFilter(query)
      expect(result).toBe('')
    })
  })

  describe('getQueryTotalCards', () => {
    let pipelineRes: PipelineResource

    beforeEach(() => {
      pipelineRes = pipelineResource
    })

    it('should return SQL string with no filters', async () => {
      const result = await pipelineRes.getQueryTotalCards({})
      expect(result).toContain('SELECT COUNT(*) as totalCards')
      expect(result).not.toContain('INNER JOIN pipeline.stage_status')
      expect(result).not.toContain('AND contact."serviceId"')
      expect(result).not.toContain('AND (SELECT mov."userId"')
    })

    it('should include status filter in SQL', async () => {
      const query = { status: { id: 'STATUS_WON' } }
      const result = await pipelineRes.getQueryTotalCards(query)
      expect(result).toContain(
        'INNER JOIN pipeline.stage_status AS ss ON ss.id = card."statusId" AND ss.name=\'STATUS_WON\'',
      )
    })

    it('should include service filter in SQL', async () => {
      const query = { service: { id: 'service-123' } }
      const result = await pipelineRes.getQueryTotalCards(query)
      expect(result).toContain('AND contact."serviceId"=\'service-123\'')
    })

    it('should include user filter in SQL', async () => {
      const query = { owner: { id: 'user-456' } }
      const result = await pipelineRes.getQueryTotalCards(query)
      expect(result).toContain('AND card."ownerId" = \'user-456\'')
    })

    it('should include createdFrom and createdUntil filter in SQL', async () => {
      const query = { createdFrom: '2024-06-01', createdUntil: '2024-06-10' }
      const result = await pipelineRes.getQueryTotalCards(query)
      expect(result).toContain("AND card.\"createdAt\" between '2024-06-01 00:00:00' AND '2024-06-10 23:59:59'")
    })

    it('should include only createdFrom filter in SQL', async () => {
      const query = { createdFrom: '2024-06-01' }
      const result = await pipelineRes.getQueryTotalCards(query)
      expect(result).toContain('AND card."createdAt" >= \'2024-06-01 00:00:00\'')
    })

    it('should include only createdUntil filter in SQL', async () => {
      const query = { createdUntil: '2024-06-10' }
      const result = await pipelineRes.getQueryTotalCards(query)
      expect(result).toContain('AND card."createdAt" <= \'2024-06-10 23:59:59\'')
    })
  })

  describe('getQueryTotalCardsValue', () => {
    let pipelineRes: PipelineResource

    beforeEach(() => {
      pipelineRes = pipelineResource
    })

    it('should return SQL string with no filters', async () => {
      const result = await pipelineRes.getQueryTotalCardsValue({})
      expect(result).toContain('SELECT COALESCE(SUM(pcp.ammount * pcp.value), 0) AS totalValue')
      expect(result).not.toContain('INNER JOIN pipeline.stage_status AS ss')
      expect(result).not.toContain('AND contact."serviceId"=')
      expect(result).not.toContain('AND (SELECT mov."userId"')
      expect(result).not.toContain('AND card."success" = true')
      expect(result).not.toContain('AND (card."success" IS NULL OR card."success" = false)')
    })

    it('should include status filter in SQL', async () => {
      const result = await pipelineRes.getQueryTotalCardsValue({ status: { id: 'STATUS_WON' } })
      expect(result).toContain(
        'INNER JOIN pipeline.stage_status AS ss ON ss.id = card."statusId" AND ss.name=\'STATUS_WON\'',
      )
    })

    it('should include service filter in SQL', async () => {
      const result = await pipelineRes.getQueryTotalCardsValue({ service: { id: 'service-1' } })
      expect(result).toContain('AND contact."serviceId"=\'service-1\'')
    })

    it('should include user filter in SQL', async () => {
      const result = await pipelineRes.getQueryTotalCardsValue({ owner: { id: 'user-1' } })
      expect(result).toContain('AND card."ownerId" = \'user-1\'')
    })

    it('should include onlyWon filter in SQL', async () => {
      const result = await pipelineRes.getQueryTotalCardsValue({}, true)
      expect(result).toContain('AND card."success" = true')
    })

    it('should include onlyLost filter in SQL', async () => {
      const result = await pipelineRes.getQueryTotalCardsValue({}, false, true)
      expect(result).toContain('AND (card."success" IS NULL OR card."success" = false)')
    })

    it('should combine filters correctly', async () => {
      const result = await pipelineRes.getQueryTotalCardsValue(
        { status: { id: 'STATUS_WON' }, service: { id: 'service-1' }, owner: { id: 'user-1' } },
        true,
        false,
      )
      expect(result).toContain(
        'INNER JOIN pipeline.stage_status AS ss ON ss.id = card."statusId" AND ss.name=\'STATUS_WON\'',
      )
      expect(result).toContain('AND contact."serviceId"=\'service-1\'')
      expect(result).toContain('AND card."ownerId" = \'user-1\'')
      expect(result).toContain('AND card."success" = true')
      expect(result).not.toContain('AND (card."success" IS NULL OR card."success" = false)')
    })
  })

  describe('export', () => {
    let pipelineRes: PipelineResource
    let mockRes
    let mockStringifier
    let mockPipeline

    beforeEach(() => {
      pipelineRes = pipelineResource
      mockRes = { write: jest.fn(), end: jest.fn() }
      mockStringifier = {
        pipe: jest.fn(),
        write: jest.fn(),
        end: jest.fn(),
      }
      jest.spyOn(require('csv-stringify'), 'stringify').mockReturnValue(mockStringifier)
      mockPipeline = {
        name: 'Pipeline Test',
        stages: [
          {
            name: 'Stage 1',
            cards: [
              {
                contact: {
                  name: 'Contact 1',
                  service: { name: 'Service 1' },
                  data: { number: '123', email: '<EMAIL>' },
                },
                movements: [
                  {
                    to_pipeline_stage: { name: 'Stage 1' },
                    toStageStatusId: 1,
                    createdAt: new Date(),
                  },
                ],
                stage_status: { name: 'UNKNOWN' },
                stage_reason: { name: 'Reason 1' },
                description: 'Description\nLine2',
                products: [{ ammount: 2, value: 10 }],
                organization: 'Org 1',
                organizationSegment: 'Seg 1',
                originChannel: 'Channel 1',
                originCampaign: 'Campaign 1',
                createdAt: new Date('2024-06-01T10:00:00Z'),
                success: true,
              },
            ],
            statuses: [{ id: 1, name: 'STATUS_WON' }],
          },
        ],
      }
      jest.spyOn(BaseResource.prototype, 'findOne').mockResolvedValue(mockPipeline)
    })

    afterEach(() => {
      jest.restoreAllMocks()
    })

    it('should write summary and rows for pt-BR', async () => {
      const user = { language: 'pt-BR', account: { settings: { timezone: 'America/Sao_Paulo' } } }
      const req = { body: { createdAtInit: '2024-06-01', createdAtFinal: '2024-06-10' } }
      await pipelineRes.export({ id: 'pipeline-id', req, res: mockRes, user })
      expect(mockStringifier.pipe).toHaveBeenCalledWith(mockRes)
      expect(mockStringifier.write).toHaveBeenCalledWith(['Pipeline Test'])
      expect(mockStringifier.write).toHaveBeenCalledWith(
        expect.arrayContaining(['Período: De 01/06/2024, até 10/06/2024']),
      )
      expect(mockStringifier.write).toHaveBeenCalledWith(
        expect.arrayContaining(['POTENCIAL DE RECEITA', 'RECEITA GANHA', 'CONVERSÃO GERAL']),
      )
      expect(mockStringifier.write).toHaveBeenCalledWith(
        expect.arrayContaining([
          expect.stringMatching(/^R\$/), // currency string
          expect.stringMatching(/^R\$/),
          expect.stringMatching(/%/),
        ]),
      )
      expect(mockStringifier.write).toHaveBeenCalledWith(['Pipeline Test'])
      expect(mockStringifier.write).toHaveBeenCalledWith(
        expect.arrayContaining(['POTENCIAL DE RECEITA', 'RECEITA GANHA', 'CONVERSÃO GERAL']),
      )
      const calls = mockStringifier.write.mock.calls.flat()
      expect(calls).toEqual(
        expect.arrayContaining([
          expect.arrayContaining([
            'Contact 1',
            'Service 1',
            expect.stringMatching(/^123, ?test@email\.com$/),
            'Stage 1',
            'UNKNOWN',
            'Reason 1',
            'Description|||Line2',
            expect.stringMatching(/^R\$/),
            'Org 1',
            'Seg 1',
            'Channel 1',
            'Campaign 1',
            expect.any(String),
            expect.any(String),
          ]),
        ]),
      )
      expect(mockStringifier.end).toHaveBeenCalled()
    })

    it('should write summary and rows for en-US', async () => {
      const user = { language: 'en-US', account: { settings: { timezone: 'America/New_York' } } }
      const req = { body: { createdAtInit: '2024-06-01', createdAtFinal: '2024-06-10' } }
      await pipelineRes.export({ id: 'pipeline-id', req, res: mockRes, user })
      expect(mockStringifier.write).toHaveBeenCalledWith(
        expect.arrayContaining(['POTENTIAL REVENUE', 'REVENUE EARNED', 'OVERALL CONVERSION']),
      )
      expect(mockStringifier.write).toHaveBeenCalledWith(
        expect.arrayContaining([
          'OPPORTUNITY',
          'CONNECTION',
          'CONTACT',
          'STAGE',
          'STATUS',
          'REASON',
          'DESCRIPTION',
          'TOTAL OPPORTUNITY VALUE',
          'COMPANY NAME',
          'COMPANY SEGMENT',
          'SOURCE CHANNEL',
          'SOURCE CAMPAIGN',
          'OPPORTUNITY CREATION DATE',
          'OWNER',
        ]),
      )
    })

    it('should write summary and rows for es', async () => {
      const user = { language: 'es', account: { settings: { timezone: 'America/Santiago' } } }
      const req = { body: { createdAtInit: '2024-06-01', createdAtFinal: '2024-06-10' } }
      await pipelineRes.export({ id: 'pipeline-id', req, res: mockRes, user })
      expect(mockStringifier.write).toHaveBeenCalledWith(
        expect.arrayContaining(['POTENCIAL DE INGRESOS', 'INGRESOS GANADOS', 'CONVERSIÓN GENERAL']),
      )
      expect(mockStringifier.write).toHaveBeenCalledWith(
        expect.arrayContaining([
          'OPORTUNIDAD',
          'CONEXIÓN',
          'CONTACTO',
          'ETAPA',
          'ESTADO',
          'MOTIVO',
          'DESCRIPCIÓN',
          'VALOR TOTAL DE LA OPORTUNIDAD',
          'NOMBRE DE LA EMPRESA',
          'SEGMENTO DE LA EMPRESA',
          'CANAL DE ORIGEN',
          'CAMPAÑA DE ORIGEN',
          'FECHA DE CREACIÓN DE LA OPORTUNIDAD',
          'RESPONSABLE',
        ]),
      )
    })
  })

  describe('findStatusById', () => {
    let pipelineRes: PipelineResource

    beforeEach(() => {
      pipelineRes = pipelineResource
      jest.clearAllMocks()
    })

    it('should return status data and cardsCount', async () => {
      const mockStatus = {
        dataValues: {
          id: 'status-1',
          name: 'Test Status',
          extra: 'value',
        },
      }
      pipelineStageStatusRepository.findById.mockResolvedValue(mockStatus)
      cardResource.count.mockResolvedValue(5)

      const result = await pipelineRes.findStatusById('status-1')
      expect(result).toEqual({
        id: 'status-1',
        name: 'Test Status',
        extra: 'value',
        cardsCount: 5,
      })
      expect(pipelineStageStatusRepository.findById).toHaveBeenCalledWith('status-1')
      expect(cardResource.count).toHaveBeenCalledWith({ where: { statusId: 'status-1' } })
    })

    it('should handle missing status gracefully', async () => {
      pipelineStageStatusRepository.findById.mockResolvedValue(undefined)
      cardResource.count.mockResolvedValue(0)

      const result = await pipelineRes.findStatusById('status-2')
      expect(result).toEqual({
        cardsCount: 0,
      })
      expect(pipelineStageStatusRepository.findById).toHaveBeenCalledWith('status-2')
      expect(cardResource.count).toHaveBeenCalledWith({ where: { statusId: 'status-2' } })
    })
  })

  describe('findById', () => {
    let pipelineRes: PipelineResource

    beforeEach(() => {
      pipelineRes = pipelineResource
      jest.spyOn(BaseResource.prototype, 'findOne').mockResolvedValue({ id: 'pipeline-id', name: 'Pipeline Test' })
      jest.spyOn(pipelineRes, 'getQueryTotalCards').mockResolvedValue('SELECT COUNT(*) FROM cards')
      jest
        .spyOn(pipelineRes, 'getQueryTotalCardsValue')
        .mockResolvedValueOnce('SELECT SUM(value) FROM cards')
        .mockResolvedValueOnce('SELECT SUM(value) FROM cards WHERE won=true')
        .mockResolvedValueOnce('SELECT SUM(value) FROM cards WHERE lost=true')
    })

    afterEach(() => {
      jest.clearAllMocks()
    })

    it('should call super.findOne with correct params (no query)', async () => {
      await pipelineRes.findById('pipeline-id')
      expect(BaseResource.prototype.findOne).toHaveBeenCalledWith(
        expect.objectContaining({
          where: { id: 'pipeline-id' },
          include: [
            expect.objectContaining({
              model: 'stages',
              include: [
                {
                  model: 'statuses',
                  order: ['position'],
                },
                {
                  model: 'reasons',
                  order: ['position'],
                },
              ],
              attributes: {
                include: [
                  [expect.any(Object), 'totalCards'],
                  [expect.any(Object), 'totalCardsValue'],
                  [expect.any(Object), 'totalWonCardsValue'],
                  [expect.any(Object), 'totalLostCardsValue'],
                ],
              },
              order: [['position', 'ASC']],
            }),
          ],
        }),
      )
    })

    it('should call getQueryTotalCards and getQueryTotalCardsValue with query', async () => {
      const query = { status: { id: 'STATUS_WON' }, user: { id: 'user-1' } }
      jest
        .spyOn(pipelineRes, 'getQueryTotalCards')
        .mockResolvedValue('SELECT COUNT(*) FROM cards WHERE status=STATUS_WON')
      jest
        .spyOn(pipelineRes, 'getQueryTotalCardsValue')
        .mockResolvedValueOnce('SELECT SUM(value) FROM cards WHERE status=STATUS_WON')
        .mockResolvedValueOnce('SELECT SUM(value) FROM cards WHERE status=STATUS_WON AND won=true')
        .mockResolvedValueOnce('SELECT SUM(value) FROM cards WHERE status=STATUS_WON AND lost=true')

      await pipelineRes.findById('pipeline-id', query)

      expect(pipelineRes.getQueryTotalCards).toHaveBeenCalledWith(query)
      expect(pipelineRes.getQueryTotalCardsValue).toHaveBeenNthCalledWith(1, query)
      expect(pipelineRes.getQueryTotalCardsValue).toHaveBeenNthCalledWith(2, query, true)
      expect(pipelineRes.getQueryTotalCardsValue).toHaveBeenNthCalledWith(3, query, false, true)
    })

    it('should return the result from super.findOne', async () => {
      const result = await pipelineRes.findById('pipeline-id')
      expect(result).toEqual({ id: 'pipeline-id', name: 'Pipeline Test' })
    })
  })

  describe('getStageTotals', () => {
    let pipelineRes: PipelineResource

    beforeEach(() => {
      pipelineRes = pipelineResource
      ;(pipelineStageRepository as any).findOne = jest.fn().mockResolvedValue({ id: 'stage-id', totalCards: 10 })
      jest.spyOn(pipelineRes, 'getQueryTotalCards').mockResolvedValue('SELECT COUNT(*) FROM cards')
      jest
        .spyOn(pipelineRes, 'getQueryTotalCardsValue')
        .mockResolvedValueOnce('SELECT SUM(value) FROM cards')
        .mockResolvedValueOnce('SELECT SUM(value) FROM cards WHERE won=true')
        .mockResolvedValueOnce('SELECT SUM(value) FROM cards WHERE lost=true')
    })

    afterEach(() => {
      jest.clearAllMocks()
    })

    it('should call repository with correct attributes and where (no query)', async () => {
      const result = await pipelineRes.getStageTotals('stage-id')

      expect((pipelineStageRepository as any).findOne).toHaveBeenCalledWith(
        expect.objectContaining({
          attributes: {
            include: [
              [expect.any(Object), 'totalCards'],
              [expect.any(Object), 'totalCardsValue'],
              [expect.any(Object), 'totalWonCardsValue'],
              [expect.any(Object), 'totalLostCardsValue'],
            ],
          },
          where: { id: 'stage-id' },
        }),
      )
      expect(result).toEqual({ id: 'stage-id', totalCards: 10 })
    })

    it('should build SQL with provided query filters', async () => {
      jest.clearAllMocks()
      ;(pipelineStageRepository as any).findOne = jest.fn().mockResolvedValue({ id: 'stage-id' })
      const query = { status: { id: 'STATUS_WON' }, service: { id: 'service-1' }, owner: { id: 'user-1' } }

      jest.spyOn(pipelineRes, 'getQueryTotalCards').mockResolvedValue('SELECT COUNT(*) FROM cards WHERE ...')
      jest
        .spyOn(pipelineRes, 'getQueryTotalCardsValue')
        .mockResolvedValueOnce('SELECT SUM(value) FROM cards WHERE ...')
        .mockResolvedValueOnce('SELECT SUM(value) FROM cards WHERE ... AND won=true')
        .mockResolvedValueOnce('SELECT SUM(value) FROM cards WHERE ... AND lost=true')

      await pipelineRes.getStageTotals('stage-id', query)

      expect(pipelineRes.getQueryTotalCards).toHaveBeenCalledWith(query)
      expect(pipelineRes.getQueryTotalCardsValue).toHaveBeenNthCalledWith(1, query)
      expect(pipelineRes.getQueryTotalCardsValue).toHaveBeenNthCalledWith(2, query, true)
      expect(pipelineRes.getQueryTotalCardsValue).toHaveBeenNthCalledWith(3, query, false, true)
      expect((pipelineStageRepository as any).findOne).toHaveBeenCalledWith(
        expect.objectContaining({ where: { id: 'stage-id' } }),
      )
    })
  })
})
