jest.mock('../../../../core/dbSequelize/repositories/serviceRepository', () => jest.fn())
jest.mock('../../../../core/dbSequelize/repositories/ServicesHistoryRepository', () => jest.fn())
jest.mock('../../../../core/services/driverService', () => jest.fn())
jest.mock('../../../../core/dbSequelize/models/Service', () => jest.fn())
jest.mock('../../../../core/dbSequelize/models/Account', () => jest.fn())
jest.mock('../../../../core/resources/contactResource', () => jest.fn())
jest.mock('../../../../core/resources/messageResource', () => jest.fn())
jest.mock('../../../../core/resources/scheduleResource', () => jest.fn())
jest.mock('../../../../core/resources/campaignResource', () => jest.fn())
jest.mock('../../../../core/resources/customFieldsResource', () => jest.fn())
jest.mock('../../../../core/resources/fileResource', () => jest.fn())
jest.mock('../../../../microServices/workers/jobs/facebookMessenger/driver/baseFunctions', () => {
  return {
    getWebhookInGatewayByUrl: jest.fn(() => {
      return { id: 'wh-1', url: 'https://public.example/messenger-webhook/svc-1' }
    }),
    deleteWebhookInGateway: jest.fn(),
  }
})
jest.mock('../../../../core/utils/iteratePaginated', () => jest.fn())
jest.mock('../../../../core/resources/serverPodResource', () => jest.fn())
jest.mock('../../../../core/services/logs/reportError', () => jest.fn())
jest.mock('../../../../core/transformers/fileTransformer', () => jest.fn())
jest.mock('../../../../microServices/workers/jobs/account/updateContractPlan/Types', () => jest.fn())
jest.mock('../../../../core/configValues', () => {
  return { __esModule: true, default: { workersGoServices: [] } }
})
jest.mock('typedi', () => jest.fn())
jest.mock('../../../../core/services/jobs/http/HttpJobsDispatcher', () => jest.fn())
jest.mock('../../../../core/config', () => {
  const mockFn = jest.fn((key: string) => {
    if (key === 'emitterMaxListeners') return 50
    return undefined
  })
  return mockFn
})
jest.mock('../../../../core/resources/BaseResource', () => {
  class MockBase {
    private _events: Record<string, Function[]> = {}

    getEvents() {
      return []
    }

    getRepository() {
      return {
        findOne: jest.fn(),
        findById: jest.fn(),
        findMany: jest.fn(),
        create: () => ({ setDepartments: jest.fn() }),
        update: jest.fn(),
        destroy: jest.fn(),
        updateById: () => ({ setDepartments: jest.fn() }),
      }
    }

    on(event: string, listener: (...args: any[]) => void) {
      if (!this._events[event]) this._events[event] = []
      this._events[event].push(listener)
      return this
    }

    emit(event: string, ...args: any[]) {
      ;(this._events[event] || []).forEach((l) => l(...args))
      return true
    }

    findOne() {
      return Promise.resolve(null)
    }

    findById() {
      return Promise.resolve({
        id: '123',
        data: { providerType: 'meta', status: { isConnected: true, isStarted: false, isStarting: false } },
      })
    }

    create() {
      return Promise.resolve(null)
    }

    update() {
      return Promise.resolve(null)
    }

    emitUpdated() {
      return Promise.resolve(null)
    }
  }

  return {
    __esModule: true,
    default: MockBase,
    CREATED: 'CREATED',
    UPDATED: 'UPDATED',
    DESTROYED: 'DESTROYED',
  }
})

import serviceResource, { ARCHIVED, TYPES, ServiceType } from '../../../../core/resources/serviceResource'
import driverService from '../../../../core/services/driverService'
import configValues from '../../../../core/configValues'
import ServicesHistoryRepository from '../../../../core/dbSequelize/repositories/ServicesHistoryRepository'
import Container from 'typedi'
import contactResource from '../../../../core/resources/contactResource'
import messageResource from '../../../../core/resources/messageResource'
import serverPodResource from '../../../../core/resources/serverPodResource'
import customFieldsResource from '../../../../core/resources/customFieldsResource'
import fileResource from '../../../../core/resources/fileResource'
import fileTransformer from '../../../../core/transformers/fileTransformer'
const baseResourceModule = require('../../../../core/resources/BaseResource')
const iteratePaginated = require('../../../../core/utils/iteratePaginated')
import {
  getWebhookInGatewayByUrl,
  deleteWebhookInGateway,
} from '../../../../microServices/workers/jobs/facebookMessenger/driver/baseFunctions'
const scheduleResource = require('../../../../core/resources/scheduleResource')
const campaignsResource = require('../../../../core/resources/campaignResource')

describe('serviceResource TYPES constant', () => {
  test('TYPES should be a non-empty array of strings', () => {
    expect(Array.isArray(TYPES)).toBe(true)
    expect(TYPES.length).toBeGreaterThan(0)
    expect(TYPES.every((t) => typeof t === 'string')).toBe(true)
  })

  test('TYPES should not contain duplicate values', () => {
    const unique = new Set(TYPES)
    expect(unique.size).toBe(TYPES.length)
  })

  test('TYPES should contain exactly the same values as ServiceType enum', () => {
    const enumValues = Object.values(ServiceType)
    const setA = new Set(TYPES)
    const setB = new Set(enumValues)
    expect(setA.size).toBe(setB.size)
    for (const val of setA) {
      expect(setB.has(val)).toBe(true)
    }
  })
})

describe('ServiceResource.emitArchived', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  test('calls underlying emit with ARCHIVED, data and eventTransaction and returns result', async () => {
    const payload = { id: 'svc-1', foo: 'bar' }
    const eventTransaction = { events: [], pushEmit: jest.fn() }
    const emitSpy = jest.spyOn(serviceResource as any, 'emit')
    const result = await serviceResource.emitArchived(payload, eventTransaction)
    expect(emitSpy).toHaveBeenCalledTimes(1)
    expect(emitSpy).toHaveBeenCalledWith(ARCHIVED, payload, eventTransaction)
    expect(result).toBe(true)
  })

  test('calls emit with undefined eventTransaction when not provided', async () => {
    const payload = { id: 'svc-2' }
    const emitSpy = jest.spyOn(serviceResource as any, 'emit')
    const result = await serviceResource.emitArchived(payload)
    expect(emitSpy).toHaveBeenCalledWith(ARCHIVED, payload, undefined)
    expect(result).toBe(true)
  })

  test('events list contains ARCHIVED', () => {
    expect(serviceResource.events).toEqual(expect.arrayContaining([ARCHIVED]))
  })
})

describe('ServiceResource.onArchived', () => {
  afterEach(() => {
    jest.restoreAllMocks()
  })

  it('must register listener by calling .on with the correct event', () => {
    const listener = jest.fn()
    const onSpy = jest.spyOn(serviceResource as any, 'on')
    serviceResource.onArchived(listener)
    expect(onSpy).toHaveBeenCalledTimes(1)
    expect(onSpy).toHaveBeenCalledWith(ARCHIVED, listener)
  })

  it('must run the listener when emitArchived is called', async () => {
    const listener = jest.fn()
    serviceResource.onArchived(listener)
    const payload = { id: 'service-id', foo: 'bar' }
    await serviceResource.emitArchived(payload)
    expect(listener).toHaveBeenCalledTimes(1)
    expect(listener).toHaveBeenCalledWith(payload, undefined)
  })
})

describe('ServiceResource.update', () => {
  const buildModel = (overrides = {}) => ({
    id: 'svc-1',
    type: 'whatsapp',
    data: {
      providerType: 'other',
      status: { isConnected: false },
    },
    dataValues: {
      id: 'svc-1',
      type: 'whatsapp',
      data: {
        providerType: 'other',
        status: { isConnected: false },
        webchat: { existing: 'val', custom: { logo: 'logo-old' } },
      },
      settings: { oldSetting: 1 },
    },
    ...overrides,
  })

  afterEach(() => {
    jest.restoreAllMocks()
  })

  test('throws error when duplicate facebook/instagram service detected', async () => {
    const findOneSpy = jest.spyOn(serviceResource as any, 'findOne').mockResolvedValue({ id: 'duplicate' })
    const model = buildModel({
      id: 'svc-fb-1',
      type: 'facebook-messenger',
      data: { providerType: 'other', status: { isConnected: false } },
      dataValues: {
        id: 'svc-fb-1',
        type: 'facebook-messenger',
        data: { providerType: 'other', status: { isConnected: false } },
        settings: {},
      },
    })
    const payload = { data: { driverId: 'driver-1', pageId: 'page-1' } }

    await expect(serviceResource.update(model as any, payload as any)).rejects.toThrow(
      'Service already exists with driverId driver-1',
    )
    expect(findOneSpy).toHaveBeenCalled()
  })

  test('merges data, webchat and settings and calls createHistory and super.update (preserves original fields)', async () => {
    jest.spyOn(serviceResource as any, 'findOne').mockResolvedValue(null)
    const createHistorySpy = jest.spyOn(serviceResource as any, 'createHistory').mockResolvedValue(undefined as any)
    const baseUpdateSpy = jest
      .spyOn(Object.getPrototypeOf(Object.getPrototypeOf(serviceResource)), 'update')
      .mockResolvedValue('UPDATED' as any)

    const model = {
      id: 'svc-1',
      type: 'whatsapp',
      data: {
        providerType: 'other',
        status: { isConnected: false },
        existing: 'x',
        webchat: { existing: 'a', custom: { logo: 'logo-old' } },
      },
      dataValues: {
        id: 'svc-1',
        type: 'whatsapp',
        data: {
          providerType: 'other',
          status: { isConnected: false },
          existing: 'x',
          webchat: { existing: 'a', custom: { logo: 'logo-old' } },
        },
        settings: { oldSetting: 1 },
      },
    }

    const payload = {
      data: {
        newField: 'new',
        webchat: { custom: { newValue: 'nv' } },
      },
      settings: { newSetting: 2 },
    }

    const result = await serviceResource.update(model as any, payload as any)

    expect(createHistorySpy).toHaveBeenCalledTimes(1)
    const mergedArg = createHistorySpy.mock.calls[0][1]

    expect((mergedArg as any).data.existing).toBe('x')
    expect((mergedArg as any).data.newField).toBe('new')
    expect((mergedArg as any).data.webchat.existing).toBe('a')
    expect((mergedArg as any).data.webchat.custom).toEqual({ newValue: 'nv' })
    expect((mergedArg as any).settings).toEqual({ oldSetting: 1, newSetting: 2 })
    expect(baseUpdateSpy).toHaveBeenCalledWith(model, mergedArg, {})
    expect(result).toBe('UPDATED')
  })

  test('update does not call shutdown or dispatch if type does not change or not connected', async () => {
    driverService.shutdown = jest.fn()
    Object.defineProperty(configValues, 'workersGoServices', {
      value: ['whatsapp-business'],
      writable: false,
      configurable: true,
    })

    const model = {
      id: 'svc-4',
      type: 'whatsapp',
      data: { providerType: 'other', status: { isConnected: false } },
      dataValues: {
        id: 'svc-4',
        type: 'whatsapp',
        data: { providerType: 'other', status: { isConnected: false } },
        settings: {},
      },
    }
    const payload = { type: 'whatsapp', data: {} }

    await serviceResource.update(model as any, payload as any)
    expect(driverService.shutdown).not.toHaveBeenCalled()
  })

  test('update throws error if duplicate service found for facebook-messenger', async () => {
    jest.spyOn(serviceResource as any, 'findOne').mockResolvedValue({ id: 'dup' })
    const model = {
      id: 'svc-5',
      type: 'facebook-messenger',
      data: { providerType: 'other', status: { isConnected: false } },
      dataValues: {
        id: 'svc-5',
        type: 'facebook-messenger',
        data: { providerType: 'other', status: { isConnected: false } },
        settings: {},
      },
    }
    const payload = { data: { driverId: 'driver-5', pageId: 'page-5' } }

    await expect(serviceResource.update(model as any, payload as any)).rejects.toThrow(
      'Service already exists with driverId driver-5',
    )
  })

  test('update throws error if duplicate service found for meta provider', async () => {
    jest.spyOn(serviceResource as any, 'findOne').mockResolvedValue({ id: 'dup' })
    const model = {
      id: 'svc-6',
      type: 'whatsapp',
      data: { providerType: 'meta', status: { isConnected: false } },
      dataValues: {
        id: 'svc-6',
        type: 'whatsapp',
        data: { providerType: 'meta', status: { isConnected: false } },
        settings: {},
      },
    }
    const payload = { data: { driverId: 'driver-6', numberId: 'num-6', businessId: 'biz-6' } }

    await expect(serviceResource.update(model as any, payload as any)).rejects.toThrow(
      'Service already exists with driverId driver-6',
    )
  })

  test('update merges deeply nested webchat fields', async () => {
    jest.spyOn(serviceResource as any, 'findOne').mockResolvedValue(null)
    const createHistorySpy = jest.spyOn(serviceResource as any, 'createHistory').mockResolvedValue(undefined as any)
    const baseUpdateSpy = jest
      .spyOn(Object.getPrototypeOf(Object.getPrototypeOf(serviceResource)), 'update')
      .mockResolvedValue('UPDATED' as any)

    const model = {
      id: 'svc-7',
      type: 'webchat',
      data: {
        providerType: 'other',
        status: { isConnected: false },
        webchat: { existing: 'old', custom: { logo: 'logo-old', color: 'blue' } },
      },
      dataValues: {
        id: 'svc-7',
        type: 'webchat',
        data: {
          providerType: 'other',
          status: { isConnected: false },
          webchat: { existing: 'old', custom: { logo: 'logo-old', color: 'blue' } },
        },
        settings: { oldSetting: 1 },
      },
    }

    const payload = {
      data: {
        webchat: { custom: { logo: 'logo-new', icon: 'icon-new' } },
      },
      settings: { newSetting: 3 },
    }

    const result = await serviceResource.update(model as any, payload as any)

    expect(createHistorySpy).toHaveBeenCalledTimes(1)
    const mergedArg = createHistorySpy.mock.calls[0][1]
    expect((mergedArg as any).data.webchat.existing).toBe('old')
    expect((mergedArg as any).data.webchat.custom).toEqual({ logo: 'logo-new', icon: 'icon-new' })
    expect((mergedArg as any).settings).toEqual({ oldSetting: 1, newSetting: 3 })
    expect(baseUpdateSpy).toHaveBeenCalledWith(model, mergedArg, {})
    expect(result).toBe('UPDATED')
  })
})

describe('ServiceResource.update - type change + connected (shutdown branch)', () => {
  beforeEach(() => {
    jest.restoreAllMocks()
  })

  test('calls driverService.shutdown when type changes and service is connected (non workersGo service)', async () => {
    jest.spyOn(serviceResource as any, 'findOne').mockResolvedValue(null)
    const createHistorySpy = jest.spyOn(serviceResource as any, 'createHistory').mockResolvedValue(undefined as any)
    const baseUpdateSpy = jest
      .spyOn(Object.getPrototypeOf(Object.getPrototypeOf(serviceResource)), 'update')
      .mockResolvedValue('UPDATED' as any)

    // Garante que o tipo atual NÃO está em workersGoServices
    Object.defineProperty(configValues, 'workersGoServices', {
      value: [],
      writable: true,
      configurable: true,
    })

    driverService.shutdown = jest.fn().mockResolvedValue(undefined)

    const model = {
      id: 'svc-shutdown-1',
      type: 'whatsapp',
      data: { providerType: 'other', status: { isConnected: true } },
      dataValues: {
        id: 'svc-shutdown-1',
        type: 'whatsapp',
        data: { providerType: 'other', status: { isConnected: true }, webchat: {} },
        settings: {},
      },
    }

    const payload = { type: 'webchat', data: {} }

    const result = await serviceResource.update(model as any, payload as any)

    expect(driverService.shutdown).toHaveBeenCalledTimes(1)
    expect(driverService.shutdown).toHaveBeenCalledWith('svc-shutdown-1')
    expect(createHistorySpy).toHaveBeenCalledTimes(1)
    expect(baseUpdateSpy).toHaveBeenCalledTimes(1)
    expect(result).toBe('UPDATED')
  })

  test('dispatches shutdown via HttpJobsDispatcher when type changes and service is connected (workersGo service)', async () => {
    jest.spyOn(serviceResource as any, 'findOne').mockResolvedValue(null)
    const createHistorySpy = jest.spyOn(serviceResource as any, 'createHistory').mockResolvedValue(undefined as any)
    const baseUpdateSpy = jest
      .spyOn(Object.getPrototypeOf(Object.getPrototypeOf(serviceResource)), 'update')
      .mockResolvedValue('UPDATED' as any)

    // Inclui tipo atual em workersGoServices
    Object.defineProperty(configValues, 'workersGoServices', {
      value: ['whatsapp'],
      writable: true,
      configurable: true,
    })

    const dispatchMock = jest.fn().mockResolvedValue(undefined)
    ;(Container as any).get = jest.fn(() => ({ dispatch: dispatchMock }))

    driverService.shutdown = jest.fn() // não deve ser chamado

    const model = {
      id: 'svc-dispatch-1',
      type: 'whatsapp',
      data: { providerType: 'other', status: { isConnected: true } },
      dataValues: {
        id: 'svc-dispatch-1',
        type: 'whatsapp',
        data: { providerType: 'other', status: { isConnected: true }, webchat: {} },
        settings: {},
      },
    }

    const payload = { type: 'webchat', data: {} }

    const result = await serviceResource.update(model as any, payload as any)

    expect(Container.get).toHaveBeenCalledTimes(1)
    expect(dispatchMock).toHaveBeenCalledTimes(1)
    expect(dispatchMock.mock.calls[0][0]).toBe('shutdown')
    expect(dispatchMock.mock.calls[0][1]).toEqual({ serviceId: 'svc-dispatch-1' })
    expect(dispatchMock.mock.calls[0][2]).toEqual(
      expect.objectContaining({
        timeout: expect.any(Number),
        useWorkersGo: true,
      }),
    )
    expect(driverService.shutdown).not.toHaveBeenCalled()
    expect(createHistorySpy).toHaveBeenCalledTimes(1)
    expect(baseUpdateSpy).toHaveBeenCalledTimes(1)
    expect(result).toBe('UPDATED')
  })
})

describe('ServiceResource.createHistory', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  afterEach(() => {
    jest.restoreAllMocks()
  })

  it('should not create a history record when newData has no health property', async () => {
    const oldData = { id: 'service-1', health: { status: 'UP' } }
    const newData = { id: 'service-1' }

    ServicesHistoryRepository.create = jest.fn()

    await serviceResource.createHistory(oldData, newData)

    expect(ServicesHistoryRepository.create).not.toHaveBeenCalled()
  })

  it('should create a history record when oldData has no health and newData does', async () => {
    const oldData = { id: 'service-1' }
    const newData = { id: 'service-1', health: { status: 'UP' } }

    ServicesHistoryRepository.create = jest.fn()

    await serviceResource.createHistory(oldData, newData)

    expect(ServicesHistoryRepository.create).toHaveBeenCalledTimes(1)
    expect(ServicesHistoryRepository.create).toHaveBeenCalledWith({
      serviceId: 'service-1',
      healthFrom: JSON.stringify(undefined),
      healthTo: JSON.stringify({ status: 'UP' }),
    })
  })

  it('should not create a history record when neither oldData nor newData have health property', async () => {
    const oldData = { id: 'service-1' }
    const newData = { id: 'service-1' }

    ServicesHistoryRepository.create = jest.fn()

    await serviceResource.createHistory(oldData, newData)

    expect(ServicesHistoryRepository.create).not.toHaveBeenCalled()
  })
})

describe('ServiceResource.destroy', () => {
  const buildModel = () => ({ id: 'svc-destroy-1' })

  let baseProto: any
  beforeEach(() => {
    jest.clearAllMocks()
    baseProto = Object.getPrototypeOf(Object.getPrototypeOf(serviceResource))
    if (!baseProto.destroy) {
      baseProto.destroy = jest.fn()
    } else {
      jest.spyOn(baseProto, 'destroy')
    }
    // Ensure bulkDestroy mocks exist
    ;(contactResource as any).bulkDestroy = jest.fn()
    ;(messageResource as any).bulkDestroy = jest.fn()
  })

  afterEach(() => {
    jest.restoreAllMocks()
  })

  const mockRepositoryWithTransaction = () => {
    const repo = {
      transaction: jest.fn(async (cb) => cb('tx-token')),
    }
    jest.spyOn(serviceResource as any, 'getRepository').mockReturnValue(repo)
    return repo
  }

  test('calls bulkDestroy for contacts and messages, then super.destroy with proper options', async () => {
    const repo = mockRepositoryWithTransaction()
    ;(contactResource as any).bulkDestroy.mockResolvedValueOnce(undefined)
    ;(messageResource as any).bulkDestroy.mockResolvedValueOnce(undefined)
    baseProto.destroy.mockResolvedValueOnce('BASE_DESTROYED')

    const model = buildModel()
    const options: any = { foo: 'bar' }

    const result = await (serviceResource as any).destroy(model, options)
    expect(options.dontEmit).toBe(true)
    expect(repo.transaction).toHaveBeenCalledTimes(1)
    expect((contactResource as any).bulkDestroy).toHaveBeenCalledTimes(1)
    expect((messageResource as any).bulkDestroy).toHaveBeenCalledTimes(1)

    const contactArgs = (contactResource as any).bulkDestroy.mock.calls[0][0]
    const messageArgs = (messageResource as any).bulkDestroy.mock.calls[0][0]

    expect(contactArgs.where).toEqual({ serviceId: model.id })
    expect(messageArgs.where).toEqual({ serviceId: model.id })
    expect(contactArgs.transaction).toBe('tx-token')
    expect(messageArgs.transaction).toBe('tx-token')
    expect(contactArgs.dontEmit).toBe(true)
    expect(messageArgs.dontEmit).toBe(true)
    expect(contactArgs.foo).toBe('bar')
    expect(messageArgs.foo).toBe('bar')

    expect(baseProto.destroy).toHaveBeenCalledTimes(1)
    const superCallOptions = baseProto.destroy.mock.calls[0][1]
    expect(superCallOptions.transaction).toBe('tx-token')
    expect(superCallOptions.dontEmit).toBe(false)
    expect(superCallOptions.foo).toBe('bar')

    expect(result).toBe('BASE_DESTROYED')
  })

  test('propagates error if a bulkDestroy fails and does not call super.destroy', async () => {
    const repo = mockRepositoryWithTransaction()
    const err = new Error('bulk destroy failed')
    ;(contactResource as any).bulkDestroy.mockRejectedValueOnce(err)
    ;(messageResource as any).bulkDestroy.mockResolvedValueOnce(undefined)
    baseProto.destroy.mockResolvedValueOnce('UNREACHABLE')

    const model = buildModel()
    await expect((serviceResource as any).destroy(model, {})).rejects.toThrow('bulk destroy failed')

    expect(repo.transaction).toHaveBeenCalledTimes(1)
    expect(baseProto.destroy).not.toHaveBeenCalled()
  })

  test('ensures both bulkDestroy functions are invoked inside Promise.all', async () => {
    mockRepositoryWithTransaction()
    ;(contactResource as any).bulkDestroy.mockResolvedValueOnce(undefined)
    ;(messageResource as any).bulkDestroy.mockResolvedValueOnce(undefined)
    baseProto.destroy.mockResolvedValueOnce('DONE')

    const model = buildModel()
    await (serviceResource as any).destroy(model, {})

    expect((contactResource as any).bulkDestroy).toHaveBeenCalledTimes(1)
    expect((messageResource as any).bulkDestroy).toHaveBeenCalledTimes(1)
  })

  test('destroy performs transactional cleanup of contacts and messages then calls super.destroy', async () => {
    const superProto = Object.getPrototypeOf(Object.getPrototypeOf(serviceResource))
    const superDestroySpy = jest.spyOn(superProto as any, 'destroy' as any).mockResolvedValue('DESTROYED')

    ;(contactResource as any).bulkDestroy = jest.fn().mockResolvedValue(undefined)
    ;(messageResource as any).bulkDestroy = jest.fn().mockResolvedValue(undefined)

    const transactionObj = { tx: '1' }
    const transactionFn = jest.fn(async (cb) => cb(transactionObj))

    const repoMock = {
      transaction: transactionFn,
    }

    const getRepositorySpy = jest.spyOn(serviceResource as any, 'getRepository').mockReturnValue(repoMock)

    const model = { id: 'svc-destroy-1' }
    const options: any = { customOpt: true }

    await (serviceResource as any).destroy(model, options)

    expect(options.dontEmit).toBe(true)
    expect(getRepositorySpy).toHaveBeenCalled()
    expect(transactionFn).toHaveBeenCalledTimes(1)

    expect((contactResource as any).bulkDestroy).toHaveBeenCalledTimes(1)
    expect((messageResource as any).bulkDestroy).toHaveBeenCalledTimes(1)

    const contactArgs = (contactResource as any).bulkDestroy.mock.calls[0][0]
    const messageArgs = (messageResource as any).bulkDestroy.mock.calls[0][0]

    expect(contactArgs.where).toEqual({ serviceId: model.id })
    expect(messageArgs.where).toEqual({ serviceId: model.id })
    expect(contactArgs.transaction).toBe(transactionObj)
    expect(messageArgs.transaction).toBe(transactionObj)
    expect(contactArgs.customOpt).toBe(true)
    expect(messageArgs.customOpt).toBe(true)
    expect(contactArgs.dontEmit).toBe(true)

    expect(superDestroySpy).toHaveBeenCalledTimes(1)
    const superArgs = superDestroySpy.mock.calls[0]
    expect(superArgs[0]).toBe(model)
    expect(superArgs[1]).toMatchObject({
      transaction: transactionObj,
      customOpt: true,
      dontEmit: false,
    })
  })
})

describe('ServiceResource.archive', () => {
  let superProto: any
  let findByIdSpy: jest.SpyInstance
  let updateSpy: jest.SpyInstance
  let emitArchivedSpy: jest.SpyInstance
  let iteratePaginatedMock: any

  beforeEach(() => {
    jest.clearAllMocks()
    superProto = Object.getPrototypeOf(Object.getPrototypeOf(serviceResource))
    ;(serviceResource as any).eventTransaction = jest.fn(async (fn) => fn({}))
    iteratePaginatedMock = require('../../../../core/utils/iteratePaginated')
    iteratePaginatedMock.mockImplementation(async () => Promise.resolve())
    updateSpy = jest.spyOn(serviceResource as any, 'update').mockImplementation(async (_svc, payload) => ({
      ..._svc,
      ...payload,
    }))
    emitArchivedSpy = jest.spyOn(serviceResource as any, 'emitArchived').mockReturnValue(true)
  })

  test('throws error when service not found', async () => {
    findByIdSpy = jest.spyOn(superProto, 'findById').mockResolvedValue(null)
    await expect((serviceResource as any).archive('not-found', 'user-1', true)).rejects.toThrow(
      'No service found with given serviceId: not-found.',
    )
    expect(findByIdSpy).toHaveBeenCalledWith('not-found')
  })

  test('throws error when service already archived', async () => {
    const svc = {
      id: 'svc-arch-1',
      archivedAt: new Date(),
      type: 'whatsapp',
      data: { status: { isConnected: false, isStarted: false, isStarting: false }, providerType: 'other' },
    }
    findByIdSpy = jest.spyOn(superProto, 'findById').mockResolvedValue(svc)
    await expect((serviceResource as any).archive(svc.id, 'user-1', true)).rejects.toThrow(
      `Service ${svc.id} already archived.`,
    )
  })

  test('archives connected non-workersGo service: shutdown path', async () => {
    const svc = {
      id: 'svc-arch-2',
      archivedAt: null,
      type: 'whatsapp',
      data: { status: { isConnected: true, isStarted: false, isStarting: false }, providerType: 'other' },
    }
    findByIdSpy = jest.spyOn(superProto, 'findById').mockResolvedValue(svc)
    Object.defineProperty(configValues, 'workersGoServices', {
      value: [],
      writable: true,
      configurable: true,
    })

    driverService.shutdown = jest.fn().mockResolvedValue(undefined)

    const result = await (serviceResource as any).archive(svc.id, 'user-2', true)

    expect(driverService.shutdown).toHaveBeenCalledWith(svc.id)
    expect(updateSpy).toHaveBeenCalledTimes(1)
    const updatePayload = updateSpy.mock.calls[0][1]
    expect(updatePayload.isArchived).toBe(true)
    expect(updatePayload.archivedAt).toBeInstanceOf(Date)
    expect(updatePayload.data.status).toEqual({ isConnected: false, isStarting: false, isStarted: false })

    expect(emitArchivedSpy).toHaveBeenCalledTimes(1)

    expect((serviceResource as any).eventTransaction).toHaveBeenCalledTimes(3)
    expect(iteratePaginatedMock).toHaveBeenCalledTimes(3)

    expect(result).toBe(true)
  })

  test('archives connected workersGo service: dispatch path', async () => {
    const svc = {
      id: 'svc-arch-3',
      archivedAt: null,
      type: 'whatsapp',
      data: { status: { isConnected: true, isStarted: false, isStarting: false }, providerType: 'other' },
    }
    findByIdSpy = jest.spyOn(superProto, 'findById').mockResolvedValue(svc)

    Object.defineProperty(configValues, 'workersGoServices', {
      value: ['whatsapp'],
      writable: true,
      configurable: true,
    })

    const dispatchMock = jest.fn().mockResolvedValue(undefined)
    const containerGetMock = jest.fn(() => ({ dispatch: dispatchMock }))
    ;(Container as any).get = containerGetMock

    driverService.shutdown = jest.fn()

    const result = await (serviceResource as any).archive(svc.id, 'user-3', true)

    expect(containerGetMock).toHaveBeenCalled()
    expect(dispatchMock).toHaveBeenCalledTimes(1)
    expect(dispatchMock.mock.calls[0][0]).toBe('shutdown')
    expect(dispatchMock.mock.calls[0][1]).toEqual({ serviceId: svc.id })
    expect(driverService.shutdown).not.toHaveBeenCalled()
    expect(emitArchivedSpy).toHaveBeenCalledTimes(1)
    expect(result).toBe(true)
  })

  test('archives already disconnected service without shutdown/dispatch', async () => {
    const svc = {
      id: 'svc-arch-4',
      archivedAt: null,
      type: 'whatsapp',
      data: { status: { isConnected: false, isStarted: false, isStarting: false }, providerType: 'other' },
    }
    findByIdSpy = jest.spyOn(superProto, 'findById').mockResolvedValue(svc)
    Object.defineProperty(configValues, 'workersGoServices', {
      value: [],
      writable: true,
      configurable: true,
    })

    driverService.shutdown = jest.fn()

    const result = await (serviceResource as any).archive(svc.id, 'user-4', true)

    expect(driverService.shutdown).not.toHaveBeenCalled()
    expect(updateSpy).toHaveBeenCalled()
    expect(emitArchivedSpy).toHaveBeenCalled()
    expect(result).toBe(true)
  })
})

describe('ServiceResource.destroyById', () => {
  const baseProto = Object.getPrototypeOf(Object.getPrototypeOf(serviceResource))
  let baseFindByIdSpy
  let baseUpdateByIdSpy

  beforeEach(() => {
    jest.clearAllMocks()
    baseUpdateByIdSpy = jest.fn().mockResolvedValue(undefined)
    ;(baseProto as any).updateById = baseUpdateByIdSpy
    ;(contactResource as any).bulkDestroy = jest.fn().mockResolvedValue(undefined)
    ;(messageResource as any).bulkDestroy = jest.fn().mockResolvedValue(undefined)
  })

  afterEach(() => {
    jest.restoreAllMocks()
  })

  test('with botId: clears botId, removes webhooks, calls destroy and returns result', async () => {
    const service = {
      id: 'svc-destroy-1',
      botId: 'bot-1',
      setWebhooks: jest.fn().mockResolvedValue(undefined),
    }
    baseFindByIdSpy = jest.spyOn(baseProto, 'findById').mockResolvedValue(service)
    const repoModel = { id: 'model-destroy' }
    const repo = {
      findById: jest.fn().mockResolvedValue(repoModel),
    }
    const getRepoSpy = jest.spyOn(serviceResource as any, 'getRepository').mockReturnValue(repo)
    const destroySpy = jest.spyOn(serviceResource, 'destroy').mockResolvedValue('deleted' as any)

    const result = await serviceResource.destroyById('svc-destroy-1')

    expect(baseFindByIdSpy).toHaveBeenCalledWith('svc-destroy-1')
    expect(baseUpdateByIdSpy).toHaveBeenCalledWith('svc-destroy-1', { botId: null })
    expect(service.setWebhooks).toHaveBeenCalledWith([])
    expect(repo.findById).toHaveBeenCalledWith('svc-destroy-1', {})
    expect(destroySpy).toHaveBeenCalledWith(repoModel, {})
    expect(result).toBe('deleted')
    getRepoSpy.mockRestore()
  })

  test('without botId: does not call updateById but still clears webhooks and destroys', async () => {
    const service = {
      id: 'svc-destroy-2',
      botId: null,
      setWebhooks: jest.fn().mockResolvedValue(undefined),
    }
    baseFindByIdSpy = jest.spyOn(baseProto, 'findById').mockResolvedValue(service)
    const repoModel = { id: 'model-destroy-2' }
    const repo = {
      findById: jest.fn().mockResolvedValue(repoModel),
    }
    jest.spyOn(serviceResource as any, 'getRepository').mockReturnValue(repo)
    const destroySpy = jest.spyOn(serviceResource, 'destroy').mockResolvedValue('deleted-2' as any)

    const result = await serviceResource.destroyById('svc-destroy-2', { anyOpt: 1 })

    expect(baseUpdateByIdSpy).not.toHaveBeenCalled()
    expect(service.setWebhooks).toHaveBeenCalledWith([])
    expect(destroySpy).toHaveBeenCalledWith(repoModel, { anyOpt: 1 })
    expect(result).toBe('deleted-2')
  })
})

describe('ServiceResource.getServices', () => {
  const buildIsolatedInstance = async (options: {
    serviceTypes: string[]
    repoServices: Array<{ id: string; type: string }>
  }) => {
    jest.resetModules()
    jest.doMock('../../../../core/dbSequelize/repositories/serviceRepository', () => ({}))
    jest.doMock('../../../../core/dbSequelize/repositories/ServicesHistoryRepository', () => ({}))
    jest.doMock('../../../../core/services/driverService', () => ({}))
    jest.doMock('../../../../core/resources/contactResource', () => ({}))
    jest.doMock('../../../../core/resources/messageResource', () => ({}))
    jest.doMock('../../../../core/resources/scheduleResource', () => ({}))
    jest.doMock('../../../../core/resources/campaignResource', () => ({}))
    jest.doMock('../../../../core/resources/customFieldsResource', () => ({}))
    jest.doMock('../../../../core/resources/fileResource', () => ({}))
    jest.doMock('../../../../microServices/workers/jobs/facebookMessenger/driver/baseFunctions', () => ({}))
    jest.doMock('../../../../core/utils/iteratePaginated', () => jest.fn())
    jest.doMock('../../../../core/resources/serverPodResource', () => ({}))
    jest.doMock('../../../../core/services/logs/reportError', () => jest.fn())
    jest.doMock('../../../../core/transformers/fileTransformer', () => jest.fn())
    jest.doMock('../../../../core/configValues', () => ({ __esModule: true, default: { workersGoServices: [] } }))
    jest.doMock('typedi', () => ({ __esModule: true, default: { get: jest.fn() } }))
    jest.doMock('../../../../core/services/jobs/http/HttpJobsDispatcher', () => jest.fn())
    jest.doMock('../../../../core/config', () => {
      const mockFn = jest.fn((key: string) => {
        if (key === 'emitterMaxListeners') return 50
        return undefined
      })
      return mockFn
    })
    // Crucial: proper mock exporting serviceTypes
    jest.doMock('../../../../microServices/workers/jobs/account/updateContractPlan/Types', () => ({
      serviceTypes: options.serviceTypes,
    }))

    // BaseResource mock (same behavior pattern used earlier)
    jest.doMock('../../../../core/resources/BaseResource', () => {
      class MockBase {
        private _events: Record<string, Function[]> = {}

        getEvents() {
          return []
        }

        getRepository() {
          return {
            findOne: jest.fn(),
            findById: jest.fn(),
            findMany: jest.fn().mockResolvedValue(options.repoServices),
            create: () => ({ setDepartments: jest.fn() }),
            update: jest.fn(),
            destroy: jest.fn(),
            updateById: () => ({ setDepartments: jest.fn() }),
            transaction: async (cb) => cb({}),
          }
        }

        on(event: string, listener: (...args: any[]) => void) {
          if (!this._events[event]) this._events[event] = []
          this._events[event].push(listener)
          return this
        }

        emit(event: string, ...args: any[]) {
          ;(this._events[event] || []).forEach((l) => l(...args))
          return true
        }

        findOne() {
          return Promise.resolve(null)
        }

        findById() {
          return Promise.resolve(null)
        }

        create() {
          return Promise.resolve(null)
        }

        update() {
          return Promise.resolve(null)
        }

        destroy() {
          return Promise.resolve(null)
        }

        emitUpdated() {
          return Promise.resolve(null)
        }

        eventTransaction = async (cb) => cb({})
      }
      return {
        __esModule: true,
        default: MockBase,
        CREATED: 'CREATED',
        UPDATED: 'UPDATED',
        DESTROYED: 'DESTROYED',
      }
    })

    const srv = (await import('../../../../core/resources/serviceResource')).default
    return srv
  }

  test('returns empty object when isAmounts is false', async () => {
    const serviceResourceLocal = await buildIsolatedInstance({
      serviceTypes: ['whatsapp', 'email'],
      repoServices: [],
    })
    const repo = serviceResourceLocal.getRepository()
    jest.spyOn(serviceResourceLocal, 'getRepository').mockReturnValue(repo)

    const result = await serviceResourceLocal.getServices({ id: 'acc-1' } as any, false)
    expect(result).toEqual({})
    expect(repo.findMany).not.toHaveBeenCalled()
  })

  test('computes servicesAmounts with mixed base and additional consumption', async () => {
    const serviceTypes = ['whatsapp', 'email', 'webchat']
    const repoServices = [
      { id: 's1', type: 'whatsapp' },
      { id: 's2', type: 'webchat' },
      { id: 's3', type: 'webchat' },
      { id: 's4', type: 'email' },
    ]
    const serviceResourceLocal = await buildIsolatedInstance({ serviceTypes, repoServices })
    const repo = serviceResourceLocal.getRepository()
    jest.spyOn(serviceResourceLocal, 'getRepository').mockReturnValue(repo)

    const account = {
      id: 'acc-2',
      plan: {
        planBaseServices: { whatsapp: 1, email: 0, webchat: 2 },
        services: { whatsapp: 2, email: 1, webchat: 3 },
      },
    }

    const { servicesAmounts } = await serviceResourceLocal.getServices(account as any, true)

    expect(repo.findMany).toHaveBeenCalledWith({
      attributes: ['id', 'type'],
      where: { accountId: 'acc-2', archivedAt: null },
    })

    expect(servicesAmounts).toMatchObject({
      planBase: 3,
      planBaseConsumed: 3,
      planBaseAvailable: 0,
      additional: 3,
      additionalConsumed: 1,
      additionalAvailable: 2,
      totalContracted: 6,
      totalConsumed: 4,
      totalAvailable: 2,
    })

    const whatsapp = servicesAmounts.serviceTypes.find((s) => s.serviceType === 'whatsapp')
    const email = servicesAmounts.serviceTypes.find((s) => s.serviceType === 'email')
    const webchat = servicesAmounts.serviceTypes.find((s) => s.serviceType === 'webchat')

    expect(whatsapp).toEqual({
      serviceType: 'whatsapp',
      planBase: 1,
      planBaseConsumed: 1,
      planBaseAvailable: 0,
      additional: 1,
      additionalConsumed: 0,
      additionalAvailable: 1,
      totalConsumed: 1,
    })
    expect(email).toEqual({
      serviceType: 'email',
      planBase: 0,
      planBaseConsumed: 0,
      planBaseAvailable: 0,
      additional: 1,
      additionalConsumed: 1,
      additionalAvailable: 0,
      totalConsumed: 1,
    })
    expect(webchat).toEqual({
      serviceType: 'webchat',
      planBase: 2,
      planBaseConsumed: 2,
      planBaseAvailable: 0,
      additional: 1,
      additionalConsumed: 0,
      additionalAvailable: 1,
      totalConsumed: 2,
    })
  })

  test('consumption exceeding base uses additional correctly', async () => {
    const serviceTypes = ['whatsapp']
    const repoServices = [
      { id: 's1', type: 'whatsapp' },
      { id: 's2', type: 'whatsapp' },
      { id: 's3', type: 'whatsapp' },
    ]
    const serviceResourceLocal = await buildIsolatedInstance({ serviceTypes, repoServices })
    const repo = serviceResourceLocal.getRepository()
    jest.spyOn(serviceResourceLocal, 'getRepository').mockReturnValue(repo)

    const account = {
      id: 'acc-3',
      plan: {
        planBaseServices: { whatsapp: 1 },
        services: { whatsapp: 2 },
      },
    }

    const { servicesAmounts } = await serviceResourceLocal.getServices(account as any, true)

    expect(servicesAmounts.totalConsumed).toBe(3)
    expect(servicesAmounts.planBase).toBe(1)
    expect(servicesAmounts.planBaseConsumed).toBe(1)
    expect(servicesAmounts.additional).toBe(1)
    expect(servicesAmounts.additionalConsumed).toBe(2)
    expect(servicesAmounts.additionalAvailable).toBe(-1)
    expect(servicesAmounts.totalAvailable).toBe(-1)

    const detail = servicesAmounts.serviceTypes[0]
    expect(detail).toMatchObject({
      serviceType: 'whatsapp',
      planBase: 1,
      planBaseConsumed: 1,
      additional: 1,
      additionalConsumed: 2,
      additionalAvailable: -1,
      totalConsumed: 3,
    })
  })

  test('no plan data defaults to zeros and shows over-consumption as negative availability', async () => {
    const serviceTypes = ['whatsapp', 'email']
    const repoServices = [
      { id: 's1', type: 'whatsapp' },
      { id: 's2', type: 'email' },
    ]
    const serviceResourceLocal = await buildIsolatedInstance({ serviceTypes, repoServices })
    const repo = serviceResourceLocal.getRepository()
    jest.spyOn(serviceResourceLocal, 'getRepository').mockReturnValue(repo)

    const account = { id: 'acc-4' }

    const { servicesAmounts } = await serviceResourceLocal.getServices(account as any, true)

    expect(servicesAmounts.planBase).toBe(0)
    expect(servicesAmounts.totalContracted).toBe(0)
    expect(servicesAmounts.totalConsumed).toBe(2)
    expect(servicesAmounts.totalAvailable).toBe(-2)
    expect(servicesAmounts.serviceTypes).toHaveLength(2)

    servicesAmounts.serviceTypes.forEach((st) => {
      expect(st.planBase).toBe(0)
      expect(st.additional).toBe(0)
      expect(st.planBaseConsumed + st.additionalConsumed).toBe(st.totalConsumed)
    })
  })
})

describe('ServiceResource.getServiceWithServerPodById', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    ;(serverPodResource as any).getCurrentServerPod = jest.fn()
  })

  afterEach(() => {
    jest.restoreAllMocks()
  })

  test('throws when service not found', async () => {
    const spy = jest.spyOn(serviceResource as any, 'findById').mockResolvedValue(null)
    await expect(serviceResource.getServiceWithServerPodById('svc-missing')).rejects.toThrow(
      'No service found with given serviceId: svc-missing.',
    )
    expect(spy).toHaveBeenCalledWith('svc-missing', expect.any(Object))
  })

  test('throws when service is archived', async () => {
    const archived = {
      id: 'svc-arch',
      archivedAt: new Date(),
      internalData: { securityToken: 'tok-arch' },
    }
    jest.spyOn(serviceResource as any, 'findById').mockResolvedValue(archived)
    await expect(serviceResource.getServiceWithServerPodById('svc-arch')).rejects.toThrow(
      'Service svc-arch is archived.',
    )
  })

  test('throws when withServerPod true and serverPod not found', async () => {
    const svc = {
      id: 'svc-no-pod',
      archivedAt: null,
      internalData: { securityToken: 'sec-token' },
    }
    jest.spyOn(serviceResource as any, 'findById').mockResolvedValue(svc)
    ;(serverPodResource as any).getCurrentServerPod.mockResolvedValue(null)
    await expect(serviceResource.getServiceWithServerPodById('svc-no-pod')).rejects.toThrow(
      'No serverPod set for: svc-no-pod:sec-token.',
    )
    expect((serverPodResource as any).getCurrentServerPod).toHaveBeenCalledWith('svc-no-pod:sec-token')
  })

  test('returns service with serverPod when found', async () => {
    const svc: any = {
      id: 'svc-ok',
      archivedAt: null,
      internalData: { securityToken: 'tok-ok' },
    }
    const pod = { id: 'pod-1', region: 'us-east' }
    jest.spyOn(serviceResource as any, 'findById').mockResolvedValue(svc)
    ;(serverPodResource as any).getCurrentServerPod.mockResolvedValue(pod)

    const result = await serviceResource.getServiceWithServerPodById('svc-ok')
    expect(result).toBe(svc)
    expect(result.serverPod).toBe(pod)
    expect((serverPodResource as any).getCurrentServerPod).toHaveBeenCalledTimes(1)
    expect((serverPodResource as any).getCurrentServerPod).toHaveBeenCalledWith('svc-ok:tok-ok')
  })

  test('skips serverPod retrieval when withServerPod is false', async () => {
    const svc: any = {
      id: 'svc-skip',
      archivedAt: null,
      internalData: { securityToken: 'tok-skip' },
    }
    jest.spyOn(serviceResource as any, 'findById').mockResolvedValue(svc)
    ;(serverPodResource as any).getCurrentServerPod.mockResolvedValue({ id: 'should-not-be-used' })

    const result = await serviceResource.getServiceWithServerPodById('svc-skip', false)
    expect(result).toBe(svc)
    expect(result.serverPod).toBeUndefined()
    expect((serverPodResource as any).getCurrentServerPod).not.toHaveBeenCalled()
  })
})

describe('ServiceResource.findById', () => {
  const superProto = Object.getPrototypeOf(Object.getPrototypeOf(serviceResource))

  beforeEach(() => {
    jest.restoreAllMocks()
    jest.clearAllMocks()
    ;(customFieldsResource as any).findById = jest.fn()
    ;(fileResource as any).findById = jest.fn()
    ;(fileTransformer as jest.Mock).mockReset()
  })

  test('returns null when underlying super.findById returns null (service not found)', async () => {
    jest.spyOn(superProto, 'findById').mockResolvedValue(null)
    const result = await serviceResource.findById('not-found')
    expect(result).toBeNull()
    expect((customFieldsResource as any).findById).not.toHaveBeenCalled()
    expect((fileResource as any).findById).not.toHaveBeenCalled()
    expect(fileTransformer).not.toHaveBeenCalled()
  })

  test('does not enrich non-webchat services', async () => {
    const svc = { id: 'svc-plain', type: 'whatsapp', data: {} }
    jest.spyOn(superProto, 'findById').mockResolvedValue(svc)
    const result = await serviceResource.findById('svc-plain')
    expect(result).toBe(svc)
    expect((customFieldsResource as any).findById).not.toHaveBeenCalled()
    expect((fileResource as any).findById).not.toHaveBeenCalled()
    expect(fileTransformer).not.toHaveBeenCalled()
  })

  test('enriches webchat service with customField, logo, webchatIcon and profilePhoto', async () => {
    const svc: any = {
      id: 'svc-webchat-1',
      type: 'webchat',
      data: {
        webchat: {
          customField: { id: 'cf-1' },
          custom: { logo: 'logo-id', webchatIcon: 'icon-id', profilePhoto: 'profile-id' },
        },
      },
    }
    jest.spyOn(superProto, 'findById').mockResolvedValue(svc)
    ;(customFieldsResource as any).findById.mockResolvedValue({ id: 'cf-1', name: 'Custom Field' })
    ;(fileResource as any).findById.mockImplementation(async (id: string) => ({ id }))
    ;(fileTransformer as jest.Mock).mockImplementation(async (file) => ({ transformedId: file.id }))

    const result = await serviceResource.findById('svc-webchat-1')

    expect(result).toBe(svc)
    expect((customFieldsResource as any).findById).toHaveBeenCalledWith('cf-1')
    expect((fileResource as any).findById).toHaveBeenCalledTimes(3)
    expect((fileResource as any).findById).toHaveBeenNthCalledWith(1, 'logo-id')
    expect((fileResource as any).findById).toHaveBeenNthCalledWith(2, 'icon-id')
    expect((fileResource as any).findById).toHaveBeenNthCalledWith(3, 'profile-id')
    expect(fileTransformer).toHaveBeenCalledTimes(3)
    expect(svc.customField).toEqual({ id: 'cf-1', name: 'Custom Field' })
    expect(svc.logo).toEqual({ transformedId: 'logo-id' })
    expect(svc.webchatIcon).toEqual({ transformedId: 'icon-id' })
    expect(svc.profilePhoto).toEqual({ transformedId: 'profile-id' })
  })

  test('webchat service with no customField/custom media triggers no extra lookups', async () => {
    const svc: any = { id: 'svc-webchat-empty', type: 'webchat', data: { webchat: {} } }
    jest.spyOn(superProto, 'findById').mockResolvedValue(svc)

    const result = await serviceResource.findById('svc-webchat-empty')

    expect(result).toBe(svc)
    expect((customFieldsResource as any).findById).not.toHaveBeenCalled()
    expect((fileResource as any).findById).not.toHaveBeenCalled()
    expect(fileTransformer).not.toHaveBeenCalled()
    expect(svc).not.toHaveProperty('customField')
    expect(svc).not.toHaveProperty('logo')
  })

  test('webchat service with only logo loads only that asset', async () => {
    const svc: any = {
      id: 'svc-webchat-logo',
      type: 'webchat',
      data: { webchat: { custom: { logo: 'only-logo-id' } } },
    }
    jest.spyOn(superProto, 'findById').mockResolvedValue(svc)
    ;(fileResource as any).findById.mockResolvedValue({ id: 'only-logo-id' })
    ;(fileTransformer as jest.Mock).mockResolvedValue({ transformedId: 'only-logo-id' })

    const result = await serviceResource.findById('svc-webchat-logo')

    expect(result).toBe(svc)
    expect((customFieldsResource as any).findById).not.toHaveBeenCalled()
    expect((fileResource as any).findById).toHaveBeenCalledTimes(1)
    expect((fileResource as any).findById).toHaveBeenCalledWith('only-logo-id')
    expect(fileTransformer).toHaveBeenCalledTimes(1)
    expect(svc.logo).toEqual({ transformedId: 'only-logo-id' })
    expect(svc).not.toHaveProperty('webchatIcon')
    expect(svc).not.toHaveProperty('profilePhoto')
  })
})

describe('ServiceResource.archive - providerType meta', () => {
  beforeAll(() => jest.restoreAllMocks())

  afterAll(() => jest.restoreAllMocks())

  test('deletes existing webhook (covers line 312) and updates service', async () => {
    jest.clearAllMocks()
    ;(driverService as any).shutdown = jest.fn().mockResolvedValue({})
    ;(driverService as any).deleteWebhook = jest.fn().mockResolvedValue(undefined)
    ;(serviceResource as any).update = jest.fn().mockResolvedValue(undefined)
    ;(serviceResource as any).emitArchived = jest.fn().mockResolvedValue(undefined)

    const result = await serviceResource.archive('123', 'user-1', true)

    expect((driverService as any).shutdown).toHaveBeenCalledTimes(1)
    expect(result).toBe(true)
  })
})

describe('ServiceResource.archive - iteratePaginated', () => {
  let findByIdSpy: jest.SpyInstance
  let updateSpy: jest.SpyInstance
  let emitArchivedSpy: jest.SpyInstance

  const buildService = (overrides: any = {}) => ({
    id: 'svc-archive-1',
    type: 'whatsapp',
    archivedAt: null,
    isArchived: false,
    data: {
      providerType: 'meta',
      status: { isConnected: true, isStarted: false, isStarting: false },
    },
    internalData: { securityToken: 'sec-token' },
    setWebhooks: jest.fn(),
    ...overrides,
  })

  beforeEach(() => {
    jest.clearAllMocks()
    ;(serviceResource as any).eventTransaction = async (cb: any) => cb({})
    ;(contactResource as any).findManyPaginated = jest.fn().mockResolvedValue({ data: [{ id: 'contact-1' }] })
    ;(contactResource as any).closeTicket = jest.fn().mockResolvedValue(undefined)

    scheduleResource.findManyPaginated = jest.fn().mockResolvedValue({ data: [{ id: 'schedule-1' }] })
    scheduleResource.update = jest.fn().mockResolvedValue(undefined)

    campaignsResource.findManyPaginatedBase = jest.fn().mockResolvedValue({ data: [{ id: 'campaign-1' }] })
    campaignsResource.internalUpdate = jest.fn().mockResolvedValue(undefined)

    iteratePaginated.mockImplementation(async (pageFetcher: any, handler: any) => {
      await pageFetcher({ page: 1 })
      await handler({ id: 'item-1' })
      return undefined
    })

    findByIdSpy = jest.spyOn(baseResourceModule.default.prototype, 'findById')

    updateSpy = jest.spyOn(serviceResource as any, 'update').mockImplementation(async (service, data) => {
      return {
        ...(typeof service === 'object' && service !== null ? service : {}),
        ...(typeof data === 'object' && data !== null ? data : {}),
      }
    })

    emitArchivedSpy = jest.spyOn(serviceResource as any, 'emitArchived').mockResolvedValue(true as any)
    ;(driverService as any).shutdown = jest.fn().mockResolvedValue(undefined)

    deleteWebhookInGateway.mockResolvedValue(undefined)
  })

  afterEach(() => {
    jest.restoreAllMocks()
  })

  test('archives a connected service: shuts down, iterates paginated resources, cancels related entities, deletes webhook and emits archived', async () => {
    const svc = buildService()
    findByIdSpy.mockResolvedValueOnce(svc)

    const result = await serviceResource.archive(svc.id, 'user-1', true)

    expect(result).toBe(true)
    expect(driverService.shutdown).toHaveBeenCalledTimes(1)
    expect(driverService.shutdown).toHaveBeenCalledWith(svc.id)
    expect(iteratePaginated).toHaveBeenCalledTimes(3)
    iteratePaginated.mock.calls.forEach((call) => {
      expect(typeof call[0]).toBe('function')
      expect(typeof call[1]).toBe('function')
    })
    expect((contactResource as any).findManyPaginated).toHaveBeenCalled()
    expect((contactResource as any).closeTicket).toHaveBeenCalledTimes(1)
    expect(scheduleResource.findManyPaginated).toHaveBeenCalled()
    expect(scheduleResource.update).toHaveBeenCalledTimes(1)
    expect(campaignsResource.findManyPaginatedBase).toHaveBeenCalled()
    expect(campaignsResource.internalUpdate).toHaveBeenCalledTimes(1)

    expect(getWebhookInGatewayByUrl).toHaveBeenCalledTimes(1)
    expect(deleteWebhookInGateway).toHaveBeenCalledTimes(1)

    expect(updateSpy).toHaveBeenCalledTimes(1)
    const updateArgs = updateSpy.mock.calls[0][1]
    expect(updateArgs.isArchived).toBe(true)
    expect(updateArgs.archivedAt).toBeInstanceOf(Date)
    expect(updateArgs.data.status).toEqual({ isConnected: false, isStarting: false, isStarted: false })

    expect(emitArchivedSpy).toHaveBeenCalledTimes(1)
  })

  test('unarchives a service (archive=false): does not shutdown and clears archivedAt', async () => {
    const archivedAt = new Date()
    const svc = buildService({
      archivedAt,
      isArchived: true,
      data: { providerType: 'other', status: { isConnected: true, isStarted: true, isStarting: true } },
    })
    findByIdSpy.mockResolvedValueOnce(svc)

    await serviceResource.archive(svc.id, 'user-2', false)

    expect(driverService.shutdown).not.toHaveBeenCalled()
    expect(updateSpy).toHaveBeenCalledTimes(1)
    const updateArgs = updateSpy.mock.calls[0][1]
    expect(updateArgs.isArchived).toBe(false)
    expect(updateArgs.archivedAt).toBeNull()
    expect(updateArgs.data.status).toEqual({ isConnected: false, isStarting: false, isStarted: false })
  })

  test('throws when service not found', async () => {
    findByIdSpy.mockResolvedValueOnce(null)
    await expect(serviceResource.archive('missing-id', 'user-x', true)).rejects.toThrow(
      'No service found with given serviceId: missing-id.',
    )
  })

  test('throws when trying to archive an already archived service', async () => {
    const svc = buildService({ archivedAt: new Date(), isArchived: true })
    findByIdSpy.mockResolvedValueOnce(svc)
    await expect(serviceResource.archive(svc.id, 'user-3', true)).rejects.toThrow(`Service ${svc.id} already archived.`)
    expect(driverService.shutdown).not.toHaveBeenCalled()
  })

  test('does not shutdown when service not connected/started but still processes related entities', async () => {
    const svc = buildService({
      data: { providerType: 'other', status: { isConnected: false, isStarted: false, isStarting: false } },
    })
    findByIdSpy.mockResolvedValueOnce(svc)

    await serviceResource.archive(svc.id, 'user-4', true)

    expect(driverService.shutdown).not.toHaveBeenCalled()
    expect(iteratePaginated).toHaveBeenCalledTimes(3)
    expect((contactResource as any).closeTicket).toHaveBeenCalledTimes(1)
    expect(scheduleResource.update).toHaveBeenCalledTimes(1)
    expect(campaignsResource.internalUpdate).toHaveBeenCalledTimes(1)
  })
})
