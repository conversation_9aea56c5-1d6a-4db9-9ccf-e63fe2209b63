jest.mock('../../../../../core/config', () => ({
  __esModule: true,
  default: jest.fn(),
}))

jest.mock('../../../../../core/resources/accountResource', () => ({
  __esModule: true,
  default: {
    findById: jest.fn(),
  },
}))

jest.mock('../../../../../core/resources/creditMovementResource', () => ({
  __esModule: true,
  default: {
    balance: jest.fn(),
  },
}))

jest.mock('../../../../../core/resources/notificationResource', () => ({
  __esModule: true,
  default: {
    create: jest.fn(),
    findMany: jest.fn(),
  },
}))

jest.mock('../../../../../core/resources/userResource', () => ({
  __esModule: true,
  default: {
    findAdminUsers: jest.fn(),
  },
}))

import AgentCreditsService from '../../../../../core/services/agent/AgentCreditsService'
import config from '../../../../../core/config'
import accountResource from '../../../../../core/resources/accountResource'
import creditMovementResource from '../../../../../core/resources/creditMovementResource'
import notificationResource from '../../../../../core/resources/notificationResource'
import userResource from '../../../../../core/resources/userResource'

const mockedConfig = jest.mocked(config)
const mockedAccountResource = jest.mocked(accountResource)
const mockedCreditMovementResource = jest.mocked(creditMovementResource)
const mockedNotificationResource = jest.mocked(notificationResource)
const mockedUserResource = jest.mocked(userResource)

describe('AgentCreditsService', () => {
  let agentCreditsService: AgentCreditsService
  const mockAccountId = 'test-account-id'

  beforeEach(() => {
    jest.clearAllMocks()
    agentCreditsService = new AgentCreditsService()
    agentCreditsService.logger = { log: jest.fn() } as any
  })

  describe('limitReached', () => {
    it('should return false when AI consumption is not blocked', async () => {
      mockedConfig.mockReturnValue(false)

      const result = await agentCreditsService.limitReached(mockAccountId)

      expect(result).toBe(false)
      expect(mockedConfig).toHaveBeenCalledWith('blockAiConsumption')
    })

    it('should return true when account has no plan renew date', async () => {
      mockedConfig.mockReturnValue(true)
      mockedAccountResource.findById.mockResolvedValue({
        plan: null,
      } as any)

      const result = await agentCreditsService.limitReached(mockAccountId)

      expect(result).toBe(true)
      expect(agentCreditsService.logger.log).toHaveBeenCalledWith(
        `There is not a plan renew date for account id ${mockAccountId}`,
        'warn',
      )
    })

    it('should return true when account plan has no renew date', async () => {
      mockedConfig.mockReturnValue(true)
      mockedAccountResource.findById.mockResolvedValue({
        plan: {},
      } as any)

      const result = await agentCreditsService.limitReached(mockAccountId)

      expect(result).toBe(true)
      expect(agentCreditsService.logger.log).toHaveBeenCalledWith(
        `There is not a plan renew date for account id ${mockAccountId}`,
        'warn',
      )
    })

    it('should return true when credits balance is zero or negative', async () => {
      const renewDate = new Date()
      mockedConfig.mockReturnValue(true)
      mockedAccountResource.findById.mockResolvedValue({
        plan: { renewDate },
      } as any)
      mockedCreditMovementResource.balance.mockResolvedValue(0)

      const result = await agentCreditsService.limitReached(mockAccountId)

      expect(result).toBe(true)
      expect(mockedCreditMovementResource.balance).toHaveBeenCalledWith(mockAccountId, 'agent', renewDate)
    })

    it('should return false when credits balance is positive', async () => {
      const renewDate = new Date()
      mockedConfig.mockReturnValue(true)
      mockedAccountResource.findById.mockResolvedValue({
        plan: { renewDate },
      } as any)
      mockedCreditMovementResource.balance.mockResolvedValue(10)

      const result = await agentCreditsService.limitReached(mockAccountId)

      expect(result).toBe(false)
      expect(mockedCreditMovementResource.balance).toHaveBeenCalledWith(mockAccountId, 'agent', renewDate)
    })

    it('should record insufficient credits attempt when shouldNotify is true and limit is reached', async () => {
      const renewDate = new Date()
      mockedConfig.mockReturnValue(true)
      mockedAccountResource.findById.mockResolvedValue({
        plan: { renewDate },
      } as any)
      mockedCreditMovementResource.balance.mockResolvedValue(0)

      const recordSpy = jest.spyOn(agentCreditsService, 'recordInsufficientCreditsAttempt').mockResolvedValue()

      const result = await agentCreditsService.limitReached(mockAccountId, true)

      expect(result).toBe(true)
      expect(recordSpy).toHaveBeenCalledWith(mockAccountId)
    })

    it('should return true on error', async () => {
      mockedConfig.mockReturnValue(true)
      mockedAccountResource.findById.mockRejectedValue(new Error('Database error'))

      const result = await agentCreditsService.limitReached(mockAccountId)

      expect(result).toBe(true)
      expect(agentCreditsService.logger.log).toHaveBeenCalledWith(
        `Error checking agent credits limit for account ${mockAccountId}: Database error`,
        'error',
      )
    })
  })

  describe('notifyAdminsAboutInsufficientCredits', () => {
    it('should create notifications for all admin users', async () => {
      const mockAdminUsers = [{ id: 'admin-1' }, { id: 'admin-2' }]
      const mockAccount = { name: 'Test Account' }

      mockedUserResource.findAdminUsers.mockResolvedValue(mockAdminUsers as any)
      mockedAccountResource.findById.mockResolvedValue(mockAccount as any)
      mockedNotificationResource.create.mockResolvedValue(undefined)

      await agentCreditsService.notifyAdminsAboutInsufficientCredits(mockAccountId)

      expect(mockedUserResource.findAdminUsers).toHaveBeenCalledWith(mockAccountId)
      expect(mockedAccountResource.findById).toHaveBeenCalledWith(mockAccountId)
      expect(mockedNotificationResource.create).toHaveBeenCalledTimes(2)

      expect(mockedNotificationResource.create).toHaveBeenCalledWith({
        accountId: mockAccountId,
        userId: 'admin-1',
        type: 'agent-credits-insufficient',
        text: '',
        read: false,
        config: {
          accountName: 'Test Account',
          timestamp: expect.any(String),
        },
      })
    })

    it('should return early when no admin users found', async () => {
      mockedUserResource.findAdminUsers.mockResolvedValue([])

      await agentCreditsService.notifyAdminsAboutInsufficientCredits(mockAccountId)

      expect(agentCreditsService.logger.log).toHaveBeenCalledWith(
        `No admin users found for account: ${mockAccountId}`,
        'warn',
      )
      expect(mockedNotificationResource.create).not.toHaveBeenCalled()
    })

    it('should return early when account not found', async () => {
      const mockAdminUsers = [{ id: 'admin-1' }]
      mockedUserResource.findAdminUsers.mockResolvedValue(mockAdminUsers as any)
      mockedAccountResource.findById.mockResolvedValue(null)

      await agentCreditsService.notifyAdminsAboutInsufficientCredits(mockAccountId)

      expect(agentCreditsService.logger.log).toHaveBeenCalledWith(`Account not found: ${mockAccountId}`, 'warn')
      expect(mockedNotificationResource.create).not.toHaveBeenCalled()
    })

    it('should throw error on failure', async () => {
      mockedUserResource.findAdminUsers.mockRejectedValue(new Error('Database error'))

      await expect(agentCreditsService.notifyAdminsAboutInsufficientCredits(mockAccountId)).rejects.toThrow(
        'Database error',
      )
      expect(agentCreditsService.logger.log).toHaveBeenCalledWith(
        `Error creating agent credits notifications for account ${mockAccountId}: Database error`,
        'error',
      )
    })
  })

  describe('hasNotificationBeenSentToday', () => {
    it('should return true when notification exists for today', async () => {
      mockedNotificationResource.findMany.mockResolvedValue([{ id: 'notification-1' }])

      const result = await agentCreditsService.hasNotificationBeenSentToday(mockAccountId)

      expect(result).toBe(true)
      expect(mockedNotificationResource.findMany).toHaveBeenCalledWith({
        where: {
          accountId: mockAccountId,
          type: 'agent-credits-insufficient',
          createdAt: {
            $gte: expect.any(Date),
          },
        },
        limit: 1,
      })
    })

    it('should return false when no notification exists for today', async () => {
      mockedNotificationResource.findMany.mockResolvedValue([])

      const result = await agentCreditsService.hasNotificationBeenSentToday(mockAccountId)

      expect(result).toBe(false)
    })

    it('should return false on error', async () => {
      mockedNotificationResource.findMany.mockRejectedValue(new Error('Database error'))

      const result = await agentCreditsService.hasNotificationBeenSentToday(mockAccountId)

      expect(result).toBe(false)
      expect(agentCreditsService.logger.log).toHaveBeenCalledWith(
        `Error checking existing notifications for account ${mockAccountId}: Database error`,
        'error',
      )
    })
  })

  describe('recordInsufficientCreditsAttempt', () => {
    it('should notify admins when no notification sent today', async () => {
      const hasNotificationSpy = jest
        .spyOn(agentCreditsService, 'hasNotificationBeenSentToday')
        .mockResolvedValue(false)
      const notifyAdminsSpy = jest
        .spyOn(agentCreditsService, 'notifyAdminsAboutInsufficientCredits')
        .mockResolvedValue()

      await agentCreditsService.recordInsufficientCreditsAttempt(mockAccountId)

      expect(hasNotificationSpy).toHaveBeenCalledWith(mockAccountId)
      expect(notifyAdminsSpy).toHaveBeenCalledWith(mockAccountId)
      expect(agentCreditsService.logger.log).toHaveBeenCalledWith(
        `First insufficient credits attempt of the day for account: ${mockAccountId}`,
        'info',
      )
    })

    it('should not notify admins when notification already sent today', async () => {
      const hasNotificationSpy = jest.spyOn(agentCreditsService, 'hasNotificationBeenSentToday').mockResolvedValue(true)
      const notifyAdminsSpy = jest
        .spyOn(agentCreditsService, 'notifyAdminsAboutInsufficientCredits')
        .mockResolvedValue()

      await agentCreditsService.recordInsufficientCreditsAttempt(mockAccountId)

      expect(hasNotificationSpy).toHaveBeenCalledWith(mockAccountId)
      expect(notifyAdminsSpy).not.toHaveBeenCalled()
      expect(agentCreditsService.logger.log).toHaveBeenCalledWith(
        `Notification already sent today for account: ${mockAccountId}`,
        'info',
      )
    })

    it('should handle errors gracefully', async () => {
      jest.spyOn(agentCreditsService, 'hasNotificationBeenSentToday').mockRejectedValue(new Error('Database error'))

      await agentCreditsService.recordInsufficientCreditsAttempt(mockAccountId)

      expect(agentCreditsService.logger.log).toHaveBeenCalledWith(
        `Error recording insufficient credits attempt for account ${mockAccountId}: Database error`,
        'error',
      )
    })
  })
})
