jest.mock('openai', () => {
  return {
    OpenAI: jest.fn().mockImplementation(() => ({
      chat: {
        completions: {
          create: jest.fn(),
        },
      },
    })),
  }
})

jest.mock('../../../../../core/config', () => ({
  __esModule: true,
  default: jest.fn(),
}))

jest.mock('../../../../../core/resources/creditMovementResource', () => ({
  __esModule: true,
  default: {
    createDebit: jest.fn(),
  },
}))

jest.mock('../../../../../core/services/agent/AgentCreditsService', () => ({
  __esModule: true,
  default: jest.fn().mockImplementation(() => ({
    limitReached: jest.fn(),
  })),
}))

import { OpenAI } from 'openai'
import { AgentConfig } from '../../../../../core/services/agent/utils'
import SuggestAgentPromptService from '../../../../../core/services/agent/SuggestAgentPromptService'
import config from '../../../../../core/config'
import AgentCreditsService from '../../../../../core/services/agent/AgentCreditsService'

const MockedOpenAI = jest.mocked(OpenAI)
const mockedConfig = jest.mocked(config)
const MockedAgentCreditsService = jest.mocked(AgentCreditsService)
const mockedAccountId = 'a12fe3e2-3cd7-48aa-a49b-c2b0e1e42cfb'

describe('SuggestAgentPromptService', () => {
  let suggestAgentPromptService: SuggestAgentPromptService
  let mockOpenAIInstance: any
  let mockAgentCreditsService: any

  beforeEach(() => {
    jest.clearAllMocks()

    mockOpenAIInstance = {
      chat: {
        completions: {
          create: jest.fn(),
        },
      },
    }

    mockAgentCreditsService = {
      limitReached: jest.fn(),
    }

    MockedOpenAI.mockImplementation(() => mockOpenAIInstance)
    MockedAgentCreditsService.mockImplementation(() => mockAgentCreditsService)

    mockedConfig.mockImplementation((key: string) => {
      if (key === 'apiKeyOpenIA') return 'test-api-key'
      if (key === 'blockAiConsumption') return true
      return undefined
    })

    suggestAgentPromptService = new SuggestAgentPromptService()
    suggestAgentPromptService.logger = { log: jest.fn() } as any
    // Inject the mocked service using property access since it's private
    ;(suggestAgentPromptService as any).agentCreditsService = mockAgentCreditsService
  })

  afterEach(() => {
    jest.clearAllMocks()
  })

  describe('getPrompt', () => {
    it('should throw error when agent limit is reached', async () => {
      const payload: AgentConfig = {
        voiceTone: 'neutral',
        languageType: 'neutral',
        function: 'support',
        companySegment: 'testSegment',
        companySubject: 'testSubject',
        companyServices: 'testServices',
        prompt: 'You are a helpful assistant.',
        maxAttempts: 3,
        actions: [{ action: { id: '1', name: 'greet', description: 'Greet the user', type: 'default' } }],
      }

      mockAgentCreditsService.limitReached.mockResolvedValue(true)

      await expect(suggestAgentPromptService.getPrompt(payload, mockedAccountId)).rejects.toThrow('Agent limit reached')

      expect(mockAgentCreditsService.limitReached).toHaveBeenCalledWith(mockedAccountId, true)
      expect(suggestAgentPromptService.logger.log).toHaveBeenCalledWith('The agent limit has been reached', 'warn')
    })

    it('should throw error if companySegment, companySubject, or companyServices is missing', async () => {
      const payload: AgentConfig = {
        voiceTone: 'neutral',
        languageType: 'neutral',
        function: 'support',
        companySegment: '',
        companySubject: '',
        companyServices: '',
        prompt: 'You are a helpful assistant.',
        maxAttempts: 3,
        actions: [{ action: { id: '1', name: 'greet', description: 'Greet the user', type: 'default' } }],
      }

      await expect(suggestAgentPromptService.getPrompt(payload, mockedAccountId)).rejects.toThrow(
        'Missing required fields in payload: companySegment, companySubject, companyServices',
      )
    })

    it('should throw error if voiceTone, languageType, or function is invalid', async () => {
      const payload: AgentConfig = {
        voiceTone: 'invalidTone',
        languageType: 'invalidLanguage',
        function: 'invalidFunction',
        companySegment: 'testSegment',
        companySubject: 'testSubject',
        companyServices: 'testServices',
        prompt: 'You are a helpful assistant.',
        maxAttempts: 3,
        actions: [{ action: { id: '1', name: 'greet', description: 'Greet the user', type: 'default' } }],
      }

      await expect(suggestAgentPromptService.getPrompt(payload, mockedAccountId)).rejects.toThrow(
        'Invalid agent configuration: function, voiceTone, languageType',
      )
    })

    it('should return a valid prompt', async () => {
      const payload: AgentConfig = {
        voiceTone: 'neutral',
        languageType: 'neutral',
        function: 'support',
        companySegment: 'testSegment',
        companySubject: 'testSubject',
        companyServices: 'testServices',
        maxAttempts: 3,
        prompt: 'You are a helpful assistant.',
        actions: [{ action: { id: '1', name: 'greet', description: 'Greet the user', type: 'default' } }],
      }

      const mockOpenAIResponse = {
        choices: [
          {
            message: {
              content: 'Suggested prompt',
            },
          },
        ],
      }

      mockAgentCreditsService.limitReached.mockResolvedValue(false)
      mockOpenAIInstance.chat.completions.create.mockResolvedValue(mockOpenAIResponse)

      const result = await suggestAgentPromptService.getPrompt(payload, mockedAccountId)
      expect(result).toBe('Suggested prompt')
      expect(mockOpenAIInstance.chat.completions.create).toHaveBeenCalledTimes(1)
      expect(mockAgentCreditsService.limitReached).toHaveBeenCalledWith(mockedAccountId, true)
    })

    it('should work without accountId when blockAiConsumption is disabled', async () => {
      const payload: AgentConfig = {
        voiceTone: 'neutral',
        languageType: 'neutral',
        function: 'support',
        companySegment: 'testSegment',
        companySubject: 'testSubject',
        companyServices: 'testServices',
        maxAttempts: 3,
        prompt: 'You are a helpful assistant.',
        actions: [{ action: { id: '1', name: 'greet', description: 'Greet the user', type: 'default' } }],
      }

      const mockOpenAIResponse = {
        choices: [
          {
            message: {
              content: 'Suggested prompt without credits check',
            },
          },
        ],
      }

      mockOpenAIInstance.chat.completions.create.mockResolvedValue(mockOpenAIResponse)

      const result = await suggestAgentPromptService.getPrompt(payload)
      expect(result).toBe('Suggested prompt without credits check')
      expect(mockOpenAIInstance.chat.completions.create).toHaveBeenCalledTimes(1)
      expect(mockAgentCreditsService.limitReached).not.toHaveBeenCalled()
    })
  })
})
