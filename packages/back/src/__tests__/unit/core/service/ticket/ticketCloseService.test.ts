import TicketCloseService from '../../../../../core/services/ticket/TicketCloseService'
import ticketResource from '../../../../../core/resources/ticketResource'
import clientFeedbackResource from '../../../../../core/resources/clientFeedbackResource'
import ticketTransfersResource from '../../../../../core/resources/ticketTransfersResource'
import messageResource from '../../../../../core/resources/messageResource'
import contactResource from '../../../../../core/resources/contactResource'
import accountResource from '../../../../../core/resources/accountResource'
import { getCloseTicketQueue } from '../../../../../core/queues/tickets'
import HttpJobsDispatcher from '../../../../../core/services/jobs/http/HttpJobsDispatcher'
import { getWaitingTime, getTicketTime } from '../../../../../core/services/ticket/TicketTransferService'
import { Container } from 'typedi'

jest.mock('../../../../../core/resources/ticketResource', () => ({
  maybeEventTransaction: jest.fn(),
  nestedTransaction: jest.fn(),
  updateById: jest.fn(),
  findById: jest.fn(),
  emitClosed: jest.fn(),
}))
jest.mock('../../../../../core/resources/clientFeedbackResource', () => ({
  create: jest.fn(),
}))
jest.mock('../../../../../core/resources/ticketTransfersResource', () => ({
  update: jest.fn(),
  create: jest.fn(),
}))
jest.mock('../../../../../core/resources/messageResource', () => ({
  createTicketCloseMessage: jest.fn(),
  findById: jest.fn(),
}))
jest.mock('../../../../../core/resources/contactResource', () => ({
  updateById: jest.fn(),
}))
jest.mock('../../../../../core/resources/accountResource', () => ({
  findById: jest.fn(),
}))
jest.mock('../../../../../core/queues/tickets', () => ({
  getCloseTicketQueue: jest.fn(),
}))
jest.mock('../../../../../core/services/jobs/http/HttpJobsDispatcher', () => jest.fn())
jest.mock('../../../../../core/services/ticket/TicketTransferService', () => ({
  getWaitingTime: jest.fn(),
  getTicketTime: jest.fn(),
}))

const mockRun = jest.fn()
const mockMaybeEventTransaction = jest.fn()
const mockNestedTransaction = jest.fn()
const mockUpdate = jest.fn()
const mockCreateTicketCloseMessage = jest.fn()
const mockCreate = jest.fn()
const mockUpdateById = jest.fn()
const mockFindById = jest.fn()
const mockEmitClosed = jest.fn()
const mockAddTicketTopics = jest.fn()
const mockDispatch = jest.fn()
const mockClientFeedbackCreate = jest.fn()
const mockContactUpdateById = jest.fn()

const mockHttpJobsDispatcher = { dispatch: jest.fn() }

beforeEach(() => {
  jest.clearAllMocks()

  // Setup ticketResource mocks
  ticketResource.maybeEventTransaction.mockImplementation((fn, eventTransaction) => fn('eventTx'))
  ticketResource.nestedTransaction.mockImplementation((fn, transaction) => fn('tx'))
  ticketResource.updateById = mockUpdateById
  ticketResource.findById = mockFindById
  ticketResource.emitClosed = mockEmitClosed

  // Setup ticketTransfersResource mocks
  ticketTransfersResource.update = mockUpdate
  ticketTransfersResource.create = mockCreate

  // Setup messageResource mocks
  messageResource.createTicketCloseMessage = mockCreateTicketCloseMessage
  messageResource.findById = jest.fn()

  // Setup clientFeedbackResource mocks
  clientFeedbackResource.create = mockClientFeedbackCreate

  // Setup contactResource mocks
  contactResource.updateById = mockContactUpdateById

  // Setup accountResource mocks
  accountResource.findById = jest.fn()

  // Setup getCloseTicketQueue
  getCloseTicketQueue.mockReturnValue({ run: mockRun })

  HttpJobsDispatcher.mockImplementation(() => mockHttpJobsDispatcher)

  // Força o Container a retornar o mock correto
  Container.get = jest.fn((klass) => {
    console.log('\n\n CONTAINER GET CALLED WITH:', klass.name)
    if (klass === HttpJobsDispatcher) return mockHttpJobsDispatcher
    return {}
  })
})

function makeContact(overrides = {}) {
  return {
    id: 'contact1',
    currentTicketId: 'ticket1',
    currentTicket: {
      id: 'ticket1',
      startedAt: new Date('2023-01-01T10:00:00Z'),
      metrics: {},
      departmentId: 'dep1',
      userId: 'user1',
      accountId: 'acc1',
      currentTicketTransfer: {
        id: 'tt1',
        metrics: {},
        firstMessageId: 'msg1',
      },
    },
    serviceId: 'svc1',
    accountId: 'acc1',
    service: { type: 'webchat' },
    ...overrides,
  }
}

function setupRunImpl(fn) {
  mockRun.mockImplementation(fn)
}

describe('TicketCloseService', () => {
  let service: TicketCloseService

  beforeEach(() => {
    service = new TicketCloseService()
    // Default mocks for ticketResource
    mockUpdate.mockResolvedValue({})
    mockCreateTicketCloseMessage.mockResolvedValue({ id: 'closeMsg1' })
    mockCreate.mockResolvedValue({})
    mockUpdateById.mockResolvedValue({})
    mockFindById.mockResolvedValue({
      addTicketTopics: mockAddTicketTopics,
    })
    mockAddTicketTopics.mockResolvedValue(undefined)
    mockClientFeedbackCreate.mockResolvedValue({})
    mockContactUpdateById.mockResolvedValue({ updated: true })
    accountResource.findById.mockResolvedValue({ settings: { flags: { 'enable-smart-summary': true } } })
    getWaitingTime.mockResolvedValue(100)
    getTicketTime.mockReturnValue(200)
  })

  it('throws if contact is missing', async () => {
    setupRunImpl(() => Promise.resolve())
    const data = {
      contact: null as any,
    }
    const options = {}
    await expect(service.closeTicket(data, options)).rejects.toThrow('Contact is required')
  })

  it('throws if contact.currentTicketId is missing', async () => {
    const contact = makeContact({ currentTicketId: undefined })
    setupRunImpl(() => Promise.resolve())
    await expect(service.closeTicket({ contact })).rejects.toThrow('does not have an open ticket')
  })

  it('throws if contact.currentTicket is missing', async () => {
    const contact = makeContact({ currentTicket: undefined })
    setupRunImpl(() => Promise.resolve())
    await expect(service.closeTicket({ contact })).rejects.toThrow('contact.currentTicket is required')
  })

  it('throws if currentTicket.currentTicketTransfer is missing', async () => {
    const contact = makeContact()
    contact.currentTicket.currentTicketTransfer = undefined
    setupRunImpl(() => Promise.resolve())
    await expect(service.closeTicket({ contact })).rejects.toThrow('currentTicket.currentTicketTransfer is required')
  })

  it('runs the close ticket flow and returns updated contact', async () => {
    const contact = makeContact()
    messageResource.findById.mockResolvedValue({ id: 'msg1' })
    setupRunImpl((fn) => fn())
    const result = await service.closeTicket({
      contact,
      byUserId: 'user2',
      comments: 'closing',
      ticketTopicIds: ['topic1', 'topic2'],
      aiSummaryRating: 'good',
    })
    expect(getCloseTicketQueue).toHaveBeenCalledWith('contact1')
    expect(ticketResource.maybeEventTransaction).toHaveBeenCalled()
    expect(ticketResource.nestedTransaction).toHaveBeenCalled()
    expect(getWaitingTime).toHaveBeenCalled()
    expect(getTicketTime).toHaveBeenCalled()
    expect(accountResource.findById).toHaveBeenCalledWith('acc1', { attributes: ['settings'] })
    expect(mockUpdate).toHaveBeenCalledWith(
      contact.currentTicket.currentTicketTransfer,
      expect.objectContaining({
        endedAt: expect.any(Date),
        metrics: expect.objectContaining({ ticketTime: 200, waitingTime: 100 }),
      }),
      expect.any(Object),
    )
    expect(mockCreateTicketCloseMessage).toHaveBeenCalledWith(
      expect.objectContaining({ contact, ticketId: 'ticket1', contactId: 'contact1' }),
      expect.any(Object),
    )
    expect(mockCreate).toHaveBeenCalledWith(
      expect.objectContaining({
        action: 'closed',
        accountId: 'acc1',
        fromDepartmentId: 'dep1',
        fromUserId: 'user1',
        ticketId: 'ticket1',
        byUserId: 'user2',
        comments: 'closing',
        startedAt: expect.any(Date),
      }),
      expect.any(Object),
    )
    expect(mockUpdateById).toHaveBeenCalledWith(
      'ticket1',
      expect.objectContaining({
        isOpen: false,
        comments: 'closing',
        endedAt: expect.any(Date),
        currentTicketTransferId: null,
        metrics: expect.objectContaining({ ticketTime: expect.any(Number) }),
      }),
      expect.any(Object),
    )
    expect(mockFindById).toHaveBeenCalledWith('ticket1', expect.objectContaining({ include: expect.any(Array) }))
    expect(mockAddTicketTopics).toHaveBeenCalledWith(['topic1', 'topic2'], expect.any(Object))
    expect(mockClientFeedbackCreate).toHaveBeenCalledWith({ ticketId: 'ticket1', feedback: 'good' }, expect.any(Object))
    expect(mockContactUpdateById).toHaveBeenCalledWith('contact1', { currentTicketId: null }, expect.any(Object))
    expect(mockEmitClosed).toHaveBeenCalled()
    expect(result).toEqual({ updated: true })
  })

  it('does not emit closed event if dontEmit is true', async () => {
    const contact = makeContact()
    setupRunImpl((fn) => fn())
    await service.closeTicket({ contact }, { dontEmit: true })
    expect(mockEmitClosed).not.toHaveBeenCalled()
  })

  it('handles ticketTopicIds as string', async () => {
    const contact = makeContact()
    setupRunImpl((fn) => fn())
    await service.closeTicket({ contact, ticketTopicIds: 'topic1' })
    expect(mockAddTicketTopics).toHaveBeenCalledWith(['topic1'], expect.any(Object))
  })

  it('does not add client feedback if aiSummaryRating is not provided', async () => {
    const contact = makeContact()
    setupRunImpl((fn) => fn())
    await service.closeTicket({ contact })
    expect(mockClientFeedbackCreate).not.toHaveBeenCalled()
  })

  it('does not dispatch SendMailCloseTicketWebchat if service type is not webchat', async () => {
    const contact = makeContact({ service: { type: 'whatsapp' } })
    setupRunImpl((fn) => fn())
    await service.closeTicket({ contact })
    expect(mockHttpJobsDispatcher.dispatch).not.toHaveBeenCalled()
  })

  it('does not dispatch summary job if enable-smart-summary is false', async () => {
    accountResource.findById.mockResolvedValue({ settings: { flags: { 'enable-smart-summary': false } } })
    const contact = makeContact()
    setupRunImpl((fn) => fn())
    await service.closeTicket({ contact })
    expect(mockDispatch).not.toHaveBeenCalledWith('queued-start-summary', expect.anything(), expect.anything())
  })
})
