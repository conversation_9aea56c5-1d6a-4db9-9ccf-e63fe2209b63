import 'reflect-metadata'
import CheckServiceDisconnectionJob from '../../../../../../microServices/workers/jobs/whatsapp/CheckServiceDisconnectionJob'
import serviceResource from '../../../../../../core/resources/serviceResource'
import notificationResource from '../../../../../../core/resources/notificationResource'
import userResource from '../../../../../../core/resources/userResource'
import queuedAsyncMap from '../../../../../../core/utils/array/queuedAsyncMap'
import iteratePaginated from '../../../../../../core/utils/iteratePaginated'

jest.mock('../../../../../../core/resources/serviceResource', () => ({ findMany: jest.fn(), updateById: jest.fn() }))
jest.mock('../../../../../../core/resources/notificationResource', () => ({ create: jest.fn() }))
jest.mock('../../../../../../core/resources/userResource', () => ({ findManyPaginated: jest.fn() }))
jest.mock('../../../../../../core/utils/array/queuedAsyncMap', () => jest.fn((arr, fn) => Promise.all(arr.map(fn))))
jest.mock('../../../../../../core/utils/iteratePaginated', () => jest.fn())
jest.mock('../../../../../../core/dbSequelize/repositories/serviceRepository', () => jest.fn())
const logger = {
  log: jest.fn(),
}
const redis = { get: jest.fn(), set: jest.fn() }

describe('CheckServiceDisconnectionJob', () => {
  let job: CheckServiceDisconnectionJob
  beforeEach(() => {
    job = new CheckServiceDisconnectionJob()
    job['logger'] = logger as any
    job['redis'] = redis as any
    jest.clearAllMocks()
  })

  it('should log and return if no disconnected services found', async () => {
    ;(serviceResource.findMany as jest.Mock).mockResolvedValue([])
    logger.log = jest.fn()

    await job.handle()

    expect(logger.log).toHaveBeenCalledWith('Starting service disconnection check', 'info')
    expect(logger.log).toHaveBeenCalledWith('No disconnected services found', 'info')
    expect(queuedAsyncMap).not.toHaveBeenCalled()
  })

  it('should process disconnected services', async () => {
    const services = [
      { id: '1', name: 'svc', accountId: 'acc', data: {} },
      { id: '2', name: 'svc2', accountId: 'acc2', data: {} },
      { id: '3', name: 'svc3', accountId: 'acc3', data: {} },
    ]

    ;(serviceResource.findMany as jest.Mock).mockResolvedValue(services)
    logger.log = jest.fn()
    ;(queuedAsyncMap as jest.Mock).mockResolvedValue(undefined)

    await job.handle()

    expect(logger.log).toHaveBeenCalledWith('Found 3 disconnected services', 'info')
    expect(queuedAsyncMap).toHaveBeenCalledWith(services, expect.any(Function), 5)
    expect(logger.log).toHaveBeenCalledWith('Service disconnection check finished', 'info')
  })

  it('should set tracking key and log if first detection', async () => {
    const service = { id: '1', name: 'svc', accountId: 'acc', data: {} }
    redis.get = jest.fn().mockResolvedValue(null)
    redis.set = jest.fn().mockResolvedValue(undefined)
    logger.log = jest.fn()

    await job['processDisconnectedService'](service as any)

    expect(redis.set).toHaveBeenCalledWith(
      'service-disconnection:1:first-detected',
      expect.any(String),
      24 * 60 * 60 * 1000,
    )
    expect(logger.log).toHaveBeenCalledWith(
      `Service 1 (svc) disconnection detected, starting 3-minute tolerance period`,
      'info',
    )
  })

  it('should skip if within tolerance period', async () => {
    const now = new Date()
    const firstDetectedAt = new Date(now.getTime() - 1 * 60 * 1000).toISOString() // 1 min ago
    const service = { id: '1', name: 'svc', accountId: 'acc', data: {} }
    redis.get = jest.fn().mockResolvedValue(firstDetectedAt)
    logger.log = jest.fn()

    await job['processDisconnectedService'](service as any)

    expect(logger.log).not.toHaveBeenCalledWith(expect.stringContaining('sending notifications'), 'info')
  })

  it('should skip if already notified', async () => {
    const now = new Date()
    const firstDetectedAt = new Date(now.getTime() - 5 * 60 * 1000).toISOString() // 5 min ago
    const service = { id: '1', name: 'svc', accountId: 'acc', data: { notifiedServiceDisconnectionAt: now } }
    redis.get = jest.fn().mockResolvedValue(firstDetectedAt)
    logger.log = jest.fn()

    await job['processDisconnectedService'](service as any)

    expect(logger.log).toHaveBeenCalledWith('Service 1 already notified, skipping', 'debug')
  })

  it('should send notifications after tolerance period', async () => {
    const now = new Date()
    const firstDetectedAt = new Date(now.getTime() - 5 * 60 * 1000).toISOString() // 5 min ago
    const service = { id: '1', name: 'svc', accountId: 'acc', data: {} }
    redis.get = jest.fn().mockResolvedValue(firstDetectedAt)
    logger.log = jest.fn()
    job['sendDisconnectionNotifications'] = jest.fn().mockResolvedValue(undefined)

    await job['processDisconnectedService'](service as any)

    expect(logger.log).toHaveBeenCalledWith(expect.stringContaining('has been disconnected for'), 'info')
    expect(job['sendDisconnectionNotifications']).toHaveBeenCalledWith(service)
  })

  it('should notify users and update service', async () => {
    const service = { id: '1', name: 'svc', accountId: 'acc', data: {} }
    const users = [{ id: 'u1' }, { id: 'u2' }]
    ;(userResource.findManyPaginated as jest.Mock).mockImplementation(({ page }) =>
      page === 1 ? { data: users, total: 2, page: 1, perPage: 100 } : { data: [], total: 2, page: 2, perPage: 100 },
    )
    ;(iteratePaginated as jest.Mock).mockImplementation(async (_fetch, cb) => {
      for (const user of users) await cb(user)
    })
    notificationResource.create = jest.fn().mockResolvedValue(undefined)
    logger.log = jest.fn()
    serviceResource.updateById = jest.fn().mockResolvedValue(undefined)

    await job['sendDisconnectionNotifications'](service as any)

    expect(notificationResource.create).toHaveBeenCalledTimes(users.length)
    expect(logger.log).toHaveBeenCalledWith(`User u1 successfully notified about service disconnection`, 'info')
    expect(logger.log).toHaveBeenCalledWith(`User u2 successfully notified about service disconnection`, 'info')
    expect(serviceResource.updateById).toHaveBeenCalledWith('1', {
      data: expect.objectContaining({
        notifiedServiceDisconnectionAt: expect.any(Date),
      }),
    })
  })
})
