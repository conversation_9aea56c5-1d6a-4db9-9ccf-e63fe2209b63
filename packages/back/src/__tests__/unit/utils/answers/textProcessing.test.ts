import { parseAiTextForExport, getReasonText } from '../../../../core/utils/answers/textProcessing'

describe('Answer Text Processing Utils', () => {
  describe('parseAiTextForExport', () => {
    it('should return empty string when aiText is null', () => {
      expect(parseAiTextForExport(null)).toBe('')
    })

    it('should return empty string when aiText is undefined', () => {
      expect(parseAiTextForExport(undefined)).toBe('')
    })

    it('should return empty string when aiText is empty', () => {
      expect(parseAiTextForExport('')).toBe('')
    })

    it('should convert **** to ---', () => {
      const input = 'Text before ****'
      const expected = 'Text before ---'
      expect(parseAiTextForExport(input)).toBe(expected)
    })

    it('should convert \\n to spaces', () => {
      const input = 'Line one\nLine two'
      const expected = 'Line one Line two'
      expect(parseAiTextForExport(input)).toBe(expected)
    })

    it('should remove bold markdown formatting', () => {
      const input = '**Bold text**'
      const expected = 'Bold text'
      expect(parseAiTextForExport(input)).toBe(expected)
    })

    it('should handle multiple bold formatting in same text', () => {
      const input = '**First bold** and **second bold**'
      const expected = 'First bold and second bold'
      expect(parseAiTextForExport(input)).toBe(expected)
    })

    it('should handle complex markdown formatting', () => {
      const input = '**Cliente insatisfeito**\n\nResposta demorada.\n\n****'
      const expected = 'Cliente insatisfeito  Resposta demorada.  ---'
      expect(parseAiTextForExport(input)).toBe(expected)
    })

    it('should trim whitespace from result', () => {
      const input = '  **Text**  \n  '
      const expected = 'Text'
      expect(parseAiTextForExport(input)).toBe(expected)
    })

    it('should handle text with no markdown formatting', () => {
      const input = 'Simple text without formatting'
      const expected = 'Simple text without formatting'
      expect(parseAiTextForExport(input)).toBe(expected)
    })

    it('should handle multiple line breaks', () => {
      const input = 'Line 1\n\nLine 2\n\n\nLine 3'
      const expected = 'Line 1  Line 2   Line 3'
      expect(parseAiTextForExport(input)).toBe(expected)
    })

    it('should handle multiple separators', () => {
      const input = 'Section 1 **** Section 2 **** Section 3'
      const expected = 'Section 1 --- Section 2 --- Section 3'
      expect(parseAiTextForExport(input)).toBe(expected)
    })
  })

  describe('getReasonText', () => {
    it('should return aiText when aiGenerated is true and aiText exists', () => {
      const answer = {
        aiGenerated: true,
        aiText: '**Feedback da IA**',
        reason: 'Motivo original',
      }
      expect(getReasonText(answer)).toBe('Feedback da IA')
    })

    it('should return reason when aiGenerated is false', () => {
      const answer = {
        aiGenerated: false,
        aiText: 'Texto IA',
        reason: 'Motivo original',
      }
      expect(getReasonText(answer)).toBe('Motivo original')
    })

    it('should return reason when aiGenerated is true but aiText is null', () => {
      const answer = {
        aiGenerated: true,
        aiText: null,
        reason: 'Motivo original',
      }
      expect(getReasonText(answer)).toBe('Motivo original')
    })

    it('should return reason when aiGenerated is true but aiText is empty', () => {
      const answer = {
        aiGenerated: true,
        aiText: '',
        reason: 'Motivo original',
      }
      expect(getReasonText(answer)).toBe('Motivo original')
    })

    it('should return empty string when no reason or aiText', () => {
      const answer = {
        aiGenerated: false,
        reason: null,
      }
      expect(getReasonText(answer)).toBe('')
    })

    it('should return empty string when reason is undefined', () => {
      const answer = {
        aiGenerated: false,
        reason: undefined,
      }
      expect(getReasonText(answer)).toBe('')
    })

    it('should handle complex aiText with markdown', () => {
      const answer = {
        aiGenerated: true,
        aiText: '**Cliente insatisfeito**\n\nProblemas de comunicação\n\n****',
        reason: 'Should not use this',
      }
      expect(getReasonText(answer)).toBe('Cliente insatisfeito  Problemas de comunicação  ---')
    })

    it('should prioritize aiText over reason when both exist and aiGenerated is true', () => {
      const answer = {
        aiGenerated: true,
        aiText: 'IA feedback',
        reason: 'Manual reason',
      }
      expect(getReasonText(answer)).toBe('IA feedback')
    })

    it('should handle aiGenerated undefined (falsy)', () => {
      const answer = {
        aiGenerated: undefined,
        aiText: 'IA feedback',
        reason: 'Manual reason',
      }
      expect(getReasonText(answer)).toBe('Manual reason')
    })

    it('should handle missing aiGenerated property', () => {
      const answer = {
        aiText: 'IA feedback',
        reason: 'Manual reason',
      }
      expect(getReasonText(answer)).toBe('Manual reason')
    })

    it('should handle empty object', () => {
      const answer = {}
      expect(getReasonText(answer)).toBe('')
    })

    it('should handle answer with only aiGenerated true', () => {
      const answer = {
        aiGenerated: true,
      }
      expect(getReasonText(answer)).toBe('')
    })
  })
})
