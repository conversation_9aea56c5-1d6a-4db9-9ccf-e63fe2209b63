import { validateBotCheckbox } from '../../../core/zod-schemas/checkboxSchema'

describe('validateBotCheckbox', () => {
  const settings = {
    options: [
      { id: '1', label: 'Apple' },
      { id: '2', label: 'Banana' },
      { id: '3', label: 'Orange' },
    ],
  }

  it('should return ids for valid single selection', () => {
    expect(validateBotCheckbox('Apple', settings)).toEqual(['1'])
    expect(validateBotCheckbox('banana', settings)).toEqual(['2'])
  })

  it('should return ids for valid multiple selections', () => {
    expect(validateBotCheckbox('Apple, Banana', settings)).toEqual(['1', '2'])
    expect(validateBotCheckbox('Orange,Apple', settings)).toEqual(['3', '1'])
  })

  it('should return false if any label does not exist', () => {
    expect(validateBotCheckbox('Apple, Pineapple', settings)).toBe(false)
    expect(validateBotCheckbox('Pineapple', settings)).toBe(false)
  })

  it('should return false if there are duplicate labels', () => {
    expect(validateBotCheckbox('Apple, Apple', settings)).toBe(false)
  })

  it('should return false if settings is missing or options is not an array', () => {
    expect(validateBotCheckbox('Apple', undefined)).toBe(false)
    expect(validateBotCheckbox('Apple', { options: null })).toBe(false)
  })

  it('should return false if input is empty or only spaces', () => {
    expect(validateBotCheckbox('', settings)).toBe(false)
    expect(validateBotCheckbox('   ', settings)).toBe(false)
  })
})
