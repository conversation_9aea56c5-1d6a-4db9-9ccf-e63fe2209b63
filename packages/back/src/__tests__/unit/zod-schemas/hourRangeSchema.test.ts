import { ZodSchema } from 'zod'
import { hourRangeSchema } from '../../../core/zod-schemas'
import { validadeBotHourRange } from '../../../core/zod-schemas/hourRangeSchema'

type SetupSut = {
  sut: ZodSchema
}

describe('Validation HOUR INTERVAL Zod Schema', () => {
  const makeSut = (): SetupSut => ({
    sut: hourRangeSchema,
  })

  it('should be able return success with a valid interval hour', () => {
    const { sut } = makeSut()
    const validIntervalHour = {
      start: '20:00',
      end: '20:01',
    }

    expect(sut.safeParse(validIntervalHour).success).toBeTruthy()
  })

  it('should be able return error with an invalid interval hour (with HHhMM pattern)', () => {
    const { sut } = makeSut()
    const invalidIntervalHour = {
      start: '20h00',
      end: '21h00',
    }

    expect(sut.safeParse(invalidIntervalHour).success).toBeFalsy()
  })

  it('should be able return error with an invalid interval hour (with HH:MM:SS pattern)', () => {
    const { sut } = makeSut()
    const invalidIntervalHour = {
      start: '20:00:00',
      end: '20:00:01',
    }

    expect(sut.safeParse(invalidIntervalHour).success).toBeFalsy()
  })
})

describe('validadeBotHourRange', () => {
  it('should be able return success with a valid interval hour', () => {
    expect(validadeBotHourRange('08:00-09:00')).toEqual({ start: '08:00', end: '09:00' })
    expect(validadeBotHourRange('08:00 - 09:00')).toEqual({ start: '08:00', end: '09:00' })
  })

  it('should return an error when the end date is earlier than the start date.', () => {
    expect(validadeBotHourRange('09:00-08:00')).toBe(false)
  })

  it('should return error when invalid format', () => {
    expect(validadeBotHourRange('8:00-09:00')).toBe(false)
    expect(validadeBotHourRange('08:00-9:00')).toBe(false)
    expect(validadeBotHourRange('08:00-09:00:00')).toBe(false)
    expect(validadeBotHourRange('08h00-09h00')).toBe(false)
    expect(validadeBotHourRange('')).toBe(false)
  })
})
