import { validateBotList } from '../../../core/zod-schemas/listSchema'

describe('validateBotList', () => {
  const settings = {
    options: [
      { id: '1', label: 'Apple' },
      { id: '2', label: 'Banana' },
      { id: '3', label: 'Orange' },
    ],
  }

  it('should return the id for a valid label (case insensitive)', () => {
    expect(validateBotList('apple', settings)).toBe('1')
    expect(validateBotList('BANANA', settings)).toBe('2')
    expect(validateBotList('Orange', settings)).toBe('3')
  })

  it('should return false if label does not exist', () => {
    expect(validateBotList('Pineapple', settings)).toBe(false)
  })

  it('should return false if settings is missing', () => {
    expect(validateBotList('Apple', undefined)).toBe(false)
  })

  it('should return false if more than one option matches', () => {
    const duplicated = {
      options: [
        { id: '1', label: 'Apple' },
        { id: '2', label: 'Apple' },
      ],
    }
    expect(validateBotList('Apple', duplicated)).toBe(false)
  })

  it('should return false if foundItem is empty', () => {
    const emptySettings = { options: [] }
    expect(validateBotList('Apple', emptySettings)).toBe(false)
  })
})
