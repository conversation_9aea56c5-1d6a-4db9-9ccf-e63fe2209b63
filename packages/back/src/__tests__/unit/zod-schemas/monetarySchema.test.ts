import { validateBotMonetary } from '../../../core/zod-schemas/monetarySchema'

describe('validateBotMonetary', () => {
  it('should accept a valid monetary value with currency', () => {
    expect(validateBotMonetary('2,50/BRL')).toEqual({
      value: 'R$ 2,50',
      identifier: 'BRL',
      min: '',
      max: '',
    })
  })

  it('should accept a valid monetary value with currency and min/max', () => {
    expect(validateBotMonetary('10,00/BRL', { options: [], min: '5', max: '20' })).toEqual({
      value: 'R$ 10,00',
      identifier: 'BRL',
      min: 5,
      max: 20,
    })
  })

  it('should reject value with invalid currency identifier', () => {
    expect(validateBotMonetary('2,50/XXX')).toBe(false)
  })

  it('should reject value with invalid format', () => {
    expect(validateBotMonetary('2,50BRL')).toBe(false)
    expect(validateBotMonetary('BRL/2,50')).toBe(false)
    expect(validateBotMonetary('')).toBe(false)
  })

  it('should reject value below min', () => {
    expect(validateBotMonetary('1,00/BRL', { options: [], min: '2', max: '10' })).toBe(false)
  })

  it('should reject value above max', () => {
    expect(validateBotMonetary('100,00/BRL', { options: [], min: '2', max: '10' })).toBe(false)
  })
})
