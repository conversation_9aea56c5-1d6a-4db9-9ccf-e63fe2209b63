import { validateBotNumeric } from '../../../core/zod-schemas/numericSchema'

describe('validateBotNumeric', () => {
  const settings = { options: [], min: '1', max: '10' }

  it('should accept a valid number within range', () => {
    expect(validateBotNumeric('5', settings)).toEqual({ value: '5', min: '1', max: '10' })
    expect(validateBotNumeric('1', settings)).toEqual({ value: '1', min: '1', max: '10' })
    expect(validateBotNumeric('10', settings)).toEqual({ value: '10', min: '1', max: '10' })
  })

  it('should reject a number below min', () => {
    expect(validateBotNumeric('0', settings)).toBe(false)
    expect(validateBotNumeric('-1', settings)).toBe(false)
  })

  it('should reject a number above max', () => {
    expect(validateBotNumeric('11', settings)).toBe(false)
    expect(validateBotNumeric('100', settings)).toBe(false)
  })

  it('should reject non-numeric input', () => {
    expect(validateBotNumeric('abc', settings)).toBe(false)
    expect(validateBotNumeric('5a', settings)).toBe(false)
    expect(validateBotNumeric('', settings)).toBe(false)
  })

  it('should accept decimal numbers within range', () => {
    expect(validateBotNumeric('2.5', { options: [], min: '1', max: '3' })).toEqual({ value: '2.5', min: '1', max: '3' })
  })

  it('should reject decimal numbers out of range', () => {
    expect(validateBotNumeric('0.5', settings)).toBe(false)
    expect(validateBotNumeric('10.1', settings)).toBe(false)
  })
})
