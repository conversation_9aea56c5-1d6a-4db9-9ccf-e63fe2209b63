import { validateBotPhone } from '../../../core/zod-schemas/phoneSchema'

describe('validateBotPhone', () => {
  it('should return success with a valid phone format', () => {
    expect(validateBotPhone('+55/53996642113')).toEqual({ value: '(53) 99664-2113', identifier: '+55' })
  })

  it('should reject number without country code (DDI)', () => {
    expect(validateBotPhone('53996642113')).toBe(false)
  })

  it('should reject non-existent country code (DDI)', () => {
    expect(validateBotPhone('+99/53996642113')).toBe(false)
  })

  it('should reject number containing letters', () => {
    expect(validateBotPhone('+55/53A96642113')).toBe(false)
  })

  it('should reject number that is too short', () => {
    expect(validateBotPhone('+55/123')).toBe(false)
  })

  it('should reject alternative formats', () => {
    expect(validateBotPhone('+55-53996642113')).toBe(false)
  })

  it('should reject empty string', () => {
    expect(validateBotPhone('')).toBe(false)
  })
})
