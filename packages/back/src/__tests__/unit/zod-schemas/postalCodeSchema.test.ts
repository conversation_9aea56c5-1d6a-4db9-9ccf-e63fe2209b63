import { validateBotPostalCode } from '../../../core/zod-schemas/postalCodeSchema'

describe('validateBotPostalCode', () => {
  it('should accept a Brazilian postal code with slash', () => {
    expect(validateBotPostalCode('BR/65066-489')).toEqual({
      value: '65066-489',
      identifier: 'BR',
    })
  })

  it('should accept a USA postal code with slash', () => {
    expect(validateBotPostalCode('US/12345')).toEqual({
      value: '12345',
      identifier: 'US',
    })
  })

  it('should accept a valid Brazilian postal code without hyphen', () => {
    expect(validateBotPostalCode('BR/65066489')).toEqual({
      value: '65066-489',
      identifier: 'BR',
    })
  })

  it('should accept a valid Brazilian postal code with space', () => {
    expect(validateBotPostalCode('BR 65066-489')).toEqual({
      value: '65066-489',
      identifier: 'BR',
    })
  })

  it('should reject a postal code with too many digits', () => {
    expect(validateBotPostalCode('BR/650664899')).toBe(false)
  })

  it('should reject a postal code with too few digits', () => {
    expect(validateBotPostalCode('BR/6506489')).toBe(false)
  })

  it('should reject an unknown country identifier', () => {
    expect(validateBotPostalCode('XX/12345-678')).toBe(false)
  })

  it('should reject an invalid format (missing identifier)', () => {
    expect(validateBotPostalCode('65066-489')).toBe(false)
  })

  it('should reject an invalid format (missing slash or space)', () => {
    expect(validateBotPostalCode('BR65066-489')).toBe(false)
  })
})
