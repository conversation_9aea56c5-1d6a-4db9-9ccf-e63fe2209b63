import { KnowledgeBaseResource } from '../../core/resources/knowledgeBaseResource'

const mockHaystackIaApi = {
  suggestResponseByMessage: jest.fn(),
  suggestResponseByTicket: jest.fn(),
  dispatchTranscriptions: jest.fn(),
  getMessagesByTicket: jest.fn(),
}
const mockKbRepository = {} as any

jest.mock('../../../core/resources/BaseResource', () => jest.fn())
jest.mock('../../../core/resources/messageResource', () => ({ findMany: jest.fn() }))
jest.mock('../../../core/resources/creditMovementResource', () => ({ findMany: jest.fn() }))
jest.mock('../../../core/resources/copilotTranscriptionResource', () => jest.fn())
jest.mock('../../../core/services/logs/Logger', () => jest.fn())
jest.mock('../../../core/config', () => jest.fn())
jest.mock('../../../core/utils/array/queuedAsyncMap', () => jest.fn((arr, fn) => Promise.all(arr.map(fn))))
jest.mock('typedi', () => {
  return {
    Container: {
      get: jest.fn(),
    },
    Service: () => () => {},
    Inject: () => () => {},
  }
})

describe('KnowledgeBaseResource', () => {
  let resource: KnowledgeBaseResource

  beforeEach(() => {
    jest.clearAllMocks()
    resource = new KnowledgeBaseResource(mockHaystackIaApi as any, mockKbRepository)
  })

  it('should call haystackIaApi.suggestResponseByMessage', async () => {
    mockHaystackIaApi.suggestResponseByMessage.mockResolvedValue({ message: 'ok', subjectNotFound: false })
    const result = await resource.suggestResponseByMessage('msg1', 'acc1', false)
    expect(mockHaystackIaApi.suggestResponseByMessage).toHaveBeenCalledWith('msg1', 'acc1', false)
    expect(result).toEqual({ message: 'ok', subjectNotFound: false })
  })

  it('should call haystackIaApi.suggestResponseByTicket', async () => {
    const messages = [{ id: 'msg1', text: 'ok' }]
    mockHaystackIaApi.suggestResponseByTicket.mockResolvedValue({ message: 'ok', subjectNotFound: true })
    jest.spyOn(resource, 'getMessagesByTicket').mockResolvedValue(messages)
    const result = await resource.suggestResponseByTicket('ticket1', 'acc1', 'user1', true)
    expect(mockHaystackIaApi.suggestResponseByTicket).toHaveBeenCalledWith(messages, true)
    expect(result).toEqual({ message: 'ok', subjectNotFound: true })
  })
})
