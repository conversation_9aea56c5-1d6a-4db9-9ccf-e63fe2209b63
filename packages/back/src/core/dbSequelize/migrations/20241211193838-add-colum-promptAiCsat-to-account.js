'use strict'

module.exports = {
  async up(queryInterface, Sequelize) {
    const { DataTypes } = Sequelize

    const tableInfo = await queryInterface.describeTable('accounts')
    if (!tableInfo.promptAiCsat) {
      await queryInterface.addColumn('accounts', 'promptAiCsat', {
        type: DataTypes.TEXT,
        allowNull: true,
      })
    }
  },

  async down(queryInterface) {
    const tableInfo = await queryInterface.describeTable('accounts')
    if (tableInfo.promptAiCsat) {
      await queryInterface.removeColumn('accounts', 'promptAiCsat')
    }
  },
}
