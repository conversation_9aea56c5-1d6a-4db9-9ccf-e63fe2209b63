module.exports = {
  async up(queryInterface, Sequelize) {
    const { DataTypes } = Sequelize

    await queryInterface.addColumn('schedules', 'hsmId', {
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'whatsapp_business_templates',
        key: 'id',
        name: 'schedules_whatsapp_business_templateId_fkey',
      },
    })

    await queryInterface.addColumn('schedules', 'hsmFileId', {
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'files',
        key: 'id',
        name: 'schedules_fileId_fkey',
      },
    })

    await queryInterface.addColumn('schedules', 'extraOptions', {
      type: DataTypes.JSONB,
      allowNull: true,
    })

    await queryInterface.addColumn('schedules', 'reason', {
      type: DataTypes.STRING,
      allowNull: true,
    })
  },

  async down(queryInterface) {
    await queryInterface.removeColumn('schedules', 'hsmId')
    await queryInterface.removeColumn('schedules', 'hsmFileId')
    await queryInterface.removeColumn('schedules', 'extraOptions')
    await queryInterface.removeColumn('schedules', 'reason')
  },
}
