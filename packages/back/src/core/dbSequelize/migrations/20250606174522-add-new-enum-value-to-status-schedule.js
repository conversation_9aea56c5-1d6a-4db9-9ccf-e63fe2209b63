module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.sequelize.query(`ALTER TYPE "enum_schedules_status" ADD VALUE if not exists 'error'`)
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.sequelize.query(`
    DELETE 
    FROM
        pg_enum
    WHERE
        enumlabel in('error') AND
        enumtypid = (
            SELECT
                oid
            FROM
                pg_type
            WHERE
                typname = 'enum_shedules_status'
        )
    `)
  },
}
