'use strict'

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    const stageStatus = await queryInterface.describeTable({
      tableName: 'stage_status',
      schema: 'pipeline',
    })

    if (!stageStatus.deletedAt) {
      await queryInterface.addColumn(
        {
          tableName: 'stage_status',
          schema: 'pipeline',
        },
        'deletedAt',
        {
          type: Sequelize.DATE,
          allowNull: true,
        },
      )
    }
  },

  async down(queryInterface) {
    const stageStatus = await queryInterface.describeTable({
      tableName: 'stage_status',
      schema: 'pipeline',
    })

    if (stageStatus.deletedAt) {
      await queryInterface.removeColumn(
        {
          tableName: 'stage_status',
          schema: 'pipeline',
        },
        'deletedAt',
      )
    }
  },
}
