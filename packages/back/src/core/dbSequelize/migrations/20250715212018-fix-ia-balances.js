'use strict'

import moment from 'moment'
import accountRepository from '../../dbSequelize/repositories/accountRepository'
import creditMovementResource from '../../resources/creditMovementResource'
import accountResource from '../../resources/accountResource'
import queuedAsyncMap from '../../utils/array/queuedAsyncMap'

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface) {
    const getAccounts = async () => {
      const accounts = await accountResource.findMany({
        attributes: ['id', 'plan', 'settings'],
        where: {
          isActive: true,
        },
      })

      return accounts.filter(
        (account) =>
          (account?.settings?.flags || {})['enable-smart-summary'] ||
          (account?.settings?.flags || {})['enable-audio-transcription'] ||
          (account?.settings?.flags || {})['enable-magic-text'] ||
          (account?.settings?.flags || {})['enable-copilot'] ||
          (account?.settings?.flags || {})['enable-smart-csat-score'] ||
          (account?.settings?.flags || {})['enable-bots-v3-ai-node'],
      )
    }

    const accounts = await getAccounts()

    if (!accounts || accounts.length === 0) return

    const fixResetDate = async (account) => {
      const wrongResetDates = await creditMovementResource.findMany({
        where: {
          createdAt: {
            $between: [
              `${moment(account?.plan?.renewDate).format('YYYY-MM-DD')} 00:00:00`,
              `${moment(account?.plan?.renewDate).format('YYYY-MM-DD')} 23:59:59`,
            ],
          },
          accountId: account?.id,
          origin: 'reset',
        },
      })

      if (wrongResetDates?.length > 0) {
        await queuedAsyncMap(wrongResetDates, async (wrongDate) => {
          const fixedDate = moment(wrongDate?.createdAt).subtract(1, 'day').format('YYYY-MM-DD')
          await creditMovementResource.update(wrongDate, { createdAt: fixedDate })
        })
      }
    }

    await queuedAsyncMap(accounts, async (account) => {
      if (!account?.plan?.renewDate) return

      await fixResetDate(account)

      const balances = await creditMovementResource.balances(account?.id)
      await queuedAsyncMap(Object.keys(balances), async (serviceType) => {
        if (serviceType !== 'sms-wavy') {
          const monthlyBalance = await creditMovementResource.balance(
            account?.id,
            serviceType,
            account?.plan?.renewDate,
          )

          const diff = Math.abs(monthlyBalance - balances?.[serviceType])
          if (diff !== 0) {
            const type = monthlyBalance > balances?.[serviceType] ? 'in' : 'out'
            await creditMovementResource.create({
              accountId: account?.id,
              type,
              origin: 'reset',
              amount: diff,
              createdAt: moment(account?.plan?.renewDate).subtract(1, 'day'),
              serviceType,
            })
          }
        }
      })
    })
  },

  async down(queryInterface) {},
}
