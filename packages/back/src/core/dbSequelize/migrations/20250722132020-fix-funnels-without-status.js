'use strict'

import pick from 'lodash/pick'
import setupSequelize from '../../services/db/setupSequelize'
import pipelineResource from '../../resources/pipelineResource'
import cardMovementResource from '../../resources/cardMovementResource'
import cardResource from '../../resources/cardResource'
import queuedAsyncMap from '../../utils/array/queuedAsyncMap'
import iteratePaginated from '../../utils/iteratePaginated'

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface) {
    await setupSequelize()

    const pipelines = await pipelineResource.findMany({
      include: [
        {
          model: 'stages',
          where: { position: 10 },
          include: ['statuses'],
        },
      ],
    })

    await queuedAsyncMap(pipelines, async (pipeline) => {
      if (pipeline?.stages?.[0]?.statuses?.length === 0) {
        await pipelineResource.updateStage(pipeline?.stages?.[0]?.id, {
          ...pick(pipeline?.stages?.[0], ['id', 'pipelineId', 'name']),
          statuses: [
            { name: 'STATUS_WON', position: 1 },
            { name: 'STATUS_LOOSE', position: 2 },
          ],
        })
      }
    })

    await iteratePaginated(
      ({ page }) =>
        cardResource.findManyPaginated({
          attributes: ['id', 'accountId', 'pipelineStageId', 'pipelineId', 'updatedAt', 'success'],
          include: [
            {
              model: 'pipeline_stage',
              where: { position: 10 },
              required: true,
              include: [
                {
                  model: 'statuses',
                  where: { name: { $in: ['STATUS_WON', 'STATUS_LOOSE'] } },
                },
              ],
            },
            {
              model: 'movements',
              order: [['createdAt', 'DESC']],
              limit: 1,
            },
          ],
          page: 1,
          perPage: 50,
        }),
      async (card) => {
        const stageWonId = card?.pipeline_stage?.statuses?.find((status) => status?.name === 'STATUS_WON')?.id
        const stageLostId = card?.pipeline_stage?.statuses?.find((status) => status?.name === 'STATUS_LOOSE')?.id

        await cardMovementResource.create({
          cardId: card?.id,
          accountId: card?.accountId,
          fromPipelineStageId: card?.pipelineStageId,
          toPipelineStageId: card?.pipelineStageId,
          pipelineId: card?.pipelineId,
          userId: card?.movements?.[0]?.userId,
          createdAt: card?.updatedAt,
          fromStageStatusId: card?.movements?.[0]?.fromStageStatusId,
          toStageStatusId: card?.success ? stageWonId : stageLostId,
          fromPipelineId: card?.pipelineId,
          toPipelineId: card?.pipelineId,
        })

        await cardResource.update(card, { statusId: card?.success ? stageWonId : stageLostId })
      },
    )
  },

  async down(queryInterface) {},
}
