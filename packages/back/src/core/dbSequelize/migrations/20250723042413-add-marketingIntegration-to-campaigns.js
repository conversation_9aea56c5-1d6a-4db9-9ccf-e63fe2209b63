'use strict'

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    const campaigns = await queryInterface.describeTable({
      tableName: 'campaigns',
      schema: 'public',
    })

    if (!campaigns.marketingIntegration) {
      await queryInterface.addColumn(
        {
          tableName: 'campaigns',
          schema: 'public',
        },
        'marketingIntegration',
        {
          type: Sequelize.BOOLEAN,
          allowNull: true,
        },
      )
    }
  },

  async down(queryInterface) {
    const campaigns = await queryInterface.describeTable({
      tableName: 'campaigns',
      schema: 'public',
    })

    if (campaigns.marketingIntegration) {
      await queryInterface.removeColumn(
        {
          tableName: 'campaigns',
          schema: 'public',
        },
        'marketingIntegration',
      )
    }
  },
}
