'use strict'

module.exports = {
  async up(queryInterface, Sequelize) {
    const tableInfo = await queryInterface.describeTable('plan_ai_history')

    if (!tableInfo?.agent) {
      await queryInterface.addColumn('plan_ai_history', 'agent', {
        type: Sequelize.DataTypes.BOOLEAN,
        defaultValue: false,
        allowNull: false,
      })
    }
  },

  async down(queryInterface) {
    const tableInfo = await queryInterface.describeTable('plan_ai_history')

    if (tableInfo?.agent) {
      await queryInterface.removeColumn('plan_ai_history', 'agent')
    }
  },
}
