import setupSequelize from '../../services/db/setupSequelize'
import roleResource from '../../resources/roleResource'
import permissionResource from '../../resources/permissionResource'

module.exports = {
  up: async () => {
    await setupSequelize()

    const permissionMapping = [
      { old: 'pipelines.view', new: 'pipelines.opportunity.view' },
      { old: 'pipelines.create', new: 'pipelines.opportunity.create' },
      { old: 'pipelines.update', new: 'pipelines.opportunity.update' },
      { old: 'pipelines.destroy', new: 'pipelines.opportunity.destroy' },
    ]

    console.log('Iniciando adição de permissões de oportunidade aos cargos...')

    for (const mapping of permissionMapping) {
      console.log(`Processando mapeamento: ${mapping.old} -> ${mapping.new}`)

      const oldPermission = await permissionResource.findOne({
        where: { name: mapping.old },
      })

      if (!oldPermission) {
        console.log(`Permissão antiga '${mapping.old}' não encontrada. Pulando...`)
        continue
      }

      const newPermission = await permissionResource.findOne({
        where: { name: mapping.new },
      })

      if (!newPermission) {
        console.log(`Permissão nova '${mapping.new}' não encontrada. Pulando...`)
        continue
      }

      const rolesWithOldPermission = await roleResource.findMany({
        include: [
          {
            model: 'permissions',
            where: { name: mapping.old },
            required: true,
          },
        ],
      })

      console.log(`Encontrados ${rolesWithOldPermission.length} cargos com a permissão '${mapping.old}'`)

      for (const role of rolesWithOldPermission) {
        const rolePermissions = await role.getPermissions()
        const hasNewPermission = rolePermissions.some((p) => p.name === mapping.new)

        if (!hasNewPermission) {
          await role.addPermission(newPermission)
          console.log(`Adicionada permissão '${mapping.new}' ao cargo: ${role.displayName}`)
        }
      }
    }

    console.log('Concluída a adição de permissões de oportunidade aos cargos.')
  },

  down: async () => {
    await setupSequelize()

    const permissionMapping = [
      { old: 'pipelines.view', new: 'pipelines.opportunity.view' },
      { old: 'pipelines.create', new: 'pipelines.opportunity.create' },
      { old: 'pipelines.update', new: 'pipelines.opportunity.update' },
      { old: 'pipelines.destroy', new: 'pipelines.opportunity.destroy' },
    ]

    console.log('Iniciando remoção de permissões de oportunidade dos cargos...')

    for (const mapping of permissionMapping) {
      console.log(`Processando remoção: ${mapping.new}`)

      const newPermission = await permissionResource.findOne({
        where: { name: mapping.new },
      })

      if (!newPermission) {
        console.log(`Permissão '${mapping.new}' não encontrada. Pulando...`)
        continue
      }

      const rolesWithNewPermission = await roleResource.findMany({
        include: [
          {
            model: 'permissions',
            where: { name: mapping.new },
            required: true,
          },
        ],
      })

      console.log(`Encontrados ${rolesWithNewPermission.length} cargos com a permissão '${mapping.new}'`)

      for (const role of rolesWithNewPermission) {
        await role.removePermission(newPermission)
        console.log(`Removida permissão '${mapping.new}' do cargo: ${role.displayName}`)
      }
    }

    console.log('Concluída a remoção de permissões de oportunidade dos cargos.')
  },
}
