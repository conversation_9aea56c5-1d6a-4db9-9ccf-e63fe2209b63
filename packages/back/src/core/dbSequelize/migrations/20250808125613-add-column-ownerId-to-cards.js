'use strict'

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    const tableInfo = await queryInterface.describeTable({
      tableName: 'cards',
      schema: 'pipeline',
    })

    if (!tableInfo?.ownerId) {
      await queryInterface.addColumn(
        {
          tableName: 'cards',
          schema: 'pipeline',
        },
        'ownerId',
        {
          type: Sequelize.UUID,
          allowNull: true,
          references: {
            model: {
              tableName: 'users',
              schema: 'public',
            },
            key: 'id',
          },
        },
      )
    }
  },

  async down(queryInterface, Sequelize) {
    const tableInfo = await queryInterface.describeTable({
      tableName: 'cards',
      schema: 'pipeline',
    })

    if (tableInfo?.ownerId) {
      await queryInterface.removeColumn(
        {
          tableName: 'cards',
          schema: 'pipeline',
        },
        'ownerId',
      )
    }
  },
}
