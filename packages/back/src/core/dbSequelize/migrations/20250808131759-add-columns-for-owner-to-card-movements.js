'use strict'

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    const tableInfo = await queryInterface.describeTable({
      tableName: 'card_movements',
      schema: 'pipeline',
    })

    if (!tableInfo?.fromOwnerId) {
      await queryInterface.addColumn({ tableName: 'card_movements', schema: 'pipeline' }, 'fromOwnerId', {
        type: Sequelize.UUID,
        allowNull: true,
        references: {
          model: {
            tableName: 'users',
            schema: 'public',
          },
          key: 'id',
        },
      })
    }

    if (!tableInfo?.toOwnerId) {
      await queryInterface.addColumn({ tableName: 'card_movements', schema: 'pipeline' }, 'toOwnerId', {
        type: Sequelize.UUID,
        allowNull: true,
        references: {
          model: {
            tableName: 'users',
            schema: 'public',
          },
          key: 'id',
        },
      })
    }
  },

  async down(queryInterface, Sequelize) {
    const tableInfo = await queryInterface.describeTable({
      tableName: 'card_movements',
      schema: 'pipeline',
    })

    if (tableInfo?.fromOwnerId) {
      await queryInterface.removeColumn({ tableName: 'card_movements', schema: 'pipeline' }, 'fromOwnerId')
    }
    if (tableInfo?.toOwnerId) {
      await queryInterface.removeColumn({ tableName: 'card_movements', schema: 'pipeline' }, 'toOwnerId')
    }
  },
}
