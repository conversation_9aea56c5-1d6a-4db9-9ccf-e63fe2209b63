'use strict'

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface) {
    const [cards] = await queryInterface.sequelize.query(`
      SELECT id FROM pipeline.cards WHERE "ownerId" IS NULL
    `)

    for (const card of cards) {
      const [[movement]] = await queryInterface.sequelize.query(`
        SELECT "userId" FROM pipeline.card_movements
        WHERE "cardId" = '${card.id}'
        ORDER BY "createdAt" ASC
        LIMIT 1
      `)

      if (movement && movement.userId) {
        await queryInterface.sequelize.query(`
          UPDATE pipeline.cards
          SET "ownerId" = '${movement.userId}'
          WHERE id = '${card.id}'
        `)
      }
    }
  },

  async down() {
    // No revert for this data migration
  },
}
