import setupSequelize from '../../services/db/setupSequelize'
import roleResource from '../../resources/roleResource'
import permissionResource from '../../resources/permissionResource'

module.exports = {
  up: async () => {
    await setupSequelize()

    const rolesWithOldPermission = await roleResource.findMany({
      include: [
        {
          model: 'permissions',
          where: { name: 'pipelines.view' },
          required: true,
        },
      ],
    })

    const newPermission = await permissionResource.findOne({
      where: { name: 'automation.view' },
    })

    for (const role of rolesWithOldPermission) {
      const rolePermissions = await role.getPermissions()
      const hasNewPermission = rolePermissions.some((p) => p?.id === newPermission?.id)

      if (!hasNewPermission) {
        await role.addPermission(newPermission)
      }
    }
  },

  down: async () => {},
}
