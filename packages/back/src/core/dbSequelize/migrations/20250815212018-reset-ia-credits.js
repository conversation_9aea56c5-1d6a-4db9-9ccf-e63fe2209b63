'use strict'

import moment from 'moment'
import accountRepository from '../../dbSequelize/repositories/accountRepository'
import creditMovementResource, { SERVICE_TYPES } from '../../resources/creditMovementResource'
import accountResource from '../../resources/accountResource'
import queuedAsyncMap from '../../utils/array/queuedAsyncMap'

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface) {
    const getAccounts = async () => {
      const accounts = await accountResource.findMany({
        attributes: ['id', 'plan', 'settings'],
        where: {
          isActive: true,
        },
      })

      return accounts.filter(
        (account) =>
          (account?.settings?.flags || {})['enable-smart-summary'] ||
          (account?.settings?.flags || {})['enable-audio-transcription'] ||
          (account?.settings?.flags || {})['enable-magic-text'] ||
          (account?.settings?.flags || {})['enable-copilot'] ||
          (account?.settings?.flags || {})['enable-smart-csat-score'] ||
          (account?.settings?.flags || {})['enable-bots-v3-ai-node'],
      )
    }

    const accounts = await getAccounts()

    if (!accounts || accounts.length === 0) return

    await queuedAsyncMap(accounts, async (account) => {
      const servicesTypes = SERVICE_TYPES?.filter((serviceType) => serviceType !== 'sms-wavy')
      await queuedAsyncMap(servicesTypes, async (serviceType) => {
        if (account?.plan?.ai?.[serviceType] > 0) {
          const monthlyBalance = await creditMovementResource.balance(
            account?.id,
            serviceType,
            account?.plan?.renewDate,
            true,
          )
          if (monthlyBalance <= 0) {
            await creditMovementResource.create({
              accountId: account?.id,
              type: 'in',
              origin: 'renewal',
              amount: Math.abs(account?.plan?.ai?.[serviceType] - monthlyBalance),
              createdAt: moment(),
              serviceType,
            })
          }
        }

        const balance = await creditMovementResource.balance(account?.id, serviceType)
        if (account?.plan?.ai?.[serviceType] && balance !== account?.plan?.ai?.[serviceType]) {
          const type = balance < account?.plan?.ai?.[serviceType] ? 'in' : 'out'
          await creditMovementResource.create({
            accountId: account?.id,
            type,
            origin: 'reset',
            amount: Math.abs(account?.plan?.ai?.[serviceType] - balance),
            createdAt: moment(account?.plan?.renewDate).subtract(1, 'day'),
            serviceType,
          })
        }
      })
    })
  },

  async down(queryInterface) {},
}
