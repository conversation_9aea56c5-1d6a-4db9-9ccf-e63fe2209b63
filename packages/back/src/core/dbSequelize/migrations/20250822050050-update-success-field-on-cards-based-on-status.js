'use strict'

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface) {
    const [statuses] = await queryInterface.sequelize.query(`
      SELECT id, name 
      FROM pipeline.stage_status 
      WHERE name IN ('STATUS_LOOSE', 'STATUS_WON')
    `)

    const statusLostIds = statuses.filter((s) => s.name === 'STATUS_LOOSE').map((s) => `'${s.id}'`)
    const statusWonIds = statuses.filter((s) => s.name === 'STATUS_WON').map((s) => `'${s.id}'`)

    if (statusLostIds.length) {
      await queryInterface.sequelize.query(`
        UPDATE pipeline.cards
        SET success = false
        WHERE "statusId" IN (${statusLostIds.join(',')})
      `)
    }

    if (statusWonIds.length) {
      await queryInterface.sequelize.query(`
        UPDATE pipeline.cards
        SET success = true
        WHERE "statusId" IN (${statusWonIds.join(',')})
      `)
    }
  },

  async down() {
    // No revert for this data migration
  },
}
