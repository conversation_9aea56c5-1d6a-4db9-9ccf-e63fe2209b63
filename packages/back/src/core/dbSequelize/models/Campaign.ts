import Sequelize from 'sequelize'
import sequelize from '../../services/db/sequelize'
import { AccountInstance } from './Account'
import { ServiceInstance } from './Service'
import { TagInstance } from './Tag'
import { CampaignMessageInstance } from './CampaignMessage'
import { MessageInstance } from './Message'
import { DepartmentInstance } from './Department'
import { UserInstance } from './User'
import { Options } from '../../resources/BaseResource'
import { FileInstance } from './File'

// @ts-ignore
const { DataTypes } = Sequelize

export type CampaignInstance = {
  id: string
  title: string
  status:
    | 'importing_contacts' // importando contatos e criando message_progress
    | 'ready' // pronto para enviar
    | 'queued' // na fila do disparo, usado no agendamento
    | 'processing' // enviando
    | 'paused' // pausado
    | 'waiting_connection' // sem conexão
    | 'canceled' // cancelado
    | 'insufficient_credits' // sem credito
    | 'hsm_limit_exceeded' //sem hsm
    | 'import_error' //erro na importação
    | 'error' // erro no disparo
    | 'done' // campanha disparada, concluída
  config: {
    minInterval: number
    maxInterval: number
    nameField?: string
    numberField?: string
    delimiter?: ',' | ';' | ':' | string
    error?: {
      message: string
    }
  }
  isScheduled: boolean
  sendsAt: Date
  startedAt: Date
  finishedAt: Date
  totalMessagesCount: number
  sentMessagesCount: number
  totalContacts: number
  totalContactsImported: number
  totalValidContacts: number
  accountId: string
  serviceId: string
  mustOpenTicket: boolean
  defaultDepartmentId: string
  defaultUserId: string
  createdById?: string
  sentById?: string
  account?: AccountInstance
  service?: ServiceInstance
  defaultDepartment?: DepartmentInstance
  defaultUser?: UserInstance
  tags?: TagInstance[]
  messages?: CampaignMessageInstance[]
  createdAt: Date
  updatedAt: Date
  createdBy?: UserInstance
  sentBy?: UserInstance
  importFile: FileInstance
  marketingIntegration?: boolean
  getMessages(options?: Options<MessageInstance>): Promise<MessageInstance[]>
  getTags(options?: Options<TagInstance>): Promise<TagInstance[]>
  setTags(data?: TagInstance[]): Promise<any>
}

const Campaign = sequelize.define(
  'Campaign',
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    title: {
      type: DataTypes.TEXT,
      allowNull: false,
    },
    status: {
      type: DataTypes.ENUM,
      values: [
        'ready',
        'queued',
        'processing',
        'paused',
        'waiting_connection',
        'canceled',
        'done',
        'insufficient_credits',
        'hsm_limit_exceeded',
        'import_error',
        'error',
        'importing_contacts',
      ],
      defaultValue: 'ready',
      allowNull: true,
    },
    config: {
      type: DataTypes.JSONB,
      allowNull: false,
      defaultValue: {},
    },
    sendsAt: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    isScheduled: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
    },
    startedAt: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    finishedAt: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    totalMessagesCount: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    sentMessagesCount: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    totalContacts: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    totalContactsImported: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    totalValidContacts: {
      type: DataTypes.INTEGER,
      allowNull: true,
    },
    mustOpenTicket: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    marketingIntegration: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
  },
  {
    tableName: 'campaigns',
    paranoid: true,
  },
)

Campaign.associate = (models) => {
  Campaign.belongsTo(models.Account, {
    as: 'account',
    foreignKey: 'accountId',
  })

  Campaign.belongsTo(models.User, {
    as: 'user',
    foreignKey: 'userId',
  })

  Campaign.belongsToMany(models.Tag, {
    as: 'tags',
    through: 'campaign_contact_lists',
    foreignKey: 'campaignId',
    otherKey: 'tagId',
  })

  Campaign.belongsTo(models.User, {
    as: 'defaultUser',
    foreignKey: 'defaultUserId',
  })

  Campaign.belongsTo(models.User, {
    as: 'createdBy',
    foreignKey: 'createdById',
  })

  Campaign.belongsTo(models.User, {
    as: 'sentBy',
    foreignKey: 'sentById',
  })

  Campaign.belongsTo(models.Department, {
    as: 'defaultDepartment',
    foreignKey: 'defaultDepartmentId',
  })

  Campaign.hasMany(models.CampaignMessage, {
    as: 'messages',
    foreignKey: 'campaignId',
  })

  Campaign.hasMany(models.CampaignMessageProgress, {
    foreignKey: 'campaignId',
    as: 'campaignMessagesProgress',
  })

  Campaign.belongsTo(models.Service, {
    as: 'service',
    foreignKey: 'serviceId',
  })

  Campaign.hasOne(models.File, {
    as: 'importFile',
    foreignKey: 'attachedId',
    scope: { attachedType: 'campaign.file' },
    constraints: false,
  })
}

export default Campaign
