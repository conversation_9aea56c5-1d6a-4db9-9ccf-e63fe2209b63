import Sequelize from 'sequelize'
import sequelize from '../../services/db/sequelize'
import { AccountInstance } from './Account'
import { UserInstance } from './User'
import { ContactInstance } from './Contact'
import { PipelineInstance } from './Pipeline'
import { PipelineStageInstance } from './PipelineStage'
import { PipelineStageStatusInstance } from './PipelineStageStatus'
import { PipelineStageReasonInstance } from './PipelineStageReason'
import { CardProductInstance } from './CardProduct'
import { CardMovementInstance } from './CardMovement'
import { CardCommentInstance } from './CardComment'

const { DataTypes } = Sequelize

export type CardInstance = {
  id: string
  title: string
  description: string
  isArchived: boolean
  archivedAt: Date
  order: number
  accountId: string
  account: AccountInstance
  contactId: string
  contact: ContactInstance
  pipelineId: string
  pipeline: PipelineInstance
  pipelineStageId: string
  pipelineStage: PipelineStageInstance
  createdAt: Date
  updatedAt: Date
  organization?: string
  organizationSegment?: string
  statusId?: string
  stage_status?: PipelineStageStatusInstance
  stage_reason?: PipelineStageReasonInstance
  reasonId?: string
  reason?: PipelineStageReasonInstance
  originChannel?: string
  originCampaign?: string
  success?: boolean
  finishedAt: Date
  products?: CardProductInstance[]
  movements?: CardMovementInstance[]
  comments?: CardCommentInstance[]
  totalValue?: number
  ownerId?: string
  owner?: UserInstance
}

const Card = sequelize.define(
  'Card',
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    title: {
      type: DataTypes.TEXT,
    },
    description: {
      type: DataTypes.TEXT,
    },
    isArchived: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    archivedAt: {
      type: DataTypes.DATE,
    },
    accountId: {
      type: DataTypes.UUIDV4,
    },
    contactId: {
      type: DataTypes.UUIDV4,
    },
    pipelineId: {
      type: DataTypes.UUIDV4,
    },
    pipelineStageId: {
      type: DataTypes.UUIDV4,
    },
    organization: {
      type: DataTypes.STRING,
    },
    organizationSegment: {
      type: DataTypes.STRING,
    },
    statusId: {
      type: DataTypes.UUIDV4,
    },
    reasonId: {
      type: DataTypes.UUIDV4,
    },
    originChannel: {
      type: DataTypes.STRING,
    },
    originCampaign: {
      type: DataTypes.STRING,
    },
    order: {
      type: DataTypes.NUMBER,
      allowNull: false,
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    success: {
      type: DataTypes.BOOLEAN,
    },
    finishedAt: {
      type: DataTypes.DATE,
    },
    ownerId: {
      type: DataTypes.UUIDV4,
    },
  },
  {
    tableName: 'cards',
    indexes: [
      {
        fields: ['contactId', 'pipelineId'],
      },
    ],
    schema: 'pipeline',
  },
)

Card.associate = (models) => {
  Card.belongsTo(models.Account, {
    as: 'account',
    foreignKey: 'accountId',
  })
  Card.belongsTo(models.Contact, {
    as: 'contact',
    foreignKey: 'contactId',
  })
  Card.belongsTo(models.Pipeline, {
    as: 'pipeline',
    foreignKey: 'pipelineId',
  })
  Card.belongsTo(models.PipelineStage, {
    as: 'pipeline_stage',
    foreignKey: 'pipelineStageId',
  })
  Card.belongsTo(models.PipelineStageStatus, {
    as: 'stage_status',
    foreignKey: 'statusId',
  })
  Card.belongsTo(models.PipelineStageReason, {
    as: 'stage_reason',
    foreignKey: 'reasonId',
  })
  Card.hasMany(models.CardMovement, {
    as: 'movements',
    foreignKey: 'cardId',
  })
  Card.hasMany(models.CardProduct, {
    as: 'products',
    foreignKey: 'cardId',
  })
  Card.hasMany(models.CardComment, {
    as: 'comments',
    foreignKey: 'cardId',
  })
  Card.belongsTo(models.User, {
    as: 'owner',
    foreignKey: 'ownerId',
  })
}

export default Card
