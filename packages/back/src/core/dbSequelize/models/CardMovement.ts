import Sequelize from 'sequelize'
import sequelize from '../../services/db/sequelize'
import { AccountInstance } from './Account'
import { CardInstance } from './Card'
import { UserInstance } from './User'
import { PipelineStageInstance } from './PipelineStage'
import { PipelineInstance } from './Pipeline'

const { DataTypes } = Sequelize

export type CardMovementInstance = {
  id: string
  accountId: string
  account: AccountInstance
  userId: string
  user: UserInstance
  cardId: string
  card: CardInstance
  fromPipelineId: string
  fromPipeline: PipelineInstance
  toPipelineId: string
  toPipeline: PipelineInstance
  fromPipelineStageId: string
  fromPipelineStage: PipelineStageInstance
  toPipelineStageId: string
  toPipelineStage: PipelineStageInstance
  pipelineId: PipelineInstance
  createdAt: Date
  fromStageStatusId?: string
  toStageStatusId?: string
  fromOwnerId?: string
  toOwnerId?: string
  fromOwner?: UserInstance
  toOwner?: UserInstance
}

const CardMovement = sequelize.define(
  'CardMovement',
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    cardId: {
      type: DataTypes.UUIDV4,
      allowNull: false,
    },
    pipelineId: {
      type: DataTypes.UUIDV4,
      allowNull: false,
    },
    fromPipelineStageId: {
      type: DataTypes.UUIDV4,
    },
    toPipelineStageId: {
      type: DataTypes.UUIDV4,
      allowNull: false,
    },
    userId: {
      type: DataTypes.UUIDV4,
      allowNull: false,
    },
    accountId: {
      type: DataTypes.UUIDV4,
      allowNull: false,
    },
    fromStageStatusId: {
      type: DataTypes.UUIDV4,
    },
    toStageStatusId: {
      type: DataTypes.UUIDV4,
    },
    fromOwnerId: {
      type: DataTypes.UUIDV4,
    },
    toOwnerId: {
      type: DataTypes.UUIDV4,
    },
  },
  {
    tableName: 'card_movements',
    updatedAt: false,
    schema: 'pipeline',
  },
)

CardMovement.associate = (models) => {
  CardMovement.belongsTo(models.Account, {
    as: 'account',
    foreignKey: 'accountId',
  })
  CardMovement.belongsTo(models.Card, {
    as: 'card',
    foreignKey: 'cardId',
  })
  CardMovement.belongsTo(models.Pipeline, {
    as: 'pipeline',
    foreignKey: 'pipelineId',
  })
  CardMovement.belongsTo(models.Pipeline, {
    as: 'from_pipeline',
    foreignKey: 'fromPipelineId',
  })
  CardMovement.belongsTo(models.Pipeline, {
    as: 'to_pipeline',
    foreignKey: 'toPipelineId',
  })
  CardMovement.belongsTo(models.PipelineStage, {
    as: 'from_pipeline_stage',
    foreignKey: 'fromPipelineStageId',
  })
  CardMovement.belongsTo(models.PipelineStage, {
    as: 'to_pipeline_stage',
    foreignKey: 'toPipelineStageId',
  })
  CardMovement.belongsTo(models.PipelineStageStatus, {
    as: 'from_stage_status',
    foreignKey: 'fromStageStatusId',
  })
  CardMovement.belongsTo(models.PipelineStageStatus, {
    as: 'to_stage_status',
    foreignKey: 'toStageStatusId',
  })
  CardMovement.belongsTo(models.User, {
    as: 'user',
    foreignKey: 'userId',
  })
  CardMovement.belongsTo(models.User, {
    as: 'from_owner',
    foreignKey: 'fromOwnerId',
  })
  CardMovement.belongsTo(models.User, {
    as: 'to_owner',
    foreignKey: 'toOwnerId',
  })
}

export default CardMovement
