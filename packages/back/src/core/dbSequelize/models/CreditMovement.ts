import Sequelize from 'sequelize'
import sequelize from '../../services/db/sequelize'
import { AccountInstance } from './Account'
import { ServiceInstance } from './Service'
import { CampaignInstance } from './Campaign'

export type ServiceType = 'sms-wavy' | 'transcription' | 'summary' | 'magic-text' | 'copilot' | 'csat' | 'agent'

export type SMSCreditOrigin =
  | 'trial'
  | 'single'
  | 'bulk'
  | 'chargeback'
  | 'renewal'
  | 'reset'
  | 'agnus'
  | 'marketplace'
  | 'copilot'
  | 'additional'

export type CreditOrigin = SMSCreditOrigin

export type CreditMovementInstance = {
  id: string
  type: 'in' | 'out'
  amount?: number
  serviceType?: ServiceType
  origin?: CreditOrigin
  accountId: string
  account: AccountInstance
  serviceId: string
  service: ServiceInstance
  campaignId: string
  campaign: CampaignInstance
  createdAt: Date
  updatedAt: Date
}

// @ts-ignore
const { DataTypes } = Sequelize

const CreditMovement = sequelize.define(
  'CreditMovement',
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    type: {
      type: DataTypes.ENUM,
      values: ['in', 'out'],
      allowNull: false,
    },
    origin: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    amount: {
      type: DataTypes.INTEGER,
      allowNull: true,
      defaultValue: 1,
    },
    createdAt: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    updatedAt: {
      type: DataTypes.DATE,
      allowNull: false,
    },
    serviceType: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    serviceId: {
      type: DataTypes.STRING,
    },
    accountId: {
      type: DataTypes.STRING,
    },
  },
  {
    tableName: 'credit_movements',
  },
)

CreditMovement.associate = (models) => {
  CreditMovement.belongsTo(models.Account, {
    as: 'account',
    foreignKey: 'accountId',
  })
  CreditMovement.belongsTo(models.Campaign, {
    as: 'campaign',
    foreignKey: 'campaignId',
  })
  CreditMovement.belongsTo(models.Service, {
    as: 'service',
    foreignKey: 'serviceId',
  })
}

export default CreditMovement
