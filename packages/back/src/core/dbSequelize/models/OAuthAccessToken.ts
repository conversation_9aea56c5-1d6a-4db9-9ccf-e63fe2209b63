import Sequelize from 'sequelize'
import sequelize from '../../services/db/sequelize'
import { UserInstance } from './User'

// @ts-ignore
const { DataTypes } = Sequelize

export type OAuthAccessTokenInstance = {
  id: string
  accessToken: string
  name: string
  accessTokenExpiresAt: Date
  scope: string
  clientId: string
  user?: UserInstance
  deletedAt?: Date | null
}

const OAuthAccessToken = sequelize.define(
  'OAuthAccessToken',
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    accessToken: DataTypes.STRING,
    name: DataTypes.STRING,
    accessTokenExpiresAt: DataTypes.DATE,
    scope: DataTypes.STRING,
    deletedAt: DataTypes.DATE,
  },
  { tableName: 'oauth_access_tokens' },
)

OAuthAccessToken.associate = (models) => {
  OAuthAccessToken.belongsTo(models.User, { foreignKey: 'userId', as: 'user' })
  OAuthAccessToken.belongsTo(models.OAuthClient, {
    foreignKey: 'clientId',
    as: 'client',
  })
}

export default OAuthAccessToken
