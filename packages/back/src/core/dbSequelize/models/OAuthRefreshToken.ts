import Sequelize from 'sequelize'
import sequelize from '../../services/db/sequelize'

// @ts-ignore
const { DataTypes } = Sequelize

export type OAuthRefreshTokenInstance = {
  id: string
  refreshToken: string
  refreshTokenExpiresAt: Date
}

const OAuthRefreshToken = sequelize.define(
  'OAuthRefreshToken',
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    refreshToken: DataTypes.STRING,
    refreshTokenExpiresAt: DataTypes.DATE,
  },
  { tableName: 'oauth_refresh_tokens' },
)

OAuthRefreshToken.associate = (models) => {
  OAuthRefreshToken.belongsTo(models.User, {
    foreignKey: 'userId',
    as: 'user',
  })
  OAuthRefreshToken.belongsTo(models.OAuthClient, {
    foreignKey: 'clientId',
    as: 'client',
  })
}

export default OAuthRefreshToken
