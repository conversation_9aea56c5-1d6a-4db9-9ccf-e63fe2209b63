import Sequelize from 'sequelize'
import sequelize from '../../services/db/sequelize'
import { AccountInstance } from './Account'
import { PipelineInstance } from './Pipeline'
import { PipelineStageInstance } from './PipelineStage'

const { DataTypes } = Sequelize

export type PipelineStageStatusInstance = {
  id: string
  name: string
  position: number
  accountId: string
  account: AccountInstance
  pipelineId: string
  pipeline: PipelineInstance
  stageId: string
  stage: PipelineStageInstance
  createdAt: Date
  updatedAt: Date
}

const PipelineStageStatus = sequelize.define(
  'PipelineStageStatus',
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    position: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    pipelineId: {
      type: DataTypes.UUID,
      allowNull: false,
    },
    stageId: {
      type: DataTypes.UUID,
      allowNull: false,
    },
    accountId: {
      type: DataTypes.UUID,
      allowNull: false,
    },
  },
  {
    tableName: 'stage_status',
    schema: 'pipeline',
    paranoid: true,
    timestamps: true,
  },
)

PipelineStageStatus.associate = (models) => {
  PipelineStageStatus.belongsTo(models.Account, {
    as: 'account',
    foreignKey: 'accountId',
  })
  PipelineStageStatus.belongsTo(models.Pipeline, {
    as: 'pipeline',
    foreignKey: 'pipelineId',
  })
  PipelineStageStatus.belongsTo(models.PipelineStage, {
    as: 'stage',
    foreignKey: 'stageId',
  })
}

export default PipelineStageStatus
