import PipelineStage, { PipelineStageInstance } from '../models/PipelineStage'
import { PipelineStageStatusInstance } from '../models/PipelineStageStatus'
import { PipelineStageReasonInstance } from '../models/PipelineStageReason'
import pipelineStageStatusRepository from './pipelineStageStatusRepository'
import pipelineStageReasonRepository from './pipelineStageReasonRepository'
import cardRepository from './cardRepository'
import queuedAsyncMap from '../../utils/array/queuedAsyncMap'
import BaseRepository from './BaseRepository'
import { Options } from './BaseSequelizeRepository'

export class PipelineStageRepository extends BaseRepository<PipelineStageInstance> {
  constructor(defaultOptions?: {}) {
    // @ts-ignore
    super(PipelineStage, defaultOptions)
  }

  create = async (
    data: Partial<PipelineStageInstance>,
    options?: Options<PipelineStageInstance>,
  ): Promise<PipelineStageInstance> => {
    const stage = await super.create(data, options)
    await queuedAsyncMap(data?.statuses ?? [], async (status: PipelineStageStatusInstance) => {
      await pipelineStageStatusRepository.create(
        { ...status, stageId: stage.id, pipelineId: stage.pipelineId, accountId: stage.accountId },
        { transaction: options?.transaction },
      )
    })

    await queuedAsyncMap(data?.reasons ?? [], async (reason: PipelineStageStatusInstance) => {
      await pipelineStageReasonRepository.create(
        { ...reason, stageId: stage.id, pipelineId: stage.pipelineId, accountId: stage.accountId },
        { transaction: options?.transaction },
      )
    })

    return stage
  }

  update = async (
    model: PipelineStageInstance,
    data: Partial<PipelineStageInstance>,
    options?: {},
  ): Promise<PipelineStageInstance> => {
    const stage = await super.update(model, data, this.buildOptions(options))

    await this.saveItems(model, data?.statuses, pipelineStageStatusRepository)

    await this.saveItems(model, data?.reasons, pipelineStageReasonRepository)

    return stage
  }

  async saveItems(
    stage: PipelineStageInstance,
    items: PipelineStageStatusInstance[] | PipelineStageReasonInstance[],
    repository: any,
  ) {
    const ids = (items || []).map((item) => item.id).filter((item) => item)
    if (ids) {
      const itemsToRemove = await repository.findMany({
        where: { id: { $notIn: ids }, stageId: stage?.id },
      })

      await queuedAsyncMap(itemsToRemove, async (item: PipelineStageStatusInstance | PipelineStageReasonInstance) => {
        if (item?.stageId) {
          await cardRepository.bulkUpdate(
            { reasonId: null },
            { where: { pipelineId: item?.pipelineId, reasonId: item?.id } },
          )
        }
        await repository.destroy(item)
      })
    }

    await queuedAsyncMap(items || [], async (item: PipelineStageStatusInstance | PipelineStageReasonInstance) => {
      if (item?.id) {
        await repository.updateById(item.id, item)
      } else {
        await repository.create({
          ...item,
          stageId: stage?.id,
          pipelineId: stage?.pipelineId,
          accountId: stage?.accountId,
        })
      }
    })
  }
}

export default new PipelineStageRepository()
