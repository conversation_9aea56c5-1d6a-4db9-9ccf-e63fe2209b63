import { Request, Response, NextFunction } from 'express'
import authHistoryResource from '../../core/resources/authHistoryResource'
import { getIpFromRequest, getUserAgentFromRequest } from './ipRestriction'
import config from '../../core/config'
import { errorToHttpError } from '../../core/utils/error/HttpError'
import { Container } from 'typedi'
import Logger from '../../core/services/logs/Logger'
import userResource from '../../core/resources/userResource'

const logger = Container.get(Logger)

export default async function (req: Request, res: Response, next: NextFunction) {
  const { token, authResponse } = res.locals?.oauth || {}

  const { accessTokenId, user } = token

  const accounts = await userResource.findAccountsByCredentials(user.email)

  try {
    const { clearThrottling } = res.locals.trottleLogin || {}
    const originIP = getIpFromRequest(req)

    await authHistoryResource.create({
      userId: user.id,
      accountId: user.accountId,
      event: 'auth',
      accessTokenId,
      passwordHash: user.password,
      branch: config('branch'),
      originIP,
      originUA: getUserAgentFromRequest(req),
    })

    await clearThrottling()
    const blockAccount = accounts.find((account) => account.plan.isGracePeriodOnHold && account.plan?.isOnGracePeriod)

    blockAccount
      ? logger.logEvent('authn_login_fail', { email: user.email }, 'account_blocked', 'error')
      : logger.logEvent('authn_login_success', { userId: user.id })

    res.send(authResponse)
  } catch (error) {
    logger.logEvent('authn_login_fail', { email: user.email }, 'error_auth_history', 'error')
    next(errorToHttpError(error))
  }
  return
}
