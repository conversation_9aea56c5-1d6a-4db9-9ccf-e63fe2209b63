import { NextFunction, Request, Response } from 'express'

import userResource from '../resources/userResource'
import HttpError from '../utils/error/HttpError'

/**
 * Esse middleware verifica se a conta está ativada para realizar o acesso a plataforma
 *
 * @param req Request
 * @param res Response
 * @param next NextFuntion
 */
const checkAccountInactive = async (req: Request, res: Response, next: NextFunction) => {
  const { email = '', accountId } = req.body
  const accounts = await userResource.findAccountsByCredentials(email, undefined, accountId)
  if (!accounts?.length) {
    return next(new HttpError(401))
  }

  const activeAccount = accounts.find((account) => account.isActive)
  if (activeAccount) {
    if (!accountId) {
      req.body.accountId = activeAccount.id
    }
    return next()
  }

  return next(new HttpError(403, 'ACCOUNT_INACTIVE'))
}

export default checkAccountInactive
