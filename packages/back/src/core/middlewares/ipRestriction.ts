import { Request, Response, NextFunction } from 'express'
import HttpError from '../utils/error/HttpError'
import { UserInstance } from '../dbSequelize/models/User'

export const getIpFromRequest = (req: Request) => String(req.headers['x-real-ip'] || req.connection.remoteAddress)
export const getUserAgentFromRequest = (req: Request) => req.headers['user-agent']

export default () => (req: Request, res: Response, next: NextFunction) => {
  const user: UserInstance = res.locals.user
  const account = user.account

  if (!account.settings.ipRestriction) {
    next()
    return
  }

  if (user.isSuperAdmin) {
    next()
    return
  }

  const ip = getIpFromRequest(req)
  const allowedIps = account.settings.allowedIps

  if (allowedIps.some((allowedIp) => [ip, req.headers['x-real-ip-alternative']].includes(allowedIp))) {
    next()
    return
  }

  // Por ultimo pra não gastar ciclos de CPU desnecessários,
  // visto que será a regra menos usada
  if (user.hasPermission('ignore-ip-restriction')) {
    next()
    return
  }

  next(new HttpError(403, `IP [${ip}] not allowed.`))
}
