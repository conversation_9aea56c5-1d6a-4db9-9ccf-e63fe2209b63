import { NextFunction, Request, Response } from 'express'

import userResource from '../resources/userResource'
import HttpError from '../utils/error/HttpError'
import { Container } from 'typedi'
import Logger from '../../core/services/logs/Logger'

const logger = Container.get(Logger)
/**
 * Esse middleware verifica se a conta está ativida para realizar o acesso a plataforma
 *
 * @param req Request
 * @param res Response
 * @param next NextFuntion
 */
const oAuth2AccountBlock = async (req: Request, res: Response, next: NextFunction) => {
  const { email = '', username = '', password = 'no-password' } = req.body
  const [parsedMail, accountId] = username.split(':')

  const { clearThrottling, getThrottleError } = res.locals.trottleLogin

  const accounts = await userResource.findAccountsByCredentials(email || parsedMail, password, accountId)

  if (!accounts?.length) {
    logger.logEvent('authn_login_fail', { email }, 'invalid_user_or_password', 'error')
    next(getThrottleError(401))
    return
  }

  const activeAccount = accounts.find((account) => account.isActive || account.plan?.isOnGracePeriod)

  // Limpa o histórico de tentativas nos casos de conta inativa ou que as credenciais estão válidas.
  if (!activeAccount) {
    await clearThrottling()
  }

  if (activeAccount) {
    if (!accountId) {
      req.body.accountId = activeAccount.id
      req.body.username = `${req.body.username}:${req.body.accountId}`
    }
    return next()
  }

  logger.logEvent('authn_login_fail', { email }, 'account_inactivated', 'error')

  return next(new HttpError(403, 'ACCOUNT_INACTIVE'))
}

export default oAuth2AccountBlock
