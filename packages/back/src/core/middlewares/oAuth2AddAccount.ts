import { NextFunction, Request, Response } from 'express'
import HttpError from '../../core/utils/error/HttpError'
import { Container } from 'typedi'
import Logger from '../../core/services/logs/Logger'

const logger = Container.get(Logger)

const oAuth2AddAccount = (req: Request, res: Response, next: NextFunction) => {
  if (!req.body.username) {
    logger.logEvent('authn_login_fail', 'error_email_required', 'error')

    next(new HttpError(401))
    return
  }

  if (req.body.accountId) {
    req.body.username = `${req.body.username}:${req.body.accountId}`
  }

  next()
}

export default oAuth2AddAccount
