import { Request, Response, NextFunction } from 'express'
import { errorToHttpError } from '../../core/utils/error/HttpError'
import oAuthServer from '../../core/services/auth/oAuthServer'
import { Container } from 'typedi'
import Logger from '../../core/services/logs/Logger'

const logger = Container.get(Logger)

export default async function (req: Request, res: Response, next: NextFunction) {
  const { user } = res.locals?.oauth?.token || {}

  req.headers['content-type'] = 'application/x-www-form-urlencoded'
  const send = res.send
  try {
    let authResponse = null

    res.send = (payload) => {
      authResponse = payload
    }
    await oAuthServer.token({
      accessTokenLifetime: 60 * 60 * 24 * 14,
    })(req, res, () => true)

    res.send = send

    const { getThrottleError } = res.locals.trottleLogin || {}

    const { token = '' } = res.locals.oauth || {}

    if (!token) {
      logger.logEvent('authn_login_fail', { email: user?.email }, 'error_getting_new_access_token', 'error')
      next(getThrottleError(401))
      return
    }

    res.locals.oauth = {
      ...res.locals?.oauth,
      authResponse,
    }
    next()
  } catch (error) {
    res.send = send
    logger.logEvent('authn_login_fail', { email: user?.email }, 'error_new_access_token', 'error')
    next(errorToHttpError(error))
  }
}
