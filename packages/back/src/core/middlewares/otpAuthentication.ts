import otpTokenIsValid from '../utils/otp/tokenIsValid'
import { decryptTextForAccount } from '../services/crypt/accountCryptor'
import { errorToHttpError } from '../utils/error/HttpError'
import { Container } from 'typedi'
import Logger from '../../core/services/logs/Logger'

const logger = Container.get(Logger)

export default async function (req, res, next) {
  const { user } = res.locals?.oauth?.token || {}
  try {
    const { getThrottleError, isOtpStage, setOtpStage } = res.locals.trottleLogin
    const account = await user.getAccount()

    if (!user?.otpAuthActive || !account.settings?.twoFactorAuthActive) {
      next()
      return
    }
    const otpToken = req.headers['x-digisac-otp'] || ''
    if (!otpToken) {
      if (!isOtpStage()) {
        await setOtpStage()
      }
      res.setHeader('x-digisac-otp', 'required')
      logger.logEvent('authn_login_fail', { email: user.email }, 'absence_otp_token', 'error')
      next(getThrottleError(401, `OTP Token is required.`))
      return
    }
    const { otpSecretKey = '' } = user

    const secretKey = await decryptTextForAccount(account, otpSecretKey)
    if (!otpTokenIsValid(otpToken, secretKey)) {
      res.setHeader('x-digisac-otp', 'invalid')
      logger.logEvent('authn_login_fail', { email: user.email }, 'invalid_otp_token', 'error')
      next(getThrottleError(401, `OTP Token is invalid.`))
      return
    }

    next()
  } catch (error) {
    logger.logEvent('authn_login_fail', { email: user.email }, 'error_otp_token', 'error')
    next(errorToHttpError(error))
  }
}
