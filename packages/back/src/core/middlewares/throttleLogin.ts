import { Request, Response, NextFunction } from 'express'
import { Container } from 'typedi'
import Logger from '../../core/services/logs/Logger'
import { addMinutes } from 'date-fns'
import RedisCacheStorage from '../../core/services/cache/RedisCacheStorage'
import { getIpFromRequest } from './ipRestriction'
import TooManyRequestsHttpError from '../../core/utils/error/TooManyRequestsHttpError'
import HttpError, { errorToHttpError } from '../../core/utils/error/HttpError'

export enum ThrottlingStages {
  LOGIN = 'login',
  OTP = 'otp',
}

interface Throttling {
  attempts: Date[]
  stage?: string
}
const logger = Container.get(Logger)

const useLoginAttempts = async (email, originIP) => {
  const redisCache = Container.get(RedisCacheStorage)
  const key = `throttlelogin:${originIP}-${email}`
  const maxAttempts = 5
  const windowTime = 60 //minutes
  const TTL = windowTime * 60 * 1000

  const getThrottling = (): Promise<Throttling> =>
    redisCache.get(key).then((data: Throttling) => data || { attempts: [], stage: ThrottlingStages.LOGIN })
  const setThrottling = async (value: Throttling) =>
    redisCache.set<Throttling>(
      key,
      {
        ...((await getThrottling()) || {}),
        ...value,
      },
      TTL,
    )
  const clearThrottling = () => redisCache.destroy(key)
  const getAttempts = () => getThrottling().then((data) => [...(data?.attempts || [])].slice(0, maxAttempts))
  const addAttempt = async (date = new Date()) => {
    await setThrottling({
      attempts: [...(await getAttempts()), date],
    })
  }
  const removeLastAttempt = async () => {
    const currentAttempts = (await getAttempts()) || []
    currentAttempts.splice(-1, 1)
    await setThrottling({
      attempts: currentAttempts || [],
    })
  }
  const setOtpStage = async () => await setThrottling({ attempts: [], stage: ThrottlingStages.OTP })

  const getStats = async () => {
    const history: Date[] = await getAttempts()
    const currentStage = await getThrottling().then((data) => data.stage || ThrottlingStages.LOGIN)
    const isOtpStage = () => currentStage === ThrottlingStages.OTP
    const firstAttempt = history[0]
    /* Ref: https://www.epochconverter.com/ */
    const resetsAt = Math.floor(addMinutes(new Date(firstAttempt), windowTime).getTime() / 1000.0)

    return {
      history,
      limit: maxAttempts,
      window: windowTime,
      attempts: history.length,
      firstAttempt,
      remaining: maxAttempts - history.length,
      resetsAt,
      isOtpStage,
      getRemainingTime: (date = new Date()) => resetsAt - Math.floor((date || new Date()).getTime() / 1000.0),
      getThrottleError: (statusCode: number, message?: string, ignoreAttempts?: boolean) => {
        if (statusCode === 401) {
          if (maxAttempts > history.length) {
            return new HttpError(statusCode, message, {
              ...((isOtpStage() || !ignoreAttempts) && { remainingAttempts: maxAttempts - history.length }),
            })
          }
          return new TooManyRequestsHttpError()
        }
        return new HttpError(statusCode, message)
      },
    }
  }

  return {
    addAttempt,
    removeLastAttempt,
    clearThrottling,
    getAttempts,
    getStats,
    setOtpStage,
  }
}

export default async function (req: Request, res: Response, next: NextFunction) {
  try {
    const { email = '', username = '' } = req.body
    const originIP = getIpFromRequest(req)
    const [parsedMail] = username.split(':')

    const { addAttempt, clearThrottling, removeLastAttempt, getStats, setOtpStage } = await useLoginAttempts(
      email || parsedMail,
      originIP,
    )

    const { remaining: lastRemaining, getRemainingTime, isOtpStage } = await getStats()

    if (!lastRemaining) {
      if (getRemainingTime() <= 0) {
        await clearThrottling()
      }

      logger.logEvent('authn_login_fail_max', { email }, 'login_blocked_by_to_exceeded_attempt', 'error')
      next(new TooManyRequestsHttpError())
      return
    }

    // Quando está no stage de otp não computa attempt pelo acesso de my-accounts ou pelo middleware oAuth2AccountBlocks
    if (!isOtpStage() || req.headers['x-digisac-otp']) {
      await addAttempt()
    }
    const { limit, window, resetsAt, remaining, getThrottleError } = await getStats()

    res.setHeader('X-RateLimit-Policy', `${limit};w=${window}`)
    res.setHeader('X-RateLimit-Limit', limit)
    res.setHeader('X-RateLimit-Remaining', remaining)
    res.setHeader('X-RateLimit-Reset', resetsAt)

    res.locals.trottleLogin = {
      isOtpStage,
      setOtpStage,
      clearThrottling,
      removeLastAttempt,
      getThrottleError,
    }
  } catch (error) {
    next(errorToHttpError(error))
  }

  next()
}
