import { Request, Response, NextFunction } from 'express'
import { Container } from 'typedi'
import RequestContextCls from '../services/cls/RequestContextCls'
import { TracerToken } from '../services/tracer/Tracer'
import { GenericTracer } from '../services/tracer/GenericTracer'

export default () => (req: Request, res: Response, next: NextFunction) => {
  res.locals.impersonate = req.headers.impersonate === 'true'

  const impersonate = res.locals.impersonate

  const requestContextCls = Container.get(RequestContextCls)

  const traceparent = (req.headers.traceparent as string) || req.body?.metadata?.traceparent

  requestContextCls.run(() => {
    requestContextCls.setTraceIds(
      {
        traceparent,
      },
      true,
    )

    const tracer = Container.get(TracerToken)

    if (tracer instanceof GenericTracer) {
      tracer.startTransaction('Request', 'request')
    }

    requestContextCls.setContext(
      {
        impersonate,
        platform: req.headers.platform as string,
        client: res.locals.client,
        requestUri: req.url,
        requestMethod: req.method,
        requestStatusCode: req.statusCode,
        userAgent: req.headers['user-agent'] as string,
        sourceIp: (req.headers['x-real-ip'] as string) || req.connection?.remoteAddress,
        geo: req.headers['CF-IPCountry'] as string,
      },
      true,
    )

    next()
  })
}
