import { Request, Response, NextFunction } from 'express'
import { Container } from 'typedi'
import RequestContextCls from '../services/cls/RequestContextCls'
import RedisCacheStorage from '../services/cache/RedisCacheStorage'
import { UserInstance } from '../dbSequelize/models/User'

export default () => (req: Request, res: Response, next: NextFunction) => {
  const requestContextCls = Container.get(RequestContextCls)

  const user: UserInstance = res.locals.oauth.token.user

  if (!user) {
    next(new Error('No user set.'))
    return
  }

  const service = Container.get(RedisCacheStorage)
  const now = new Date()
  const utc = new Date(now.getTime() + now.getTimezoneOffset() * 60000)
  service.set(`lastActivity:${user.id}`, utc)

  // @ts-ignore
  res.locals.user = req.user = user
  res.locals.impersonate = req.headers.impersonate === 'true'

  const userId = user.id
  const userEmail = user.email
  const userName = user.name
  const accountId = user.accountId
  const accessTokenId = res.locals.oauth.token.accessTokenId
  const impersonate = res.locals.impersonate
  const client = res.locals.client
  const platform = req.headers.platform as string

  requestContextCls.setContext(
    {
      userId,
      userEmail,
      userName,
      accountId,
      accessTokenId,
      impersonate,
      client,
      platform,
    },
    true,
  )

  next()
}
