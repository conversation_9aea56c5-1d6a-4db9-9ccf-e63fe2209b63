import isEmpty from 'lodash/isEmpty'
import { getErrors } from '../utils/validator'
import ValidationError from '../utils/error/ValidationError'

const locations = ['body', 'params', 'query', 'headers', 'cookies']

const validate = (rules, options) => async (req, res, next) => {
  const mergedOptions = {
    currentUser: res.locals?.user,
    req,
    res,
    next,
    ...options,
  }

  const errors = {}

  for (const location of locations) {
    if (!(location in rules)) continue
    const locationErrors = await getErrors(req[location], rules[location], mergedOptions)
    if (isEmpty(locationErrors)) continue
    errors[location] = locationErrors
  }

  if (isEmpty(errors)) {
    next()
    return
  }

  if (!mergedOptions?.hasError) next(new ValidationError(errors))
}

const validateWithConditionalRules = (conditionFunction, thenRules, elseRules, options) => (req, res, next) => {
  const result = conditionFunction(req, thenRules, elseRules)
  return validate((typeof result == 'boolean' ? thenRules : elseRules) || result, options)(req, res, next)
}

export default validate
export { validateWithConditionalRules }
