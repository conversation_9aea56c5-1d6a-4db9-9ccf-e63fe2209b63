import { addDays, differenceInMinutes, subDays } from 'date-fns'
import { isUndefined, negate, omit, pick, pickBy } from 'lodash'
import sequelize, { Op } from 'sequelize'
import Container from 'typedi'
import moment from 'moment'
import { sendGracePeriodNotification } from '../../microServices/workers/jobs/account/GracePeriodJob'
import config from '../config'
import { AccountInstance } from '../dbSequelize/models/Account'
import accountRepository from '../dbSequelize/repositories/accountRepository'
import { Options } from '../dbSequelize/repositories/BaseSequelizeRepository'
import CreditsControlCacheService from '../services/creditMovement/CreditsControlCacheService'
import { createEncryptedEncryptionKey } from '../services/crypt/accountCryptor'
import { sendMFANotification } from '../services/email'
import reportError from '../services/logs/reportError'
import BadRequestHttpError from '../utils/error/BadRequestHttpError'
import iteratePaginated from '../utils/iteratePaginated'
import BaseResource from './BaseResource'
import creditMovementResource from './creditMovementResource'
import departmentResource from './departmentResource'
import notificationResource from './notificationResource'
import permissionResource from './permissionResource'
import roleResource from './roleResource'
import serviceResource, { TYPES } from './serviceResource'
import planAiHistoryResource from './planAiHistoryResource'
import subscriptionResource from './subscriptionResource'
import tagResource from './tagResource'
import userResource from './userResource'
import i18next from '../i18n'
import queuedAsyncMap from '../utils/array/queuedAsyncMap'
import { HttpClient } from '../services/httpClient/HttpClient'
import { RemoveIaProductsDtoInterface } from './interfaces/RemoveIaProductsDtoInterface'
import AgnusApi from '../services/agnus/AgnusApi'

const createDefaultRoles = async (account, options) => {
  const accountId = account.id

  const allPermissions = await permissionResource.findMany({
    where: {
      name: {
        $notIn: ['tickets.view.all', 'messages.view.out.queue', 'tickets.view.all.departments'],
      },
    },
  })
  const adminRole = await roleResource.create(
    {
      displayName: 'Administrador',
      accountId,
      isAdmin: true,
    },
    options,
  )

  await adminRole.setPermissions(allPermissions, options)

  const operatorPermission = await permissionResource.findManyByNames([
    'chat',
    'chat.view.contacts',
    'services.view',
    'services.operate',
    'messages.view.all',
    'messages.view.department',
    'messages.view.owned',
    'contacts.view',
    'contacts.create',
    'contacts.update',
    'quickReplies.view',
    'tags.view',
    'tickets.open',
    'tickets.close.all',
    'tickets.close.me',
    'tickets.transfer.all',
    'tickets.transfer.me',
    'tickets.update',
  ])
  const operatorRole = await roleResource.create(
    {
      displayName: 'Operador',
      accountId,
      isAdmin: false,
    },
    options,
  )
  await operatorRole.setPermissions(operatorPermission, options)

  return [adminRole, operatorRole]
}

export class AccountResource extends BaseResource<AccountInstance> {
  constructor() {
    super(accountRepository)
  }

  // @ts-ignore
  async create(data, options = {}) {
    return this.getRepository().transaction(async (transaction) => {
      const settings = data.settings || {}

      const drivers = TYPES.reduce((obj, type) => {
        obj[type] = false
        return obj
      }, {})

      let account = await accountRepository.create(
        {
          ...data,
          encryptionKey: createEncryptedEncryptionKey().toString('hex'),
          settings: {
            timezone: 'ESAST',
            ticketOpenNotification: true,
            ticketTransferNotification: true,
            newMessageNotification: true,
            showSupportInfo: true,
            allowDuplicateNames: false,
            changeUserPasswordOnFirstAccess: false,
            userPasswordCreationMethod: 'manual',
            topicRequired: false,
            ticketsEnabled: true,
            isUserEmailRequired: true,
            isQueueNotificationActive: false,
            disableDefaultTicketTransfer: false,
            userAwayMinutesTime: 5,
            drivers: {
              ...drivers,
              whatsapp: true,
              telegram: true,
              'sms-wavy': true,
              email: true,
              'facebook-messenger': true,
              instagram: true,
              'google-business-message': true,
              'reclame-aqui': true,
              ...settings.drivers,
            },
            campaign: {
              whatsapp: false,
              'sms-wavy': true,
              'whatsapp-business': false,
              'auto-pause-mode': 'disabled',
              ...settings.campaign,
            },
            flags: settings.flags,
            showTagsInChat: false,
            userCanChangeVisibilityTagsOnChat: false,
            autoGenerateSummaryOnTransfer: false,
            autoGenerateSummaryOnClosure: false,
            outsideTheKnowledgeBase: false,
          },
          isCampaignActive: true,
          wizardProgress: data.wizardProgress || 'finished',
          expiresAt: data.expiresAt || null,
          isPasswordExpirationActive: false,
          expirationPasswordTime: 0,
        },
        { transaction, ...options },
      )
      const department = await departmentResource.create(
        {
          accountId: account.id,
          name: 'Suporte',
        },
        { transaction, ...options },
      )

      account = await accountRepository.updateById(
        account.id,
        {
          defaultDepartmentId: department.id,
          settings: {
            ...((account?.settings.flags || {})?.['enable-smart-csat-score'] && {
              smartCsatScoreEnabledAt: new Date(),
            }),
          },
        },
        { transaction, ...options, mergeJson: ['settings'] },
      )

      await createDefaultRoles(account, { transaction, ...options })

      await creditMovementResource.create(
        {
          type: 'in',
          serviceType: 'sms-wavy',
          origin: 'trial',
          amount: 20,
          accountId: account.id,
        },
        { transaction, ...options },
      )

      // @ts-ignore
      this.emitCreated(account, options.eventTransaction)

      return account
    })
  }

  // @ts-ignore
  async update(
    model: AccountInstance,
    data: Partial<AccountInstance>,
    options: Options<AccountInstance> & { mergeJson?: string[] } = {},
  ) {
    const hasExpireTimeChanged = data.expiresAt && data.expiresAt !== model.expiresAt
    const hasMFAChanged = data.settings?.twoFactorAuthMandatory !== model.settings?.twoFactorAuthMandatory

    const isSmartCsatScoreEnabledTransition =
      data.settings?.flags?.['enable-smart-csat-score'] && !model.settings?.flags?.['enable-smart-csat-score']

    // quando a conta estiver ativa remover os campos validos para bloqueio de conta
    const getMergedPlanData = () => {
      const mergedPlan = {
        ...model.plan,
        ...data.plan,
      }
      return data.isActive || model.isActive
        ? omit(mergedPlan, [
            'isOnGracePeriod',
            'isGracePeriodOnHold',
            'gracePeriodExtendTimesRemaining',
            'gracePeriodEndsAt',
          ])
        : mergedPlan
    }

    let mergedData = {
      ...data,
      settings: {
        ...model.settings,
        ...data.settings,
        ...(model?.settings?.idHub360 !== data?.settings?.idHub360 && {
          idClientHub360: null,
        }),
        ...(isSmartCsatScoreEnabledTransition && {
          smartCsatScoreEnabledAt: new Date(),
        }),
      },
      plan: {
        ...getMergedPlanData(),
        ...(data?.isActive === false &&
          data?.plan?.isActive === false &&
          model?.isActive && {
            isOnGracePeriod: true,
            isGracePeriodOnHold: true,
            gracePeriodExtendTimesRemaining: config('gracePeriodExtendTimes'),
            gracePeriodEndsAt: new Date(),
          }),
        ...pickBy(pick(data.plan, ['users', 'services', 'isTrial']), negate(isUndefined)),
        emailNotifications: {
          ...model.plan.emailNotifications,
          ...(hasExpireTimeChanged && {
            expiredNotificationSent: false,
            expirationNotificationSent: false,
          }),
          ...(data.plan || {}).emailNotifications,
        },
      },
    }

    if (typeof (data.plan || {}).services === 'number') {
      mergedData = {
        ...mergedData,
        plan: {
          ...mergedData.plan,
          services: {
            ...mergedData.plan.services,
            whatsapp: data.plan.services,
          },
        },
      }
    }

    // Verifica se foi desativado o MFA
    if (hasMFAChanged && data.settings?.twoFactorAuthMandatory === false) {
      await this.sendEmailDisable(model.id)
    }

    const updatedAccount = await super.update(model, mergedData, options)

    if (!updatedAccount) return null

    if (
      (((mergedData.settings.flags || {})['internal-chat'] && updatedAccount.settings.flags) || {})['internal-chat']
    ) {
      Promise.resolve(userResource.bulkGenerateTokenFromInternalChat(updatedAccount.id))
    }

    if (mergedData.settings?.isPasswordExpirationActive && updatedAccount.settings.isPasswordExpirationActive) {
      Promise.resolve(userResource.setExpirationPasswordManyUsers(updatedAccount, { setExpirationToAllUsers: false }))
    }

    if (hasMFAChanged && updatedAccount.settings.twoFactorAuthMandatory) {
      Promise.resolve(
        this.expiresUserAccessForTwoFactorAuth(
          updatedAccount,
          updatedAccount.settings.twoFactorAuthMandatorySchedule,
          true,
        ),
      )
    }

    return updatedAccount
  }

  async sendEmailDisable(accountId: string) {
    const users = await userResource.findAdminUsers(accountId)

    await users.map(async (user) => {
      i18next.changeLanguage(user.language)

      await sendMFANotification({
        to: user.email,
        titulo: i18next.t('account:MFA_TITULO_DISABLE'),
        texto: i18next.t('account:MFA_TEXTO_DISABLE'),
        texto_final: i18next.t('account:MFA_TEXTO_FINAL_DISABLE'),
        texto_rodape: i18next.t('account:MFA_TEXTO_RODAPE_DISABLE'),
      })
    })
  }

  async expiresUserAccessForTwoFactorAuth(account: AccountInstance | string, ttl: Date, notify = false) {
    const accountId = account?.id || account
    const expiresAt = new Date(ttl)
    const now = new Date()

    // Calcula a diferença em horas e minutos
    const diffInMinutes = differenceInMinutes(expiresAt, now)

    const horas = Math.floor(diffInMinutes / 60)
    const minutesToLeft = diffInMinutes % 60

    // Formata o tempo restante como uma string "HH:MM"
    const timeToLeft = `${String(horas).padStart(2, '0')}:${String(minutesToLeft).padStart(2, '0')}`

    const notificationMessage = (lang = 'pt-BR') => {
      const messages = {
        'en-US': `Your access will expire in ${timeToLeft} minutes. On your next access, set up two-factor authentication (2FA).`,
        es: `Tu acceso expirará en ${timeToLeft} minutos. En el próximo acceso, configura la autenticación de dos factores (2FA).`,
        'pt-BR': `Seu acesso expirará em ${timeToLeft} minutos. No próximo acesso configure a autenticação de dois fatores (2FA).`,
      }

      return messages[lang] || messages['pt-BR']
    }

    return iteratePaginated(
      ({ page }) =>
        userResource.findManyPaginated({
          attributes: ['id', 'accountId', 'status', 'language'],
          where: {
            accountId,
            otpAuthActive: false,
          },
          page,
          perPage: 500,
        }),
      async (user) => {
        if (notify && user.status !== 'offline') {
          await notificationResource.create({
            userId: user.id,
            type: 'warn',
            text: notificationMessage(user.language),
            accountId: user.accountId,
          })
        }
      },
      20,
    )
  }

  async extendGracePeriod(account: AccountInstance, user: UserInstance) {
    if (!account.plan.isOnGracePeriod) {
      throw new BadRequestHttpError('Not on grace period.')
    }

    if (!(account.plan.gracePeriodExtendTimesRemaining > 0)) {
      throw new BadRequestHttpError('Cannot extend grace period anymore.')
    }

    await sendGracePeriodNotification(account, user).catch(reportError)

    const updatedAccount = await this.update(
      account,
      {
        plan: {
          gracePeriodExtendTimesRemaining: account.plan.gracePeriodExtendTimesRemaining - 1,
          gracePeriodEndsAt: addDays(new Date(), 1),
          isGracePeriodOnHold: false,
          isActive: true,
        },
      },
      { mergeJson: ['plan'] },
    )

    if (config('gracePeriodWebhookUrl') && config('gracePeriodWebhookToken') && account.correlationId) {
      await this.getHttpClient()
        .post(config('gracePeriodWebhookUrl'), {
          token: config('gracePeriodWebhookToken'),
          id: account.correlationId,
        })
        .catch(reportError)
    }
    return updatedAccount
  }

  async destroy(account, options) {
    return this.getRepository().transaction(async (transaction) => {
      await roleResource.destroyMany({ where: { accountId: account.id } }, { transaction, ...options })
      await userResource.destroyMany({ where: { accountId: account.id } }, { transaction, ...options })
      await serviceResource.destroyMany({ where: { accountId: account.id } }, { transaction, ...options })
      await tagResource.destroyMany({ where: { accountId: account.id } }, { transaction, ...options })
      await subscriptionResource.destroyMany({ where: { accountId: account.id } }, { transaction, ...options })

      return super.destroy(account, {
        transaction,
        ...options,
      })
    })
  }

  async accountClientNotAccess() {
    return this.getRepository().findMany({
      attributes: ['id', 'name', 'settings', 'agnusSignatureKey', 'createdAt'],
      where: {
        $and: [sequelize.where(sequelize.literal(`plan->'isTrial'`), 'false')],
        createdAt: {
          [Op.lt]: subDays(new Date(), 3),
        },
        name: {
          [Op.ne]: 'MandeUmZap',
        },
      },
      group: 'Account.id',
      include: {
        attributes: [],
        model: 'messages',
        group: 'accountId',
      },
      having: sequelize.literal(
        `max(messages."createdAt") >= now() + interval '3' day or MAX(messages."createdAt") is null`,
      ),
    })
  }

  async accountTrialNotFirstAccess() {
    return this.getRepository().findMany({
      attributes: ['id', 'name', 'settings', 'agnusSignatureKey', 'createdAt'],
      where: {
        $and: [sequelize.where(sequelize.literal(`plan->'isTrial'`), 'true')],
        wizardProgress: {
          [Op.eq]: 'started',
        },
        createdAt: {
          [Op.lt]: subDays(new Date(), 1),
        },
        name: {
          [Op.ne]: 'MandeUmZap',
        },
      },
      include: {
        attributes: [],
        model: 'users',
        where: {
          isFirstLogin: true,
        },
      },
    })
  }

  async accountTrialNotCompletWizard() {
    return this.getRepository().findMany({
      attributes: ['id', 'name', 'settings', 'agnusSignatureKey', 'createdAt'],
      where: {
        wizardProgress: {
          [Op.ne]: 'finished',
        },
        $and: [sequelize.where(sequelize.literal(`plan->'isTrial'`), 'true')],
        createdAt: {
          [Op.lt]: subDays(new Date(), 1),
        },
        name: {
          [Op.ne]: 'MandeUmZap',
        },
      },
      include: {
        attributes: [],
        model: 'users',
        where: {
          isFirstLogin: false,
        },
      },
    })
  }

  async accountTrialNotAccess() {
    return this.getRepository().findMany({
      attributes: ['id', 'name', 'settings', 'agnusSignatureKey', 'createdAt'],
      where: {
        $and: [sequelize.where(sequelize.literal(`plan->'isTrial'`), 'true')],
        wizardProgress: {
          [Op.eq]: 'finished',
        },
        createdAt: {
          [Op.lt]: subDays(new Date(), 1),
        },
        name: {
          [Op.ne]: 'MandeUmZap',
        },
      },
      group: 'Account.id',
      include: {
        attributes: [],
        model: 'messages',
        having: sequelize.literal(
          `max(messages."createdAt") >= now() + interval '1' day or MAX(messages."createdAt") is null`,
        ),
        group: 'accountId',
      },
    })
  }

  async incrementUsedHsm(account) {
    if ((account.settings.flags || {})?.['disable-hsm-limit']) {
      return null
    }
    return super.update(
      account,
      {
        plan: {
          hsmUsedLimit: account.plan.hsmUsedLimit + 1,
        },
      },
      { mergeJson: ['plan'], dontEmit: true },
    )
  }

  async decrementUsedHsm(account) {
    if ((account.settings.flags || {})?.['disable-hsm-limit']) {
      return null
    }
    return super.update(
      account,
      {
        plan: {
          hsmUsedLimit: account.plan.hsmUsedLimit - 1,
        },
      },
      { mergeJson: ['plan'], dontEmit: true },
    )
  }

  async resetHsmLimit(account) {
    if ((account.settings.flags || {})?.['disable-hsm-limit']) {
      return null
    }
    return super.update(
      account,
      {
        plan: {
          hsmUsedLimit: 0,
        },
      },
      { mergeJson: ['plan'] },
    )
  }

  async setDataBySignatureId(account: AccountInstance) {
    return this.getRepository().update(
      account,
      {
        data: {
          term: {
            terms: account.data.term.terms,
            nextCheckDate: account.data.term.nextCheckDate,
          },
        },
      },
      { mergeJson: ['data'] },
    )
  }

  async generateNewExpirationDate(
    account: AccountInstance | string,
    options = {},
    isFirstLogin = false,
  ): Promise<Date> {
    if (typeof account === 'string') {
      account = await this.findById(account, { attributes: ['id', 'settings'], ...options })
    }
    const {
      settings: {
        isPasswordExpirationActive = false,
        expirationPasswordTime = 0,
        changeUserPasswordOnFirstAccess = false,
      },
    } = account

    if (isFirstLogin && changeUserPasswordOnFirstAccess) {
      return new Date()
    }

    return isPasswordExpirationActive ? addDays(new Date(), expirationPasswordTime) : null
  }

  /**
   * Busca todas as contas ativas e não expiradas, com controle de crédito habilitado
   * @param options - propriedades adicionais para a query
   * @returns Lista de contas encontradas
   */
  creditsControlEnabledMany(options?: Options<AccountInstance>) {
    return this.getRepository().findMany({
      attributes: ['id', 'creditsControl'], // Retorna apenas os campos necessários,
      ...options,
      where: {
        ...options?.where,
        'creditsControl.enabled': true, // Controle de crédito habilitado
        isActive: true, // Conta ativa
        $or: [{ expiresAt: null }, { expiresAt: { $gte: new Date() } }], // Não expirada
      },
    })
  }

  /**
   * Busca apenas uma conta ativa e não expirada com controle de crédito habilitado
   * @param accountId - ID da conta desejada
   * @param options - propriedades adicionais para a query
   * @returns Conta encontrada
   */
  creditsControlEnabledById(accountId: string, options?: Options<AccountInstance>) {
    return (
      accountId &&
      this.creditsControlEnabledMany({
        attributes: ['id', 'creditsControl'], // Retorna apenas os campos necessários,
        ...options,
        where: {
          ...options?.where,
          id: accountId, // Filtra pelo id da conta, se for informado um valor de accountId
        },
      }).then((accounts) => accounts?.[0])
    )
  }

  /**
   * Atualiza o controle de crédito de uma conta
   * @param accountId - ID da conta
   * @param creditsControl - Objeto parcial com as informações do controle de crédito para serem atualizadas na conta
   */
  async accountUpdateCreditsControl(accountId: string, creditsControl: Partial<AccountInstance['creditsControl']>) {
    const payload = {
      creditsControl,
    }

    const options = {
      mergeJson: ['creditsControl'],
    }

    // @ts-ignore - Utilizando mergeJson, não precisa especificar todas as propriedades de creditsControl para que seja atualizado parcialmente
    return super.updateById(accountId, payload, options)
  }

  /**
   * Busca uma conta ativa e não expirada através do seu ID
   * @param accountId - ID da conta desejada
   * @returns Conta encontrada
   */
  getAccountPlan(accountId: string): Promise<Pick<AccountInstance, 'id' | 'plan' | 'creditsControl'>> {
    return this.getRepository().findOne({
      attributes: ['id', 'plan', 'creditsControl'], // Retorna apenas os campos necessários,
      where: {
        id: accountId, // Filtra pelo id da conta
        $and: [
          { $or: [{ isActive: true }, { 'plan.isActive': true }] }, //Conta ativa ou em carência
          { $or: [{ expiresAt: null }, { expiresAt: { $gte: new Date() } }] }, // Não expirada
        ],
      },
    })
  }

  /**
   * Atualiza o plano de uma conta
   * @param accountId - ID da conta
   * @param plan - Objeto parcial com as informações do plano para serem atualizadas na conta
   */
  async accountUpdatePlan(accountId: string, plan: Partial<AccountInstance['plan']>) {
    const payload = {
      plan,
    }

    const options = {
      mergeJson: ['plan'],
    }

    // @ts-ignore - Utilizando mergeJson, não precisa especificar todas as propriedades de plan para que seja atualizado parcialmente
    return super.updateById(accountId, payload, options)
  }

  /**
   * Busca as informações quantitativas de uma conta
   * @param accountId - ID da conta desejada
   * @param filter - Filtro das informações desejadas
   * @param blockedFilter - Itens do filtro que estão bloqueados pelo controller
   * @returns Informações quantitativas
   */
  async getAccountAmounts(accountId: string, filter: string[], blockedFilter: string[] = []) {
    const account = await this.getAccountPlan(accountId)

    if (!account) {
      throw new BadRequestHttpError(`Account not found for: ${accountId}`)
    }

    const creditsControlCacheService = Container.get(CreditsControlCacheService)

    const hasCreditsControlEnabled = await creditsControlCacheService.isCreditsControlEnabled(accountId)

    if (!hasCreditsControlEnabled) {
      throw new BadRequestHttpError(`Account credits control is disabled for: ${accountId}`)
    }

    const isRequired = (param: string) => {
      // O parâmetro não está incluso no blockedFilter e o filtro é vazio ou inclui o parâmetro
      return !blockedFilter.includes(param) && (!filter.length || filter.includes(param))
    }

    const [credits, services, users] = await Promise.all([
      creditsControlCacheService.getCredits(account, isRequired('credits.amounts')),
      serviceResource.getServices(account, isRequired('services.amounts')),
      userResource.getUsers(account, isRequired('users.amounts')),
    ])

    return {
      accountId,
      ...credits,
      ...services,
      ...users,
    }
  }

  async getAgnusData(agnusSignatureKey: string): Promise<any> {
    try {
      const agnusApi = new AgnusApi()
      const signature = await agnusApi.getSignature(agnusSignatureKey)
      if (!signature?.contract_id) return null

      return agnusApi.getContract(signature?.contract_id)
    } catch (error) {
      reportError(error)
      return { due_day: moment().format('DD') }
    }
  }

  async getRenewDate(agnusSignatureKey: string): Promise<String> {
    const data = await this.getAgnusData(agnusSignatureKey)
    const dueDay = data?.due_day ?? moment().format('DD')

    if (!dueDay) {
      throw new Error('Resource: We have a problem to get the due day from agnus')
    }

    let renewDate = moment().format('YYYY-MM-') + dueDay
    if (dueDay > moment().date()) {
      renewDate = moment(renewDate).subtract(1, 'month').format('YYYY-MM-DD')
    }

    return renewDate
  }

  /**
   * Atualiza os dados dos produtos da IA vindos do agnus
   * @param accountId - ID da conta desejada
   * @param products - Os produtos de IA da conta
   * @param AIs - A quantidade total de créditos que devem ser atribuidos a conta
   * @param renewDate - Informa a data de renovação do contrato
   */
  async updateIaProducts(accountId: string, products: string, ais: any, renewDate?: Date | String) {
    const account = await this.findById(accountId, { attributes: ['plan', 'settings', 'agnusSignatureKey'] })

    if (!renewDate && !account?.plan?.renewDate && account?.agnusSignatureKey) {
      renewDate = await this.getRenewDate(account?.agnusSignatureKey)
    }

    await this.updateById(
      accountId,
      {
        plan: {
          ai: {
            summary: parseInt(ais?.summary || 0),
            'magic-text': parseInt(ais?.['magic-text'] || 0),
            transcription: parseInt(ais?.transcription || 0),
            copilot: parseInt(ais?.copilot || 0),
            csat: parseInt(ais?.csat || 0),
            agent: parseInt(ais?.agent || 0),
          },
          ...(products && { products }),
          ...(renewDate && { renewDate }),
        },
        settings: {
          flags: {
            ...account.settings.flags,
            'enable-smart-summary': ais?.summary > 0 || account.settings.flags['enable-smart-summary'],
            'enable-audio-transcription':
              ais?.transcription > 0 || account.settings.flags['enable-audio-transcription'],
            'enable-magic-text': ais?.['magic-text'] > 0 || account.settings.flags['enable-magic-text'],
            'enable-copilot': ais?.copilot > 0 || account.settings.flags['enable-copilot'],
            'enable-smart-csat-score': ais?.csat > 0 || account.settings.flags['enable-smart-csat-score'],
            'enable-bots-v3-ai-node': ais?.agent > 0 || account.settings.flags['enable-bots-v3-ai-node'],
          },
        },
      },
      { mergeJson: ['plan', 'settings'] },
    )

    if (products) {
      await planAiHistoryResource.create({
        accountId,
        activity: 'change',
        name: products,
        magicText: !!ais?.['magic-text'],
        summary: !!ais?.summary,
        transcription: !!ais?.transcription,
        copilot: !!ais?.copilot,
        csat: !!ais?.csat,
        agent: !!ais?.agent,
      })
    }

    await queuedAsyncMap(
      Object.keys(ais),
      async (ai) => {
        if (ais?.[ai] > 0) {
          const diff = ais?.[ai] - (account?.plan?.ai?.[ai] || 0)

          if (diff > 0) {
            await creditMovementResource.create({
              type: 'in',
              serviceType: ai,
              origin: 'agnus',
              amount: diff,
              accountId: accountId,
            })
            return
          }
        }
      },
      1,
    )
  }

  protected getHttpClient() {
    return Container.get(HttpClient)
  }

  async removeIaProducts(params: RemoveIaProductsDtoInterface) {
    const account = await this.findById(params.accountId, { attributes: ['plan', 'settings'] })
    if (!account) {
      throw new BadRequestHttpError(`Account: ${params.accountId} not found`)
    }
    const ais = {
      summary:
        parseInt(account.plan.ai?.summary || 0) > params.ais?.summary
          ? parseInt(account.plan.ai?.summary || 0) - params.ais?.summary
          : 0,
      'magic-text':
        parseInt(account.plan.ai?.['magic-text'] || 0) > params.ais?.['magic-text']
          ? parseInt(account.plan.ai?.['magic-text'] || 0) - params.ais?.['magic-text']
          : 0,
      transcription:
        parseInt(account.plan.ai?.transcription || 0) > params.ais?.transcription
          ? parseInt(account.plan.ai?.transcription || 0) - params.ais?.transcription
          : 0,
      copilot:
        parseInt(account.plan.ai?.copilot || 0) > params.ais?.copilot
          ? parseInt(account.plan.ai?.copilot || 0) - params.ais?.copilot
          : 0,
      csat:
        parseInt(account.plan.ai?.csat || 0) > params.ais?.csat
          ? parseInt(account.plan.ai?.csat || 0) - params.ais?.csat
          : 0,
      agent:
        parseInt(account.plan.ai?.agent || 0) > params.ais?.agent
          ? parseInt(account.plan.ai?.agent || 0) - params.ais?.agent
          : 0,
    }
    await this.updateById(
      params.accountId,
      {
        plan: {
          ai: ais,
          ...(params.products && { products: params.products }),
        },
        settings: {
          flags: {
            ...account.settings.flags,
            'enable-smart-summary': ais?.summary > 0 || false,
            'enable-audio-transcription': ais?.transcription > 0 || false,
            'enable-magic-text': ais?.['magic-text'] > 0 || false,
            'enable-copilot': ais?.copilot > 0 || false,
            'enable-smart-csat-score': ais?.csat > 0 || false,
            'enable-bots-v3-ai-node': ais?.agent > 0 || false,
          },
        },
      },
      { mergeJson: ['plan', 'settings'] },
    )

    if (params.products) {
      await planAiHistoryResource.create({
        accountId: params.accountId,
        activity: 'change',
        name: params.products,
        magicText: params.ais?.['magic-text'] > 0,
        summary: params.ais?.summary > 0,
        transcription: params.ais?.transcription > 0,
        copilot: params.ais?.copilot > 0,
        csat: params.ais?.csat > 0,
        agent: params.ais?.agent > 0,
      })
    }

    await queuedAsyncMap(
      Object.keys(params.ais),
      async (ai) => {
        if (params.ais?.[ai] > 0) {
          await creditMovementResource.create({
            type: 'out',
            serviceType: ai,
            origin: params.origin,
            amount: params.ais?.[ai],
            accountId: params.accountId,
          })
        }
      },
      1,
    )
  }

  /**
   * Atualiza as feature flags vindo do agnus
   * @param accountId - ID da conta desejada
   * @param flags - flags para habilitar ou desabilitar
   */
  async enableFeatureFlags(accountId: string, flags: object) {
    const account = await this.findById(accountId, { attributes: ['settings'] })
    if (!account) {
      throw new BadRequestHttpError(`Account: ${accountId} not found`)
    }

    const updatedFlags = {
      ...account.settings.flags,
      ...flags,
    }

    return this.updateById(
      accountId,
      {
        settings: {
          flags: updatedFlags,
        },
      },
      { mergeJson: ['settings'] },
    )
  }
}

export default new AccountResource()
