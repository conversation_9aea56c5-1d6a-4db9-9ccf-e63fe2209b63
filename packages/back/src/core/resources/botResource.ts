import { difference } from 'lodash'
import Container from 'typedi'
import BaseResource, { EventTransaction, Options } from './BaseResource'
import botRepository from '../dbSequelize/repositories/botRepository'
import formatContexts from '../utils/bot/formatContexts'
import fileResource from './fileResource'
import { BotInstance } from '../dbSequelize/models/Bot'
import { BotVersionInstance } from '../dbSequelize/models/BotVersion'
import botVersionResource from './botVersionResource'
import AgentService from '../services/agent/AgentService'
import SuggestAgentPromptService from '../services/agent/SuggestAgentPromptService'
import { AgentConfig } from '../services/agent/utils'
import queuedAsyncMap from '../utils/array/queuedAsyncMap'

const hasValidConfigForCreateAgent = (agentPayload: AgentConfig): boolean => {
  if (!agentPayload) return false

  const requiredFields = [
    'voiceTone',
    'languageType',
    'function',
    'companySegment',
    'companySubject',
    'companyServices',
    'prompt',
  ]

  for (const field of requiredFields) {
    if (!agentPayload[field]) {
      return false
    }
  }

  if (!Array.isArray(agentPayload.actions) || agentPayload.actions.length === 0) {
    return false
  }

  if (agentPayload.actions.some(({ action }) => !action.description)) {
    return false
  }

  return true
}

const createFile = async ({ file, botId, accountId }, { transaction }) => {
  const { fileName, mimetype, base64Url } = file

  if (!base64Url) return

  const newFile = await fileResource.create(
    {
      name: fileName,
      base64Url,
      mimetype,
      attachedId: botId,
      attachedType: 'bot',
      accountId,
      encrypt: false,
    },
    { transaction },
  )

  return { id: newFile.id }
}

const saveAgentsAndFilesFromBotContexts = async (
  { contexts, botId, accountId, flowJson, isDraft, isDuplicated = false },
  options,
) =>
  Promise.all(
    contexts.map(async (context) => {
      let agentId = null
      if (context?.id?.startsWith('AI_NODE')) {
        agentId = context?.triggers?.[0]?.rules?.[0]?.actions?.[0]?.data?.agentId
        const agentData = context?.triggers?.[0]?.rules?.[0]?.actions?.[0]?.data

        if (isDuplicated) {
          const aiData = flowJson?.nodes?.find((node) => node.id === context.id)?.data
          const duplicatedAgentData = {
            ...aiData,
            ...agentData,
          }
          if (aiData?.agentId && hasValidConfigForCreateAgent(duplicatedAgentData)) {
            agentId = (await Container.get(AgentService).createAgent(duplicatedAgentData, accountId)).id
          }
        }

        if (!isDuplicated && !isDraft && hasValidConfigForCreateAgent(agentData)) {
          agentId = agentId
            ? (await Container.get(AgentService).updateAgent(agentId, agentData, accountId)).id
            : (await Container.get(AgentService).createAgent(agentData, accountId)).id
        }
      }

      if (agentId) {
        flowJson?.nodes?.map((node) => {
          if (node.id === context.id) {
            node.data.agentId = agentId
          }

          return node
        })
      }

      return {
        ...context,
        triggers: await Promise.all(
          context.triggers.map(async (trigger) => ({
            ...trigger,
            rules: await Promise.all(
              trigger.rules.map(async (rule) => ({
                ...rule,
                actions: await Promise.all(
                  rule.actions.map(async (action) => {
                    let file = null

                    if (action.data.file && !action.data.file.id) {
                      file = await createFile(
                        {
                          file: action.data.file,
                          botId,
                          accountId,
                        },
                        { ...options },
                      )
                    }

                    if (action.data.file && action.data.file.id) {
                      file = { id: action.data.file.id }
                    }

                    flowJson?.nodes?.map((node) => {
                      if (node.id === context.id && file) {
                        node.data.file = file
                      }
                      return node
                    })

                    return {
                      ...action,
                      data: {
                        ...action.data,
                        ...(file && { file }),
                        ...(agentId && { agentId }),
                      },
                    }
                  }),
                ),
              })),
            ),
          })),
        ),
      }
    }),
  )

const deleteUnusedFilesFromBot = async ({ contexts, files }, { transaction }) => {
  const currentFileIds = []

  contexts.forEach((context) =>
    context.triggers.forEach((trigger) =>
      trigger.rules.forEach((rule) =>
        rule.actions.forEach((action) => {
          if (!action.data || !action.data.file || !action.data.file.id) return

          currentFileIds.push(action.data.file.id)
        }),
      ),
    ),
  )

  const allFileIds = files.map((f) => f.id)
  const toDeleteFileIds = difference(allFileIds, currentFileIds)

  return fileResource.destroyMany({
    where: { id: toDeleteFileIds },
    transaction,
  })
}

export const COMMAND = 'command.no-model'
export const API_SIGNAL = 'api-signal.no-model'
export const INACTIVE = 'inactive.no-model'

export class BotResource extends BaseResource<BotInstance> {
  constructor() {
    super(botRepository)

    this.events = [...this.getEvents(), COMMAND, API_SIGNAL, INACTIVE]
  }

  onInactive(listener) {
    return this.on(INACTIVE, listener)
  }

  emitInactive(data, eventTransaction?: EventTransaction): void | boolean {
    return this.emit(INACTIVE, data, eventTransaction)
  }

  emitCommand(data, eventTransaction?: EventTransaction): void | boolean {
    return this.emit(COMMAND, data, eventTransaction)
  }

  onApiSignal(listener) {
    return this.on(API_SIGNAL, listener)
  }

  async update(bot, data, options = {}) {
    return this.getRepository().transaction(async (transaction) => {
      if (data.onlyUpdateBotName) {
        return super.update(
          bot,
          { name: data.name || bot.name || '' },
          {
            ...options,
            transaction,
          },
        )
      }

      const isDraft = typeof data.isDraft === 'boolean' ? data.isDraft : false

      const newContexts = await saveAgentsAndFilesFromBotContexts(
        {
          contexts: data.contexts,
          botId: bot.id,
          accountId: bot.accountId,
          flowJson: data.flowJson,
          isDraft,
        },
        { transaction },
      )

      // Os arquivos estão atrelados ao bot em uma ou mais versões
      // Atualmente, os arquivos do bot não são mais excluídos
      // await deleteUnusedFilesFromBot(
      //   {
      //     contexts: newContexts,
      //     files: bot.files || (await bot.getFiles()),
      //   },
      //   { transaction },
      // )

      const formattedContexts = formatContexts(newContexts)

      let botVersion: BotVersionInstance = null

      const latestBotVersion = await botVersionResource.findOne({
        where: {
          botId: bot.id,
          status: 'draft',
          accountId: data.accountId,
        },
        order: [['updatedAt', 'DESC']],
      })

      const payload: Partial<BotVersionInstance> = {
        status: isDraft ? 'draft' : 'published',
        contexts: formattedContexts,
        flowJson: data.flowJson,
        settings: data.settings,
        publishedAt: isDraft ? null : new Date(),
        botId: bot.id,
        createdById: data.userId,
        publishedById: isDraft ? null : data.userId,
        accountId: data.accountId,
      }

      const botVersionOptions = { dontEmit: true, transaction }

      if (latestBotVersion) {
        botVersion = await botVersionResource.update(latestBotVersion, payload, botVersionOptions)
      } else {
        botVersion = await botVersionResource.create(payload, botVersionOptions)
      }

      const botData = isDraft
        ? {
            name: data.name || bot.name || '',
            data: data.data,
          }
        : {
            name: data.name || bot.name || '',
            data: data.data,
            contexts: formattedContexts,
            settings: data.settings,
            flowJson: data.flowJson,
            currentBotVersionId: botVersion.id,
          }

      return super.update(bot, botData, {
        ...options,
        transaction,
      })
    })
  }

  async create(data, options = {}) {
    return this.getRepository().transaction(async (transaction) => {
      const { accountId, userId, name, contexts, contextsFormatted, settings, flowJson, isDuplicated } = data
      const formattedContexts = contextsFormatted ? contexts : formatContexts(contexts)

      let currentDefaultTerm = null

      await contexts.map((context) => {
        context?.triggers.map((trigger) => {
          trigger?.rules.map((rule) => {
            rule?.actions.map((action) => {
              if (action?.type?.value === 'SEND_TERM') {
                currentDefaultTerm = action.data.acceptanceTerms
              }
            })
          })
        })
      })

      const flowJsonWithoutBase64Files = flowJson?.nodes?.map((node) => {
        if (node.data?.file && !node.data.file.id) {
          delete node.data.file
        }
        return node
      })

      const bot = await super.create(
        {
          accountId,
          name,
          settings,
          contexts: formattedContexts,
          flowJson: flowJsonWithoutBase64Files,
        },
        {
          ...options,
          dontEmit: true,
          transaction,
        },
      )

      const isDraft = typeof data.isDraft === 'boolean' ? data.isDraft : false

      const newContexts = await saveAgentsAndFilesFromBotContexts(
        {
          contexts: data.contexts,
          botId: bot.id,
          accountId: bot.accountId,
          flowJson,
          isDraft,
          isDuplicated,
        },
        {
          transaction,
        },
      )

      const formattedContextsWithFiles = formatContexts(newContexts)

      const botVersion = await botVersionResource.create(
        {
          status: isDraft ? 'draft' : 'published',
          contexts: formattedContextsWithFiles,
          flowJson,
          settings,
          publishedAt: isDraft ? null : new Date(),
          botId: bot.id,
          createdById: userId,
          publishedById: isDraft ? null : userId,
          accountId,
        },
        {
          dontEmit: true,
          transaction,
        },
      )

      const botData = isDraft
        ? {
            data: { ...bot.data, currentDefaultTerm },
          }
        : {
            data: { ...bot.data, currentDefaultTerm },
            contexts: formattedContextsWithFiles,
            flowJson,
            currentBotVersionId: botVersion.id,
          }

      const updatedBot = await super.update(bot, botData, {
        ...options,
        dontEmit: true,
        transaction,
      })

      super.emitCreated(updatedBot)

      return updatedBot
    })
  }

  async suggestAgentPrompt(agentConfig: AgentConfig, accountId?: string): Promise<string> {
    const suggestAgentPromptService = Container.get(SuggestAgentPromptService)

    const prompt = await suggestAgentPromptService.getPrompt(agentConfig, accountId)
    return prompt
  }

  async destroy(model: BotInstance, options: Options<BotInstance> = {}) {
    // Delete agents associated with the bot
    if (model?.settings?.botCreatedVersion === 'v3') {
      const nodes = model.flowJson?.nodes || []
      const agentIds = nodes
        .filter((node) => node.id?.startsWith('AI_NODE') && node.data?.agentId)
        .map((node) => node.data.agentId)

      if (agentIds.length > 0) {
        const agentService = Container.get(AgentService)
        await queuedAsyncMap(agentIds, async (agentId) => {
          await agentService.deleteAgent(agentId)
        })
      }
    }

    await super.destroy(model, options)
  }

  async triggerSignal({ botId, contactId, flag }: { botId: string; contactId: string; flag?: string }): Promise<void> {
    this.emit(API_SIGNAL, { botId, contactId, flag })
  }
}

export default new BotResource()
