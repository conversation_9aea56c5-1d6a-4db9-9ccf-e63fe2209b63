import { omit } from 'lodash'
import BaseResource from './BaseResource'
import { CardMovementInstance } from '../dbSequelize/models/CardMovement'
import { CardInstance } from '../dbSequelize/models/Card'
import cardMovementRepository from '../dbSequelize/repositories/cardMovementRepository'
import pipelineStageRepository from '../dbSequelize/repositories/pipelineStageRepository'
import pipelineStageStatusRepository from '../dbSequelize/repositories/pipelineStageStatusRepository'

export class CardMovementResource extends BaseResource<CardMovementInstance> {
  constructor() {
    super(cardMovementRepository)
  }

  async validate(pipelineId: string, pipelineStageId: string, stageStatusId?: string) {
    const stage = await pipelineStageRepository.findOne({ where: { id: pipelineStageId, pipelineId } })
    if (!stage) {
      throw new Error(`Stage was not found for this pipeline`)
    }
    if (stageStatusId) {
      const stageStatus = await pipelineStageStatusRepository.findOne({
        where: { id: stageStatusId, stageId: stage?.id },
      })
      if (!stageStatus) {
        throw new Error(`Status was not found for this stage`)
      }
    }
  }

  async move(card: CardInstance, newData: any) {
    const { pipelineId = null, pipelineStageId = null, statusId = null, ownerId = null } = newData

    if (!pipelineStageId && !statusId && !ownerId) return

    const lastMovement = await this.findOne({
      where: { cardId: card?.id },
      order: [['createdAt', 'desc']],
    })

    if (
      !lastMovement ||
      (pipelineId && lastMovement?.toPipelineId !== pipelineId) ||
      (pipelineStageId && lastMovement?.toPipelineStageId !== pipelineStageId) ||
      (statusId && lastMovement?.toStageStatusId !== statusId) ||
      (ownerId && lastMovement?.toOwnerId !== ownerId)
    ) {
      await this.validate(pipelineId || card?.pipelineId, pipelineStageId || lastMovement?.toPipelineStageId, statusId)

      await super.create({
        ...omit(newData, ['id', 'createdAt']),
        cardId: card?.id,
        fromPipelineId: lastMovement?.toPipelineId || card?.pipelineId,
        toPipelineId: pipelineId || lastMovement?.toPipelineId || card?.pipelineId,
        fromPipelineStageId: lastMovement?.toPipelineStageId || card?.pipelineStageId,
        toPipelineStageId: pipelineStageId || lastMovement?.toPipelineStageId,
        accountId: lastMovement?.accountId || newData?.accountId || card?.accountId,
        pipelineId: newData?.toPipelineId || lastMovement?.toPipelineId || card?.pipelineId,
        fromStageStatusId: lastMovement?.toStageStatusId,
        toStageStatusId: statusId || lastMovement?.toStageStatusId,
        fromOwnerId: lastMovement?.toOwnerId || card?.ownerId,
        toOwnerId: ownerId || lastMovement?.toOwnerId || card?.ownerId,
      })
    }
  }
}

export default new CardMovementResource()
