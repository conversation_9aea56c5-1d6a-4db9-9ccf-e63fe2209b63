import BaseResource from './BaseResource'
import departmentRepository from '../dbSequelize/repositories/departmentRepository'
import accountResource from './accountResource'
import ticketService from '../services/ticket/ticketService'
import ticketResource from './ticketResource'
import iteratePaginated from '../utils/iteratePaginated'
import { literal } from 'sequelize'

export class DepartmentResource extends BaseResource {
  constructor() {
    super(departmentRepository)
  }

  async findManyPaginated(query = {}) {
    const includeUsersCount = query.customInclude?.includes('usersCount')
    const result = await this.getRepository().findManyPaginated({
      ...query,
      attributes: {
        ...query.attributes,
        ...(includeUsersCount && {
          include: [
            [
              literal(`(
              SELECT COUNT(ud."userId")
              FROM user_departments ud
              WHERE ud."departmentId" = "Department"."id"
            )`),
              'usersCount',
            ],
          ],
        }),
      },
      ...(Array.isArray(query.order) && {
        order: query.order.map(([association, direction]) => {
          if (association === 'usersCount' && includeUsersCount) {
            return [literal('"usersCount"'), direction]
          }
          return [association, direction]
        }),
      }),
    })
    return result
  }

  async findById(id, options = {}) {
    const department = await super.findById(id, {
      ...options,
      attributes: {
        ...options.attributes,
        include: [
          [
            literal(`(
              SELECT COUNT(ud."userId")
              FROM user_departments ud
              WHERE ud."departmentId" = "Department"."id"
            )`),
            'usersCount',
          ],
        ],
      },
    })

    return department
  }

  async destroy(department, options) {
    let account
    let destroyedDepartment

    const result = await this.getRepository().transaction(async (transaction) => {
      account = await accountResource.findOne({
        where: {
          defaultDepartmentId: department.id,
        },
      })

      if (account) {
        account = await accountResource.update(
          account,
          {
            defaultDepartmentId: null,
          },
          {
            transaction,
            dontEmit: true,
          },
        )
      }

      destroyedDepartment = await super.destroy(department, {
        transaction,
        ...options,
        dontEmit: false,
      })

      return destroyedDepartment
    })

    if (!options.dontEmit) {
      if (account) accountResource.emitUpdated(account)
      if (destroyedDepartment) this.emitUpdated(destroyedDepartment)
    }

    return result
  }

  // Requires sync zone
  async closeTicket(data, options) {
    return ticketService.closeTicket(data, options)
  }

  async closeTicketByDepartmentId(departmentId) {
    if (!departmentId) throw new Error(`Department #${departmentId} is required to close ticket.`)

    const ticketWithDepartmentCount = await ticketResource.count({
      where: {
        departmentId,
        isOpen: true,
      },
    })

    if (ticketWithDepartmentCount) {
      await this.eventTransaction(async (eventTransaction) => {
        await iteratePaginated(
          ({ page }) =>
            ticketResource.findManyPaginated({
              page,
              perPage: 100,
              include: [
                {
                  model: 'contact',
                  required: true,
                  include: [
                    {
                      model: 'currentTicket',
                      include: [
                        {
                          model: 'currentTicketTransfer',
                          required: true,
                        },
                      ],
                    },
                  ],
                },
              ],
              where: {
                departmentId,
                isOpen: true,
              },
            }),
          async (ticket) => this.closeTicket({ contact: ticket.contact }, { eventTransaction }),
        )
      })
    }
  }
}

export default new DepartmentResource()
