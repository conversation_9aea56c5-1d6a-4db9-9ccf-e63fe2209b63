import { Container } from 'typedi'
import BaseResource from './BaseResource'
import { KnowledgeBaseItemInstance } from '../dbSequelize/models/KnowledgeBaseItem'
import knowledgeBaseItemRepository from '../dbSequelize/repositories/knowledgeBaseItemRepository'
import knowledgeBaseItemDocRepository from '../dbSequelize/repositories/knowledgeBaseItemDocRepository'
import fileResource from './fileResource'
import HaystackIaApi from '../services/haystackIa'
import pdfParse from 'pdf-parse'

export type Payload = {
  fileId?: string
  text?: string
  url?: string
  name: string
  accountId: string
}
export default class KnowledgeBaseItemResource extends BaseResource<KnowledgeBaseItemInstance> {
  haystackIaApi: HaystackIaApi

  constructor() {
    super(knowledgeBaseItemRepository)
    this.haystackIaApi = Container.get(HaystackIaApi)
  }

  async findById(id: string, query = {}): Promise<KnowledgeBaseItemInstance> {
    const knowledge = await knowledgeBaseItemRepository.findById(id, query)

    if (knowledge?.docs?.length) {
      const docs = await this.haystackIaApi.findKnowledge(
        knowledge?.docs?.map((doc: any) => doc?.docId),
        knowledge?.accountId,
      )
      return { ...knowledge?.dataValues, docs }
    }

    return knowledge
  }

  async create(data: Payload): Promise<KnowledgeBaseItemInstance> {
    let name, source, docIds, sourceId

    try {
      if (!data?.fileId && !data?.text && !data?.url) {
        throw new Error('You must provide fileId, text, or url')
      }

      ;({ name, source } = await this.getSource(data))
      ;({ docIds, sourceId } = await this.haystackIaApi.createKnowledge(source, { ...data, name: name ?? data?.name }))
    } catch (err: any) {
      err.extra = { index: data?.index }
      throw err
    }

    const item = await knowledgeBaseItemRepository.create({ ...data, sourceId })

    await knowledgeBaseItemDocRepository.bulkCreate(
      docIds.map((docId: string) => ({ knowledgeBaseItemId: item?.id, docId, accountId: item?.accountId })),
      {},
    )

    if (data?.fileId) {
      await this.updateFileResource(data.fileId, item?.id)
    }

    return item
  }

  async destroyById(id: string): Promise<void> {
    const knowledgeItem = await knowledgeBaseItemRepository.findById(id, { include: ['docs'] })
    const knowledgeDocs = knowledgeItem?.docs

    if (knowledgeDocs?.length > 0) {
      const knowledgeDocIds = knowledgeDocs?.map((doc: any) => doc?.docId)

      await this.haystackIaApi.deleteKnowledgeDocs(knowledgeDocIds, knowledgeItem?.accountId)
    }

    await knowledgeBaseItemRepository.destroy(knowledgeItem, {})
  }

  private async getSource(data: Payload): Promise<{ source: any; name?: string }> {
    if (data?.fileId) {
      const file = await fileResource.findById(data?.fileId)
      return { name: file?.name, source: await fileResource.getBuffer(file) }
    }
    return { source: data?.text ?? data?.url }
  }

  private async updateFileResource(fileId: string, itemId: string): Promise<void> {
    await fileResource.updateById(fileId, {
      attachedId: itemId,
      attachedType: 'knowledgebase.item',
    })
  }

  private async checkPdfText(data: Payload): Promise<boolean> {
    const { source } = await this.getSource(data)

    if (Buffer.isBuffer(source)) {
      try {
        const pdfParsed = await pdfParse(source)

        return pdfParsed.text.trim().length > 0
      } catch (err) {
        return false
      }
    }

    return false
  }
}
