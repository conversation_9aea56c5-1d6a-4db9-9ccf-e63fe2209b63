import memoize from 'moize'
import oAuthAccessTokenRepository from '../dbSequelize/repositories/oAuthAccessTokenRepository'
import oAuthClientResource from './oAuthClientResource'
import BaseResource from './BaseResource'
import { OAuthAccessTokenInstance } from '../dbSequelize/models/OAuthAccessToken'

const getClient = memoize(() => oAuthClientResource.findOne({ where: { clientId: 'api' } }))

export type CreateOAuthAccessTokenDataInput = {
  name: string
  scope: string
  expiresIn: string
}

export class OAuthAccessTokenResource extends BaseResource<OAuthAccessTokenInstance> {
  constructor() {
    super(oAuthAccessTokenRepository)
  }

  async create(data: CreateOAuthAccessTokenDataInput, options = {}) {
    const {
      model: { generateAccessToken },
    } = require('../services/auth/oAuthServer')

    const mapExpirationDateToNumber = new Map([
      ['30d', 30],
      ['90d', 90],
      ['180d', 180],
      ['1y', 365],
      ['No expiration', 0],
    ])

    const expirationDate = mapExpirationDateToNumber.get(data.expiresIn) ?? 0

    const accessTokenExpiresAt = new Date(Date.now() + expirationDate * 24 * 60 * 60 * 1000)

    return super.create(
      {
        accessTokenExpiresAt: expirationDate === 0 ? null : accessTokenExpiresAt,
        accessToken: await generateAccessToken(),
        clientId: (await getClient()).id,
        scope: data.scope || '*',
        ...data,
      },
      options,
    )
  }

  async findByUserId(userId, options = {}) {
    return super.findOne({
      where: {
        userId,
        accessTokenExpiresAt: null,
        scope: '*',
      },
      ...options,
    })
  }
}

export default new OAuthAccessTokenResource()
