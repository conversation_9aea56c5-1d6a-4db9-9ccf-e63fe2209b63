import { pick, uniqBy, isString, isEmpty } from 'lodash'
import { Service } from 'typedi'
import sequelize, { Op } from 'sequelize'
import { stringify } from 'csv-stringify'
import moment from 'moment'
import sequelizeDb from '../../core/services/db/sequelize'
import BaseResource, { CREATED, DESTROYED, UPDATED } from './BaseResource'
import { UserInstance } from '../dbSequelize/models/User'
import { PipelineInstance } from '../dbSequelize/models/Pipeline'
import { CardInstance } from '../dbSequelize/models/Card'
import { PipelineStageInstance } from '../dbSequelize/models/PipelineStage'
import { PipelineStageStatusInstance } from '../dbSequelize/models/PipelineStageStatus'
import { PipelineStageReasonInstance } from '../dbSequelize/models/PipelineStageReason'
import { PipelineNotificationsInstance } from '../dbSequelize/models/PipelineNotifications'
import queuedAsyncMap from '../utils/array/queuedAsyncMap'
import pipelineRepository from '../dbSequelize/repositories/pipelineRepository'
import pipelineStageRepository from '../dbSequelize/repositories/pipelineStageRepository'
import pipelineStageStatusRepository from '../dbSequelize/repositories/pipelineStageStatusRepository'
import pipelineAutomationsRepository from '../dbSequelize/repositories/pipelineAutomationsRepository'
import pipelineNotificationsRepository from '../dbSequelize/repositories/pipelineNotificationsRepository'
import cardMovementResource from './cardMovementResource'
import departmentResource from './departmentResource'
import cardResource from './cardResource'
import { Options } from './BaseResource'
import formatDate from '../utils/date/formatDate'
import { PipelineAutomationsInstance } from '../dbSequelize/models/PipelineAutomations'
import BadRequestHttpError from '../../core/utils/error/BadRequestHttpError'
import pipelineStageReasonRepository from '../dbSequelize/repositories/pipelineStageReasonRepository'

@Service()
export class PipelineResource extends BaseResource<PipelineInstance> {
  protected user: UserInstance | null

  constructor() {
    super(pipelineRepository)

    this.events = [CREATED, UPDATED, DESTROYED]
  }

  async validations(data: Partial<PipelineInstance>, id?: string) {
    const otherPipelineWithSameName = await super.findOne({
      where: {
        name: data.name,
        ...(id && { id: { $ne: id } }),
      },
    })

    if (otherPipelineWithSameName) {
      enum Message {
        'pt-BR' = 'Já existe uma funil com esse nome',
        'en-US' = 'There is already a funnel with that name',
        'es' = 'Ya existe un embudo con ese nombre',
      }
      throw new BadRequestHttpError(Message[this.user?.language || 'pt-BR'])
    }

    if (data?.stages?.length > uniqBy(data?.stages, (stage: PipelineStageInstance) => stage?.name).length) {
      enum Message {
        'pt-BR' = 'Existe etapa com o mesmo nome',
        'en-US' = 'There is a stage with the same name',
        'es' = 'Hay un escenario con el mismo nombre.',
      }
      throw new BadRequestHttpError(Message[this.user?.language || 'pt-BR'])
    }

    await queuedAsyncMap(data?.stages ?? [], async (stage: PipelineStageInstance) => {
      if (stage?.statuses?.length > 10) {
        enum Message {
          'pt-BR' = 'Uma etapa não pode ter mais de 10 status',
          'en-US' = 'A stage cannot have more than 10 statuses',
          'es' = 'Una etapa no puede tener más de 10 estados',
        }
        throw new BadRequestHttpError(Message[this.user?.language || 'pt-BR'])
      }

      if (
        stage?.statuses &&
        stage?.statuses?.length > uniqBy(stage?.statuses, (status: PipelineStageStatusInstance) => status.name).length
      ) {
        enum Message {
          'pt-BR' = 'Existe status com o mesmo nome',
          'en-US' = 'There is status with the same name',
          'es' = 'Existe un estado con el mismo nombre',
        }
        throw new BadRequestHttpError(Message[this.user?.language || 'pt-BR'])
      }

      if (stage?.reasons?.length > 10) {
        enum Message {
          'pt-BR' = 'Um status não pode ter mais de 10 motivos',
          'en-US' = 'A status cannot have more than 10 reasons',
          'es' = 'Un estado no puede tener más de 10 razones',
        }
        throw new BadRequestHttpError(Message[this.user?.language || 'pt-BR'])
      }

      if (
        stage?.reasons &&
        stage?.reasons?.length > uniqBy(stage?.reasons, (status: PipelineStageReasonInstance) => status.name).length
      ) {
        enum Message {
          'pt-BR' = 'Existe motivo com esse nome.',
          'en-US' = 'There is a reason with the same name.',
          'es' = 'Existe un motivo con el mismo nombre.',
        }
        throw new BadRequestHttpError(Message[this.user?.language || 'pt-BR'])
      }
    })
  }

  async create(
    data: Partial<PipelineInstance> & {
      user?: UserInstance
      departmentIds?: string[]
      departments?: Array<{ id: string }>
    },
    options?: Options<PipelineInstance>,
  ): Promise<PipelineInstance> {
    return this.nestedTransaction(async (transaction): Promise<PipelineInstance> => {
      await this.validations(data)
      this.user = data?.user

      const instance = await this.getRepository().create(
        {
          ...data,
          accountId: this.user?.accountId,
        },
        {
          ...options,
          transaction,
        },
      )

      const departmentIds = data?.departmentIds || (data?.departments || []).map((d) => d.id)
      const departments = await departmentResource.findManyByIds(departmentIds, {})
      if (departments) {
        await instance.setDepartments(departments, { transaction })
      }

      await queuedAsyncMap(data?.stages ?? [], async (stage: PipelineStageInstance) => {
        await pipelineStageRepository.create(
          {
            ...pick(stage, ['name', 'position', 'statuses', 'reasons']),
            pipelineId: instance.id,
            accountId: data.accountId,
          },
          { transaction },
        )
      })

      if (!data?.stages) {
        const getStep = () => {
          switch (this.user?.language) {
            default:
            case 'pt-BR':
              return 'Etapa'
            case 'en-US':
              return 'Step'
            case 'es':
              return 'Paso'
          }
        }

        const getFinalStep = () => {
          switch (this.user?.language) {
            default:
            case 'pt-BR':
              return 'Finalizados'
            case 'en-US':
              return 'Finalized'
            case 'es':
              return 'Finalizado'
          }
        }

        await pipelineStageRepository.create(
          {
            pipelineId: instance.id,
            name: `${getStep()} 1`,
            position: 1,
            accountId: this.user?.accountId,
          },
          { transaction },
        )
        await pipelineStageRepository.create(
          {
            pipelineId: instance.id,
            name: `${getStep()} 2`,
            position: 2,
            accountId: this.user?.accountId,
          },
          { transaction },
        )
        await pipelineStageRepository.create(
          {
            pipelineId: instance.id,
            name: `${getFinalStep()}`,
            position: 10,
            accountId: this.user?.accountId,
            statuses: [
              {
                name: 'STATUS_WON',
                position: 1,
                accountId: this.user?.accountId,
                pipelineId: instance.id,
              },
              {
                name: 'STATUS_LOOSE',
                position: 2,
                accountId: this.user?.accountId,
                pipelineId: instance.id,
              },
            ],
          },
          { transaction },
        )
      }

      return instance
    }, options.transaction).then(async (instance) => {
      const pipeline = await this.findById(instance?.id)
      if (!options.dontEmit) {
        this.emitCreated(pipeline)
      }

      return pipeline
    })
  }

  async update(
    id: string,
    data: Partial<PipelineInstance> & {
      userId?: string
      departmentIds?: string[]
      departments?: Array<{ id: string }>
      accountId?: string
    },
    options?: Options<PipelineInstance>,
  ): Promise<PipelineInstance> {
    await this.validations(data, id)

    return this.nestedTransaction(async (transaction): Promise<PipelineInstance> => {
      const instance = (await this.getRepository().updateById(id, data, {
        ...options,
        transaction,
      })) as PipelineInstance

      const departmentIds = data.departmentIds || (data.departments || []).map((d) => d.id)
      const departments = await departmentResource.findManyByIds(departmentIds, {})
      if (departments) {
        await instance.setDepartments(departments, { transaction })
      }

      await queuedAsyncMap(data?.stages || [], async (stage: PipelineStageInstance) => {
        if (stage?.new) {
          return pipelineStageRepository.create(
            { ...pick(stage, ['name', 'position']), pipelineId: instance.id, accountId: data.accountId },
            { transaction },
          )
        }

        const oldStage = await pipelineStageRepository.findById(stage.id, { transaction })
        return pipelineStageRepository.update(oldStage, stage, { transaction })
      })

      let stagesToRemove: PipelineStageInstance[]
      const idsStages = (data?.stages?.filter((stage) => !stage?.new) || [])?.map((stage) => stage.id)

      if (idsStages.length) {
        stagesToRemove = await pipelineStageRepository.findMany({
          where: { pipelineId: id, id: { $notIn: idsStages } },
        })
        const cardsToMove = await cardResource.findMany({
          where: { pipelineId: id, pipelineStageId: { $in: stagesToRemove.map((stage) => stage.id) } },
        })

        await queuedAsyncMap(cardsToMove || [], async (card: CardInstance) => {
          await cardResource.updateById(
            card.id,
            { userId: data?.userId, pipelineStageId: idsStages?.[0] },
            { transaction },
          )
        })
      }

      await queuedAsyncMap(stagesToRemove || [], async (stage: PipelineStageInstance) => {
        await pipelineStageRepository.destroy(stage, { transaction })
      })

      return instance
    }, options.transaction).then(async (instance) => {
      const pipeline = await this.findById(instance?.id)
      if (!options.dontEmit) {
        this.emitUpdated(pipeline)
      }

      return pipeline
    })
  }

  getCreatedFilter(query) {
    if (query?.createdFrom && query?.createdUntil) {
      return `AND card."createdAt" between '${moment(query?.createdFrom).format('YYYY-MM-DD')} 00:00:00' AND '${moment(
        query?.createdUntil,
      ).format('YYYY-MM-DD')} 23:59:59'`
    }
    if (query?.createdFrom) {
      return `AND card."createdAt" >= '${moment(query?.createdFrom).format('YYYY-MM-DD')} 00:00:00'`
    }
    if (query?.createdUntil) {
      return `AND card."createdAt" <= '${moment(query?.createdUntil).format('YYYY-MM-DD')} 23:59:59'`
    }
    return ''
  }

  async getQueryTotalCards(query?: {}) {
    const lastMovement =
      '(SELECT "createdAt" from pipeline.card_movements cm WHERE cm."cardId" = card.id ORDER BY "createdAt" DESC LIMIT 1)'
    return `(
      SELECT COUNT(*) as totalCards
      FROM pipeline.cards AS card
      ${
        query?.status
          ? `INNER JOIN pipeline.stage_status AS ss ON ss.id = card."statusId" AND ss.name='${query?.status?.id}'`
          : ''
      }
      WHERE card."pipelineStageId" = "PipelineStage".id
      AND card."isArchived" = false
      AND EXISTS (
        SELECT 1
        FROM contacts AS contact
        WHERE contact.id = card."contactId"
        AND contact."deletedAt" IS NULL
        ${query?.service ? `AND contact."serviceId"='${query?.service?.id}'` : ''}
        ${
          query?.nameTitle
            ? `AND unaccent(COALESCE(contact.name, contact."internalName")) iLike unaccent('%${query?.nameTitle}%')`
            : ''
        }
      )
      ${this.getCreatedFilter(query)}
      ${query?.owner ? `AND card."ownerId" = '${query?.owner?.id}'` : ''}
      ${query?.wonFrom ? `AND ${lastMovement} >= '${moment(query?.wonFrom).format('YYYY-MM-DD')} 00:00:00'` : ''}
      ${query?.wonUntil ? `AND ${lastMovement} <= '${moment(query?.wonUntil).format('YYYY-MM-DD')} 23:59:59'` : ''}
    )`
  }

  async getQueryTotalCardsValue(query?: {}, onlyWon = false, onlyLost = false) {
    const lastMovement =
      '(SELECT "createdAt" from pipeline.card_movements cm WHERE cm."cardId" = card.id ORDER BY "createdAt" DESC LIMIT 1)'
    return `(
      SELECT COALESCE(SUM(pcp.ammount * pcp.value), 0) AS totalValue
      FROM pipeline.card_products AS pcp
      JOIN pipeline.cards AS card
      ON pcp."cardId" = card.id
      ${
        query?.status
          ? `INNER JOIN pipeline.stage_status AS ss ON ss.id = card."statusId" AND ss.name='${query?.status?.id}'`
          : ''
      }
      WHERE card."pipelineStageId" = "PipelineStage".id
      AND card."isArchived" = false
      AND EXISTS (
        SELECT 1
        FROM contacts AS contact
        WHERE contact.id = card."contactId"
        AND contact."deletedAt" IS NULL
        ${query?.service ? `AND contact."serviceId"='${query?.service?.id}'` : ''}
        ${
          query?.nameTitle
            ? `AND unaccent(COALESCE(contact.name, contact."internalName")) iLike unaccent('%${query?.nameTitle}%')`
            : ''
        }
      )
      ${this.getCreatedFilter(query)}
      ${query?.owner ? `AND card."ownerId" = '${query?.owner?.id}'` : ''}
      ${onlyWon ? 'AND card."success" = true' : ''}
      ${onlyLost ? 'AND (card."success" IS NULL OR card."success" = false)' : ''}
      ${query?.wonFrom ? `AND ${lastMovement} >= '${moment(query?.wonFrom).format('YYYY-MM-DD')} 00:00:00'` : ''}
      ${query?.wonUntil ? `AND ${lastMovement} <= '${moment(query?.wonUntil).format('YYYY-MM-DD')} 23:59:59'` : ''}
    )`
  }

  async findById(id: string, query?: {}): Promise<PipelineInstance> {
    await sequelizeDb.query('CREATE EXTENSION IF NOT EXISTS unaccent')
    return super.findOne({
      where: { id },
      include: [
        {
          model: 'stages',
          include: [
            {
              model: 'statuses',
              order: ['position'],
            },
            {
              model: 'reasons',
              order: ['position'],
            },
          ],
          attributes: {
            include: [
              [sequelize.literal(await this.getQueryTotalCards(query)), 'totalCards'],
              [sequelize.literal(await this.getQueryTotalCardsValue(query)), 'totalCardsValue'],
              [sequelize.literal(await this.getQueryTotalCardsValue(query, true)), 'totalWonCardsValue'],
              [sequelize.literal(await this.getQueryTotalCardsValue(query, false, true)), 'totalLostCardsValue'],
            ],
          },
          order: [['position', 'ASC']],
        },
      ],
    })
  }

  async getStageTotals(id: string, query?: {}) {
    return pipelineStageRepository.findOne({
      attributes: {
        include: [
          [sequelize.literal(await this.getQueryTotalCards(query)), 'totalCards'],
          [sequelize.literal(await this.getQueryTotalCardsValue(query)), 'totalCardsValue'],
          [sequelize.literal(await this.getQueryTotalCardsValue(query, true)), 'totalWonCardsValue'],
          [sequelize.literal(await this.getQueryTotalCardsValue(query, false, true)), 'totalLostCardsValue'],
        ],
      },
      where: { id },
    })
  }

  async export(data) {
    const { id, req, res, user } = data
    const { createdAtInit, createdAtFinal, wonAtInit, wonAtFinal } = req?.body ?? {}
    const onlyWon =
      isString(wonAtInit) && !isEmpty(wonAtInit.trim()) && isString(wonAtFinal) && !isEmpty(wonAtFinal.trim())

    const config = await this.getExportLocaleConfig(user, createdAtInit, createdAtFinal)

    const pipeline = await this.fetchPipelineToExport({
      id,
      filters: {
        createdAtInit: createdAtInit,
        createdAtFinal: createdAtFinal,
        wonAtInit: wonAtInit,
        wonAtFinal: wonAtFinal,
        onlyWon: onlyWon,
      },
    })

    const { exportRows, revenue, potentialRevenue, totalPotentialRevenue } = await this.aggregatePipelineDataToExport(
      pipeline,
      user,
      config,
    )

    const conversion = totalPotentialRevenue === 0 ? '' : `${((revenue / totalPotentialRevenue) * 100).toFixed(2)}%`

    const summaryStats = {
      revenue: revenue.toLocaleString(config.currencyLocale, {
        style: 'currency',
        currency: config.currencyCode,
      }),
      potentialRevenue: potentialRevenue.toLocaleString(config.currencyLocale, {
        style: 'currency',
        currency: config.currencyCode,
      }),
      conversion,
    }

    const stringifier = stringify({ delimiter: ';' })
    stringifier.pipe(res)

    stringifier.write([pipeline?.name])
    stringifier.write(config.createPeriod)
    stringifier.write(config.summaryLabels)
    stringifier.write([summaryStats.potentialRevenue, summaryStats.revenue, summaryStats.conversion])

    stringifier.write([pipeline?.name])
    stringifier.write(config.columnLabels)

    exportRows.forEach((row) => stringifier.write(row))

    stringifier.end()
  }

  async getExportLocaleConfig(user, createdAtInit, createdAtFinal) {
    const language = user?.language || 'pt-BR'
    const currencyLocale = language === 'en-US' ? 'en-US' : language === 'es' ? 'es-ES' : 'pt-BR'
    const currencyCode = language === 'en-US' ? 'USD' : 'BRL'

    const languageConfigs = {
      'pt-BR': {
        summaryLabels: ['POTENCIAL DE RECEITA', 'RECEITA GANHA', 'CONVERSÃO GERAL'],
        columnLabels: [
          'OPORTUNIDADE',
          'CONEXÃO',
          'CONTATO',
          'ETAPA',
          'STATUS',
          'MOTIVO',
          'DESCRIÇÃO',
          'VALOR TOTAL DA OPORTUNIDADE',
          'NOME DA EMPRESA',
          'SEGMENTO DA EMPRESA',
          'CANAL DE ORIGEM',
          'CAMPANHA DE ORIGEM',
          'DATA DE CRIAÇÃO DA OPORTUNIDADE',
          'RESPONSÁVEL',
        ],
        createPeriodText: (start, end) => `Período: De ${start}, até ${end}`,
      },
      'en-US': {
        summaryLabels: ['POTENTIAL REVENUE', 'REVENUE EARNED', 'OVERALL CONVERSION'],
        columnLabels: [
          'OPPORTUNITY',
          'CONNECTION',
          'CONTACT',
          'STAGE',
          'STATUS',
          'REASON',
          'DESCRIPTION',
          'TOTAL OPPORTUNITY VALUE',
          'COMPANY NAME',
          'COMPANY SEGMENT',
          'SOURCE CHANNEL',
          'SOURCE CAMPAIGN',
          'OPPORTUNITY CREATION DATE',
          'OWNER',
        ],
        createPeriodText: (start, end) => `Period: From ${start}, to ${end}`,
      },
      es: {
        summaryLabels: ['POTENCIAL DE INGRESOS', 'INGRESOS GANADOS', 'CONVERSIÓN GENERAL'],
        columnLabels: [
          'OPORTUNIDAD',
          'CONEXIÓN',
          'CONTACTO',
          'ETAPA',
          'ESTADO',
          'MOTIVO',
          'DESCRIPCIÓN',
          'VALOR TOTAL DE LA OPORTUNIDAD',
          'NOMBRE DE LA EMPRESA',
          'SEGMENTO DE LA EMPRESA',
          'CANAL DE ORIGEN',
          'CAMPAÑA DE ORIGEN',
          'FECHA DE CREACIÓN DE LA OPORTUNIDAD',
          'RESPONSABLE',
        ],
        createPeriodText: (start, end) => `Período: Desde ${start}, hasta ${end}`,
      },
    }

    const config = languageConfigs[language] || languageConfigs['pt-BR']

    const createdAtInitFormated = formatDate(createdAtInit, 'dd/MM/yyyy', user.account.settings.timezone)
    const createdAtFinalFormated = formatDate(createdAtFinal, 'dd/MM/yyyy', user.account.settings.timezone)

    return {
      language,
      currencyLocale,
      currencyCode,
      summaryLabels: config.summaryLabels,
      columnLabels: config.columnLabels,
      createPeriod: [config.createPeriodText(createdAtInitFormated, createdAtFinalFormated)],
    }
  }

  async fetchPipelineToExport({
    id,
    filters,
  }: {
    id: string
    filters?: {
      createdAtInit?: number
      createdAtFinal?: number
      wonAtInit?: number
      wonAtFinal?: number
      onlyWon?: boolean
    }
  }) {
    return super.findOne({
      where: { id },
      attributes: ['name'],
      include: [
        {
          model: 'stages',
          attributes: ['id', 'name'],
          include: [
            {
              model: 'cards',
              attributes: [
                'id',
                'description',
                'originChannel',
                'originCampaign',
                'success',
                'organization',
                'organizationSegment',
                'createdAt',
              ],
              where: {
                isArchived: false,
                createdAt: {
                  [Op.gte]: new Date(filters.createdAtInit),
                  [Op.lte]: new Date(filters.createdAtFinal),
                },
              },
              include: [
                {
                  model: 'stage_reason',
                  attributes: ['name'],
                },
                {
                  model: 'movements',
                  attributes: ['toPipelineStageId', 'toStageStatusId', 'createdAt'],
                  where: {
                    createdAt: filters.onlyWon
                      ? {
                          [Op.gte]: new Date(filters.wonAtInit),
                          [Op.lte]: new Date(filters.wonAtFinal),
                        }
                      : {
                          [Op.gte]: new Date(filters.createdAtInit),
                          [Op.lte]: new Date(filters.createdAtFinal),
                        },
                  },
                  order: [['createdAt', 'DESC']],
                  limit: 1,
                  include: [
                    'to_pipeline_stage',
                    {
                      model: 'to_stage_status',
                      attributes: ['name'],
                      ...(filters.onlyWon && {
                        where: { name: 'STATUS_WON' },
                      }),
                    },
                  ],
                },
                {
                  model: 'products',
                  attributes: ['value', 'ammount'],
                },
                {
                  model: 'stage_status',
                  attributes: ['name'],
                  ...(filters.onlyWon && {
                    where: { name: 'STATUS_WON' },
                  }),
                },
                {
                  model: 'contact',
                  attributes: ['name', 'data'],
                  required: true,
                  include: [
                    {
                      model: 'service',
                      attributes: ['name'],
                    },
                  ],
                },
                {
                  model: 'owner',
                  attributes: ['name'],
                },
              ],
            },
          ],
        },
      ],
    })
  }

  async aggregatePipelineDataToExport(pipeline, user, config) {
    let potentialRevenue: number = 0
    let totalPotentialRevenue: number = 0
    let revenue: number = 0
    let exportRows: string[][] = []

    await queuedAsyncMap(pipeline?.stages ?? [], async (stage: PipelineStageInstance) => {
      await queuedAsyncMap(stage?.cards ?? [], async (card: CardInstance) => {
        const productSum =
          card.products?.reduce((sum, product) => sum + (product.ammount ?? 0) * (product.value ?? 0), 0) || 0

        totalPotentialRevenue += productSum

        if (!['STATUS_WON', 'STATUS_LOOSE'].includes(card?.stage_status?.name)) {
          potentialRevenue += productSum
        }

        if (card.success) {
          revenue += productSum
        }

        const number = card.contact?.data?.number
        const email = card.contact?.data?.email

        exportRows.push([
          card.contact?.name,
          card.contact?.service?.name,
          `${number ? number : ''}${number && email ? ', ' : ''}${email ? email : ''}`,
          stage.name,
          card.stage_status?.name,
          card.stage_reason?.name,
          (card.description || '').split('\n').join('|||'),
          card.products
            ?.reduce((sum, product) => sum + product.ammount * product.value, 0)
            .toLocaleString(config.currencyLocale, {
              style: 'currency',
              currency: config.currencyCode,
            }),
          card.organization,
          card.organizationSegment,
          card.originChannel,
          card.originCampaign,
          formatDate(card.createdAt, 'dd/MM/yyyy HH:mm:ss', user.account.settings.timezone),
          card.owner?.name || '',
        ])
      })
    })

    return { exportRows, revenue, potentialRevenue, totalPotentialRevenue }
  }

  async createAutomation(data: PipelineAutomationsInstance): Promise<PipelineAutomationsInstance> {
    if (!data || !data.pipelineId) {
      throw new Error('Automation data is invalid')
    }
    const automation = await pipelineAutomationsRepository.create({ ...data })
    const pipeline = await this.findById(automation?.pipelineId)
    this.emit(UPDATED, pipeline)
    return automation
  }

  async updateAutomation(id: string, data: PipelineAutomationsInstance): Promise<PipelineAutomationsInstance> {
    let automation = await pipelineAutomationsRepository.findById(id)
    if (!automation) {
      throw new Error('Automation not found')
    }
    automation = await pipelineAutomationsRepository.update(automation, data)
    const pipeline = await this.findById(automation?.pipelineId)
    this.emit(UPDATED, pipeline)
    return automation
  }

  async getAutomation(pipelineId: string): Promise<PipelineAutomationsInstance> {
    const automation = await pipelineAutomationsRepository.findOne({ where: { pipelineId: pipelineId } })
    return automation || []
  }

  async destroyAutomation(id: string) {
    const automation = await pipelineAutomationsRepository.findById(id, {})
    if (!automation) {
      throw new Error('Automations not found')
    }
    await pipelineAutomationsRepository.destroy(automation, {})
  }

  async createNotification(notification: {
    pipelineId: string
    pipelineAutomationId: string
  }): Promise<PipelineNotificationsInstance> {
    return pipelineNotificationsRepository.create(notification)
  }

  async createStage(
    data: Partial<PipelineStageInstance> & { pipelineId: string },
    user: UserInstance,
  ): Promise<PipelineStageInstance | string> {
    let pipeline = await this.findById(data?.pipelineId)
    if (pipeline.stages.length >= 10) {
      return 'STAGES_LIMIT'
    }
    const stageFound = pipeline?.stages.find((stage) => stage.name === data?.name)
    if (stageFound) {
      return 'STAGE_ALREADY_EXIST'
    }
    const stage = await pipelineStageRepository.create({ ...data, accountId: user?.accountId })

    // refresh pipeline
    pipeline = await this.findById(pipeline.id)
    this.emitUpdated(pipeline)

    return stage
  }

  async updateStage(id: string, data: Partial<PipelineStageInstance>): Promise<PipelineStageInstance | string> {
    let pipeline = await this.findById(data?.pipelineId)
    const stageFound = pipeline?.stages.find((stage) => stage.name === data?.name && stage.id !== data?.id)
    if (stageFound) {
      return 'STAGE_ALREADY_EXIST'
    }
    const stage = await pipelineStageRepository.findById(id)

    const updatedStage = await pipelineStageRepository.update(stage, data)

    // refresh pipeline
    pipeline = await this.findById(pipeline.id)
    this.emitUpdated(pipeline)

    return updatedStage
  }

  async destroyStage(
    id: string,
    data: { typeAction: string; idNewStage: string; user: UserInstance },
  ): Promise<string> {
    const stage = await pipelineStageRepository.findById(id, {
      include: [
        {
          model: 'pipeline',
          include: ['stages'],
        },
        'cards',
      ],
    })

    if (stage?.pipeline?.stages?.length === 2) {
      return 'PIPELINES_MIN_STAGES'
    }

    if (data?.typeAction == 'move') {
      const cardsBefore = (stage?.cards || []) as CardInstance[]

      await cardResource.bulkUpdate(
        {
          pipelineStageId: data?.idNewStage,
          statusId: null,
          success: null,
          finishedAt: null,
          reasonId: null,
        },
        {
          where: {
            pipelineStageId: id,
          },
        },
      )

      if (cardsBefore.length) {
        await queuedAsyncMap(cardsBefore ?? [], async (card: CardInstance) => {
          await cardMovementResource.move(card, {
            ...card,
            pipelineStageId: data?.idNewStage,
            userId: data?.user?.id,
          })
        })
      }
    } else {
      const firstStage = stage?.pipeline?.stages?.find((s: PipelineStageInstance) => s?.position === 1)
      await cardResource.bulkUpdate(
        {
          pipelineStageId: firstStage?.id,
          isArchived: true,
          archivedAt: new Date(),
        },
        {
          where: {
            pipelineStageId: id,
          },
        },
      )
    }

    await pipelineStageRepository.destroy(stage, {})

    this.emitUpdated(stage?.pipeline)

    return 'MESSAGE_STAGE_DELETED'
  }

  async getStatuses(pipelineId: string): Promise<PipelineStageStatusInstance[]> {
    return pipelineStageStatusRepository.findMany({
      attributes: ['name'],
      group: ['name'],
      where: { pipelineId },
    })
  }

  async archive(
    id: string,
    data: { archive: boolean },
    options?: Options<PipelineInstance>,
  ): Promise<PipelineInstance> {
    const instance = await this.findById(id)

    const archivedAt = data.archive ? new Date() : null

    await super.update(
      instance,
      { archivedAt },
      {
        ...options,
      },
    )

    return instance
  }

  async destroyStatus(
    id: string,
    data: { action: string; newStatusId: string; newStatusName: string },
  ): Promise<string> {
    const status = await pipelineStageStatusRepository.findById(id)

    if (data?.action && ['keep-without-status', 'move-to-another'].includes(data.action)) {
      await pipelineStageStatusRepository.destroy(status, {})

      await cardResource.bulkUpdate(
        {
          statusId: data.action == 'keep-without-status' ? null : data.newStatusId ?? null,
        },
        {
          where: {
            statusId: id,
          },
        },
      )

      return 'MESSAGE_STATUS_DELETED'
    } else {
      if (!data?.newStatusName) {
        throw new Error('ERROR_NO_NEW_STATUS_NAME')
      }

      await pipelineStageStatusRepository.update(status, { name: data?.newStatusName }, { where: { id: id } })

      return 'MESSAGE_STATUS_RENAMED'
    }
  }

  async findStatusById(id: string): Promise<PipelineInstance> {
    const status = await pipelineStageStatusRepository.findById(id)

    const cardsCount = await cardResource.count({
      where: { statusId: id },
    })

    return {
      ...status?.dataValues,
      cardsCount,
    }
  }

  async findManyWithTotals(query?: Options<PipelineInstance>) {
    let cardDateFilter = {}

    if (Array.isArray(query.where?.$and)) {
      query.where.$and = query.where.$and.filter((cond) => {
        if (cond.createdAt) {
          cardDateFilter = { ...cardDateFilter, ...cond.createdAt }
          return false
        }
        return true
      })
      if (query.where.$and.length === 0) delete query.where.$and
    }

    query.include = [
      {
        model: 'stages',
        required: true,
        include: [
          'statuses',
          {
            model: 'cards',
            required: Object.keys(cardDateFilter).length ? true : false,
            where: {
              ...(Object.keys(cardDateFilter).length ? { createdAt: cardDateFilter } : {}),
              isArchived: false,
            },
            attributes: [],
          },
        ],
      },
    ]

    const pipelines = await super.findMany(query)
    const response = []

    await queuedAsyncMap(pipelines ?? [], async (pipeline: PipelineInstance) => {
      response.push({ ...pipeline?.dataValues, totals: await pipelineRepository.getTotals(pipeline, cardDateFilter) })
    })

    return { data: response }
  }

  async duplicate(id: string, options) {
    const pipeline = await super.findById(id, {
      include: ['stages', 'stages.statuses', 'stages.reasons', 'automations'],
    })
    if (!pipeline) {
      enum Message {
        'pt-BR' = 'Pipeline não encontrada!',
        'en-US' = 'Pipeline not found!',
        'es' = '¡Pipeline no encontrado!',
      }
      throw new BadRequestHttpError(Message[this.user?.language || 'pt-BR'])
    }
    const newPipeline = await this.nestedTransaction(async (transaction): Promise<PipelineInstance> => {
      const instance = await super.create(
        {
          name: `${pipeline.name} (copy)`,
          goBack: pipeline.goBack,
          accountId: pipeline.accountId,
          archivedAt: null,
        },
        {
          ...options,
          dontEmit: true,
          transaction,
        },
      )

      await queuedAsyncMap(pipeline.stages, async (stage: PipelineStageInstance) => {
        const newStage = await pipelineStageRepository.create(
          {
            name: stage.name,
            position: stage.position,
            pipelineId: instance.id,
            accountId: pipeline.accountId,
          } as PipelineStageInstance,
          { transaction },
        )
        await queuedAsyncMap(stage.statuses || [], async (status: PipelineStageStatusInstance) => {
          await pipelineStageStatusRepository.create(
            {
              name: status.name,
              position: status.position,
              pipelineId: instance.id,
              stageId: newStage.id,
              accountId: instance.accountId,
            } as PipelineStageStatusInstance,
            { transaction },
          )
        })
        await queuedAsyncMap(stage.reasons || [], async (reason: PipelineStageReasonInstance) => {
          await pipelineStageReasonRepository.create(
            {
              name: reason.name,
              position: reason.position,
              pipelineId: instance.id,
              stageId: newStage.id,
              accountId: instance.accountId,
              isWon: reason.isWon,
            } as PipelineStageReasonInstance,
            { transaction },
          )
        })
      })

      await queuedAsyncMap(pipeline.automations, async (automation: PipelineAutomationsInstance) => {
        await pipelineAutomationsRepository.create(
          {
            pipelineId: instance.id,
            type: automation.type,
            isArchived: automation.isArchived,
            archivedAt: automation.archivedAt,
            config: automation.config,
          },
          { transaction },
        )
      })
      return instance
    }, options.transaction)
    this.emitCreated(newPipeline)
    return newPipeline
  }
}

export default new PipelineResource()
