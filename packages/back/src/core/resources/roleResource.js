import { omit, pick, chunk } from 'lodash'
import { literal } from 'sequelize'
import BaseResource from './BaseResource'
import roleRepository from '../dbSequelize/repositories/roleRepository'
import userResource from './userResource'
import queuedAsyncMap from '../utils/array/queuedAsyncMap'
import iteratePaginated from '../utils/iteratePaginated'
export class RoleResource extends BaseResource {
  constructor() {
    super(roleRepository)
  }

  async findManyPaginated(query = {}) {
    const includeUsersCount = query?.customInclude?.includes('usersCount')

    const options = {
      ...query,
      ...(includeUsersCount && { subQuery: false }),
      attributes: {
        ...query.attributes,
        ...(includeUsersCount && {
          include: [
            [
              literal(`(
          SELECT COUNT(ud."userId")
          FROM user_roles ud
          WHERE ud."roleId" = "Role"."id"
        )`),
              'usersCount',
            ],
          ],
        }),
      },
      ...(Array.isArray(query.order) && {
        order: query.order.map(([association, direction]) => {
          if (association === 'usersCount' && includeUsersCount) {
            return [literal('"usersCount"'), direction]
          }
          return [association, direction]
        }),
      }),
    }

    const result = await super.getRepository().findManyPaginated(options)

    return result
  }

  async findById(id, options = {}) {
    const result = await super.findById(id, {
      ...options,
      attributes: {
        ...options.attributes,
        include: [
          [
            literal(`(
              SELECT COUNT(ud."userId")
              FROM user_roles ud
              WHERE ud."roleId" = "Role"."id"
            )`),
            'usersCount',
          ],
        ],
      },
    })

    return result
  }

  async create(data, options = {}) {
    const roleData = {
      ...pick(data, ['displayName', 'isAdmin', 'serviceId', 'accountId']),
    }

    let role = await super.create(roleData, {
      ...options,
      dontEmit: true,
    })

    if (data?.permissions?.length) {
      await role.setPermissions(data.permissions)
    }

    if (!options.dontEmit) this.emitCreated(role)

    if (options.include) {
      role = await super.reload(role, options)
    }

    return role
  }

  async updateById(id, data, options = {}) {
    let updatedRole = null
    await this.getRepository().transaction(async (transaction) => {
      const roleData = omit(data, ['permissions'])
      updatedRole = await super.updateById(id, roleData, {
        ...options,
        transaction,
      })

      if (data?.permissions?.length) {
        await updatedRole.setPermissions(data.permissions, { transaction })
      }

      if (!options?.dontEmit) this.emitUpdated(updatedRole)

      if (options?.include) {
        updatedRole = await super.reload(updatedRole, {
          ...options,
          transaction,
        })
      }
    })

    await roleRepository.getCached().forgetCacheListFromData(updatedRole)

    const users = await iteratePaginated(
      ({ page }) =>
        userResource.findManyPaginated({
          page,
          perPage: 100,
          include: [
            {
              model: 'roles',
              as: 'roles',
              where: { id },
              include: ['permissions'],
              required: true,
            },
          ],
        }),
      async (user) => user,
      10,
      true,
    )

    await queuedAsyncMap(chunk(users, 30), async (items) => {
      userResource.emitUpdated(items)
    })

    return updatedRole
  }

  findOneByNameAndAccountId(...props) {
    return this.getRepository().findOneByNameAndAccountId(...props)
  }
}

export default new RoleResource()
