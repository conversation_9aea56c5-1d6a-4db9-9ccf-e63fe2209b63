import { subMinutes, addMinutes } from 'date-fns'
import scheduleRepository from '../dbSequelize/repositories/scheduleRepository'
import contactResource from './contactResource'
import BaseResource from './BaseResource'
import fileResource from './fileResource'
import HttpError from '../utils/error/HttpError'
import iteratePaginated from '../utils/iteratePaginated'
import whatsappBusinessTemplateResource from './whatsappBusinessTemplateResource'
import { validateResourceExists } from '../../core/utils/resource/validateResourceExist'

export class ScheduleResource extends BaseResource {
  constructor() {
    super(scheduleRepository)
  }

  async checkScheduleTime(accountId, scheduledAt, serviceId) {
    return this.count({
      where: {
        accountId,
        scheduledAt: {
          $between: [subMinutes(new Date(scheduledAt), 5), addMinutes(new Date(scheduledAt), 5)],
        },
      },
      include: [
        {
          model: 'contact',
          where: { serviceId },
          required: true,
        },
      ],
    })
  }

  async create(data, options = {}) {
    const { accountId, scheduledAt, hsmId, hsmFileId, extraOptions } = data

    const contact = await contactResource.findById(data.contactId, {
      attributes: ['serviceId'],
      where: {
        accountId,
      },
    })

    const countScheduleTime = await this.checkScheduleTime(accountId, scheduledAt, contact.serviceId)

    if (countScheduleTime) {
      throw new HttpError(402, 'Schedule time is already exist.')
    }

    await Promise.all([
      hsmId && validateResourceExists(whatsappBusinessTemplateResource, hsmId, 'Template ID'),
      hsmFileId && validateResourceExists(fileResource, hsmFileId, 'File ID'),
    ])

    let hsmMessage
    if (hsmId && extraOptions) {
      const findHsm = await whatsappBusinessTemplateResource.findById(hsmId, { attributes: ['components'] })

      const headerText =
        findHsm.components.find((component) => component.type === 'HEADER' && 'text' in component)?.text ?? ''
      const bodyText =
        findHsm.components.find((component) => component.type === 'BODY' && 'text' in component)?.text ?? ''

      hsmMessage = [headerText, bodyText].filter((text) => text).join('\n')
    }

    const schedule = await scheduleRepository.create({ ...data, ...(hsmMessage && { message: hsmMessage }) })

    if (data.files && data.files.length !== 0) {
      const uploadMedia = async (data) => {
        const { base64 } = data
        return Buffer.from(base64, 'base64')
      }

      for await (const file of data.files) {
        const uploadedMedia = await uploadMedia(file)

        await fileResource.create({
          name: file.name,
          data: uploadedMedia,
          mimetype: file.mimetype,
          attachedId: schedule.id,
          attachedType: 'schedule.files',
          accountId,
          serviceId: contact.serviceId,
          isPtt: true,
        })
      }
    }

    if (!options.dontEmit) this.emitCreated(schedule)

    return schedule
  }

  async findManyPaginated(query) {
    if (query.where?.serviceId && !query.where?.contactId) {
      const contactIds = await iteratePaginated(
        ({ page }) =>
          contactResource.findManyPaginated({
            page,
            perPage: 100,
            where: {
              serviceId: query.where.serviceId,
              accountId: query.where.accountId,
            },
          }),
        async (contact) => contact.id,
        100,
        true,
      )

      query.where = { ...query.where, contactId: { $in: contactIds } }
    }

    delete query.where.serviceId

    return this.getRepository().findManyPaginated(query)
  }
}
export default new ScheduleResource()
