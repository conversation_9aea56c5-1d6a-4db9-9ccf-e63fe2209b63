import BaseResource, { EventTransaction, PaginatedOptions } from './BaseResource'
import serviceRepository from '../dbSequelize/repositories/serviceRepository'
import ServicesHistoryRepository from '../dbSequelize/repositories/ServicesHistoryRepository'
import driverService from '../services/driverService'
import { ServiceInstance } from '../dbSequelize/models/Service'
import { AccountInstance } from '../dbSequelize/models/Account'
import contactResource from './contactResource'
import messageResource from './messageResource'
import scheduleResource from './scheduleResource'
import campaignsResource from './campaignResource'
import customFieldsResource from './customFieldsResource'
import fileResource from './fileResource'
import {
  getWebhookInGatewayByUrl,
  deleteWebhookInGateway,
} from '../../microServices/workers/jobs/facebookMessenger/driver/baseFunctions'
import config from '../config'
import iteratePaginated from '../utils/iteratePaginated'
import serverPodResource from './serverPodResource'
import reportError from '../services/logs/reportError'
import fileTransformer from '../transformers/fileTransformer'
import { serviceTypes } from '../../microServices/workers/jobs/account/updateContractPlan/Types'
import configValues from '../configValues'
import Container from 'typedi'
import HttpJobsDispatcher from '../services/jobs/http/HttpJobsDispatcher'

export const TYPES = [
  'whatsapp',
  'whatsapp-business',
  'whatsapp-remote-pod',
  'telegram',
  'sms-wavy',
  'webchat',
  'email',
  'facebook-messenger',
  'instagram',
  'google-business-message',
  'reclame-aqui',
]

export enum ServiceType {
  WHATSAPP = 'whatsapp',
  WHATSAPP_BUSINESS = 'whatsapp-business',
  WHATSAPP_REMOTE_POD = 'whatsapp-remote-pod',
  TELEGRAM = 'telegram',
  SMS = 'sms-wavy',
  WEBCHAT = 'webchat',
  EMAIL = 'email',
  FACEBOOK = 'facebook-messenger',
  INSTAGRAM = 'instagram',
  GOOGLE = 'google-business-message',
  RECLAME_AQUI = 'reclame-aqui',
}

export const ARCHIVED = 'archived'

// Conexão de facebook pode receber reação, mas não pode enviar
export const serviceTypesAllowedToSendReactions = ['whatsapp', 'instagram', 'whatsapp-business', 'telegram']
export const serviceTypesEnabledReactions = [
  'whatsapp',
  'facebook-messenger',
  'instagram',
  'whatsapp-business',
  'telegram',
]
export const serviceProviderTypeEnabledReactions = ['gupshup', '360Dialog', 'meta']
export const serviceProviderTypeBlockReactionOwnMessage = ['gupshup']

export class ServiceResource extends BaseResource<ServiceInstance> {
  constructor() {
    super(serviceRepository)
    this.events = [...super.getEvents(), ARCHIVED]
  }

  emitArchived(data, evenTransaction?: EventTransaction) {
    return this.emit(ARCHIVED, data, evenTransaction)
  }

  onArchived(listener) {
    return this.on(ARCHIVED, listener)
  }

  async update(model, data, options = {}) {
    if (
      (['facebook-messenger', 'instagram'].includes(model.type) &&
        data.data?.driverId &&
        data.data.pageId &&
        !!(await this.findOne({
          where: {
            id: { $ne: model.id },
            'data.driverId': data.data.driverId,
            'data.pageId': data.data.pageId,
            archivedAt: { $eq: null },
            type: model.type,
          },
        }))) ||
      (model.data.providerType === 'meta' &&
        data.data?.driverId &&
        data.data.numberId &&
        data.data.businessId &&
        !!(await this.findOne({
          where: {
            id: { $ne: model.id },
            'data.providerType': model.data.providerType,
            'data.driverId': data.data.driverId,
            archivedAt: { $eq: null },
            type: model.type,
          },
        })))
    ) {
      throw new Error(`Service already exists with driverId ${data.data.driverId}`)
    }

    if (data.type && data.type !== model.type && model.data.status.isConnected) {
      if (configValues.workersGoServices.includes(model.type)) {
        await Container.get(HttpJobsDispatcher).dispatch(
          'shutdown',
          { serviceId: model.id },
          {
            timeout: 2 * 60_000,
            useWorkersGo: true,
          },
        )
      } else {
        await driverService.shutdown(model.id)
      }
    }

    const mergedData = {
      ...model.dataValues,
      ...data,
      data: {
        ...model.dataValues.data,
        ...data.data,
        webchat: { ...model?.dataValues?.data?.webchat, ...data?.data?.webchat },
      },
      settings: {
        ...model.dataValues.settings,
        ...data.settings,
      },
    }

    await this.createHistory(model, mergedData)

    return super.update(model, mergedData, options)
  }

  async createHistory(oldData, newData) {
    if (newData?.health && oldData?.health !== newData?.health) {
      await ServicesHistoryRepository.create({
        serviceId: oldData?.id,
        ...(newData?.health && { healthFrom: JSON.stringify(oldData?.health) }),
        ...(newData?.health && { healthTo: JSON.stringify(newData?.health) }),
      })
    }
  }

  destroy(model, options = {}) {
    options.dontEmit = true
    return this.getRepository().transaction(async (transaction) => {
      await Promise.all([
        contactResource.bulkDestroy({
          ...options,
          where: { serviceId: model.id },
          transaction,
        }),
        messageResource.bulkDestroy({
          ...options,
          where: { serviceId: model.id },
          transaction,
        }),
      ])

      return super.destroy(model, {
        ...options,
        transaction,
        dontEmit: false,
      })
    })
  }

  archive = async (id, byUserId, archive) => {
    const service = await super.findById(id)

    if (!service) {
      throw new Error(`No service found with given serviceId: ${id}.`)
    }
    if (archive && service.archivedAt) {
      throw new Error(`Service ${id} already archived.`)
    }

    const { isConnected, isStarted, isStarting } = service.data.status
    if (archive && (isConnected || isStarted || isStarting)) {
      if (configValues.workersGoServices.includes(service.type)) {
        await Container.get(HttpJobsDispatcher)
          .dispatch(
            'shutdown',
            { serviceId: id },
            {
              timeout: 2 * 60_000,
              useWorkersGo: true,
            },
          )
          .catch(reportError)
      } else {
        await driverService.shutdown(id).catch(reportError)
      }
    }

    const archivedAt = archive ? new Date() : null
    const isArchived = !!archive
    const status = { isConnected: false, isStarting: false, isStarted: false }

    const data = {
      byUserId, // Qual usuário está fechando o chamado
      /*  ticketTopicIds, // asunto(não precisa)
    comments, // comentário(não precisa */
    }

    await this.eventTransaction(async (eventTransaction) => {
      await iteratePaginated(
        ({ page }) =>
          contactResource.findManyPaginated({
            page,
            perPage: 100,
            include: [
              {
                model: 'currentTicket',
                include: [
                  {
                    model: 'currentTicketTransfer',
                    required: true,
                  },
                ],
                where: {
                  isOpen: true,
                },
                required: true,
              },
            ],
            where: {
              serviceId: id,
            },
            noAccountId: true,
          }),
        async (contact) => contactResource.closeTicket({ contact, ...data }, { eventTransaction }),
      )
    })

    await this.eventTransaction((eventTransaction) =>
      iteratePaginated(
        ({ page }) =>
          scheduleResource.findManyPaginated({
            page,
            perPage: 100,
            where: {
              status: 'scheduled',
            },
            include: [
              {
                attributes: [],
                model: 'contact',
                where: {
                  serviceId: id,
                },
                required: true,
              },
            ],
            noAccountId: true,
          }),
        async (schedule) => scheduleResource.update(schedule, { status: 'done' }, { eventTransaction }),
      ),
    )

    await this.eventTransaction((eventTransaction) =>
      iteratePaginated(
        ({ page }) =>
          campaignsResource.findManyPaginatedBase({
            page,
            perPage: 100,
            where: {
              finishedAt: null,
            },
            include: [
              {
                attributes: [],
                model: 'service',
                where: {
                  id,
                },
                required: true,
              },
            ],
            noAccountId: true,
          }),
        async (campaign) =>
          campaignsResource.internalUpdate(
            campaign,
            {
              status: 'canceled',
            },
            { eventTransaction },
          ),
      ),
    )

    if (['facebook-messenger', 'instagram'].includes(service?.type) || service.data.providerType === 'meta') {
      const url = `${config('publicUrl')}/messenger-webhook/${service.id}`

      const webhook = await getWebhookInGatewayByUrl(url)
      if (webhook) {
        await deleteWebhookInGateway(webhook.id)
      }
    }

    const updatedService = await this.update(service, {
      archivedAt,
      isArchived,
      data: {
        ...service.data,
        status: { ...service.data.status, ...status },
      },
    })

    this.emitArchived(updatedService)

    return true
  }

  async destroyById(id, options = {}) {
    const service = await super.findById(id)

    if (service?.botId) {
      await super.updateById(id, {
        botId: null,
      })
    }

    await service.setWebhooks([])

    return this.getRepository()
      .findById(id, options)
      .then((model) => this.destroy(model, options))
  }

  async getServices(account: Partial<AccountInstance>, isAmounts: boolean) {
    if (!isAmounts) {
      return {}
    }

    const services = await this.getRepository().findMany({
      attributes: ['id', 'type'],
      where: { accountId: account.id, archivedAt: null },
    })

    const consumed = services.reduce(
      (acc, service) => {
        acc.totalConsumed += 1

        acc.typesConsumed[service.type] = acc.typesConsumed[service.type] ? acc.typesConsumed[service.type] + 1 : 1

        return acc
      },
      {
        totalConsumed: 0,
        typesConsumed: {},
      },
    )

    const accountPlanBase = account.plan?.planBaseServices || {}
    const accountPlan = account.plan?.services || {}

    const result = serviceTypes.reduce(
      (acc, serviceType) => {
        const servicePlanBase = accountPlanBase[serviceType] || 0
        const servicePlan = accountPlan[serviceType] || 0
        const serviceConsumed = consumed.typesConsumed[serviceType] || 0

        acc.planBase += servicePlanBase
        acc.totalContracted += servicePlan

        // Prioriza o consumo do plano base e depois subtrai para ver o consumo do adicional
        const planBaseConsumed = serviceConsumed < servicePlanBase ? serviceConsumed : servicePlanBase
        const additionalConsumed = serviceConsumed - planBaseConsumed

        const additional = servicePlan - servicePlanBase

        acc.planBaseConsumed += planBaseConsumed
        acc.additionalConsumed += additionalConsumed

        acc.serviceTypes.push({
          serviceType,
          planBase: servicePlanBase,
          planBaseConsumed,
          planBaseAvailable: servicePlanBase - planBaseConsumed,
          additional,
          additionalConsumed,
          additionalAvailable: additional - additionalConsumed,
          totalConsumed: serviceConsumed,
        })

        return acc
      },
      {
        planBase: 0,
        planBaseConsumed: 0,
        additionalConsumed: 0,
        totalContracted: 0,
        serviceTypes: [],
      },
    )

    const additional = result.totalContracted - result.planBase

    const servicesAmounts = {
      planBase: result.planBase,
      planBaseConsumed: result.planBaseConsumed,
      planBaseAvailable: result.planBase - result.planBaseConsumed,
      additional,
      additionalConsumed: result.additionalConsumed,
      additionalAvailable: additional - result.additionalConsumed,
      totalContracted: result.totalContracted,
      totalConsumed: consumed.totalConsumed,
      totalAvailable: result.totalContracted - consumed.totalConsumed,
      serviceTypes: result.serviceTypes,
    }

    return { servicesAmounts }
  }

  async getServiceWithServerPodById(serviceId: string, withServerPod = true) {
    const service = await this.findById(serviceId, {
      include: [
        {
          model: 'account',
          attributes: ['id', 'settings'],
        },
      ],
    })

    if (!service) {
      throw new Error(`No service found with given serviceId: ${serviceId}.`)
    }

    if (service.archivedAt) {
      throw new Error(`Service ${serviceId} is archived.`)
    }

    if (withServerPod) {
      const serviceIdSecurityTokenString = `${service.id}:${service.internalData.securityToken}`
      const serverPod = await serverPodResource.getCurrentServerPod(serviceIdSecurityTokenString)

      if (!serverPod) {
        throw new Error(`No serverPod set for: ${serviceIdSecurityTokenString}.`)
      }

      service.serverPod = serverPod
    }

    return service
  }

  async findById(id: string, query = {}): Promise<ServiceInstance> {
    const service = await super.findById(id, query)
    if (service && service?.type === 'webchat') {
      if (service?.data?.webchat?.customField?.id) {
        service.customField = await customFieldsResource.findById(service?.data?.webchat?.customField?.id)
      }
      if (service?.data?.webchat?.custom?.logo) {
        const logo = await fileResource.findById(service?.data?.webchat?.custom?.logo)
        service.logo = await fileTransformer(logo)
      }
      if (service?.data?.webchat?.custom?.webchatIcon) {
        const webchatIcon = await fileResource.findById(service?.data?.webchat?.custom?.webchatIcon)
        service.webchatIcon = await fileTransformer(webchatIcon)
      }
      if (service?.data?.webchat?.custom?.profilePhoto) {
        const profilePhoto = await fileResource.findById(service?.data?.webchat?.custom?.profilePhoto)
        service.profilePhoto = await fileTransformer(profilePhoto)
      }
    }
    return service
  }
}

export default new ServiceResource()
