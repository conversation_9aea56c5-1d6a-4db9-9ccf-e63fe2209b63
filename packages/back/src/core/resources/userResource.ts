import { omit, pick, flattenDeep, uniqBy, chunk } from 'lodash'
import { randomBytes } from 'crypto'
import { Container } from 'typedi'
import { Sequelize } from 'sequelize'
import Cryptr from 'cryptr'
import validator from 'validator'
import sequelize from '../services/db/sequelize'
import BaseResource, { EventTransaction } from './BaseResource'
import userRepository from '../dbSequelize/repositories/userRepository'
import oAuthAccessTokenResource from './oAuthAccessTokenResource'
import oAuthRefreshTokenResource from './oAuthRefreshTokenResource'
import webhookResource from './webhookResource'
import contactResource from './contactResource'
import accountResource from './accountResource'
import roleRepository from '../dbSequelize/repositories/roleRepository'
import hasher from '../utils/crypt/hasher'
import { DistributionInstance } from '../dbSequelize/models/Distribution'
import { RoleInstance } from '../dbSequelize/models/Role'
import { UserInstance } from '../dbSequelize/models/User'
import configValues from '../configValues'
import iteratePaginated from '../utils/iteratePaginated'
import internalChatService from '../services/internalChat'
import { AccountInstance } from '../dbSequelize/models/Account'
import { encryptTextForAccount } from '../services/crypt/accountCryptor'
import queuedAsyncMap from '../utils/array/queuedAsyncMap'
import { Options } from '../resources/BaseResource'
import RedisCacheStorage from '../../core/services/cache/RedisCacheStorage'
import ticketResource from './ticketResource'
import { ContactInstance } from '../../core/dbSequelize/models/Contact'
import scheduleResource from './scheduleResource'
import { ScheduleInstance } from '../../core/dbSequelize/models/Schedule'
import redisTaskQueue from '../services/queue/redisTaskQueue'
import Logger from '../../core/services/logs/Logger'
import reportError from '../../core/services/logs/reportError'
const logger = Container.get(Logger)

export const TAKEOVER = 'takeover'
export const LOGOUT = 'logout'
export const LOGIN = 'login'
export const USER_TRANSFER = 'user_transfer'
export const ARCHIVED = 'archived'

export class UserResource extends BaseResource<UserInstance> {
  constructor() {
    super(userRepository)
    this.events = [...super.getEvents(), TAKEOVER, LOGOUT, LOGIN, USER_TRANSFER, ARCHIVED]
  }

  onTakeover(listener) {
    return this.on(TAKEOVER, listener)
  }

  emitTakeover(data, evenTransaction?: EventTransaction) {
    return this.emit(TAKEOVER, data, evenTransaction)
  }

  emitLogout(data, evenTransaction?: EventTransaction) {
    return this.emit(LOGOUT, data, evenTransaction)
  }

  onLogout(listener) {
    return this.on(LOGOUT, listener)
  }

  emitLogin(data, evenTransaction?: EventTransaction) {
    return this.emit(LOGIN, data, evenTransaction)
  }

  onLogin(listener) {
    return this.on(LOGIN, listener)
  }

  emitUserTransfer(data, evenTransaction?: EventTransaction) {
    return this.emit(USER_TRANSFER, data, evenTransaction)
  }

  onUserTransfer(listener) {
    return this.on(USER_TRANSFER, listener)
  }

  emitArchived(data, evenTransaction?: EventTransaction) {
    return this.emit(ARCHIVED, data, evenTransaction)
  }

  onArchived(listener) {
    return this.on(ARCHIVED, listener)
  }

  async create(data, options = {}) {
    if (data.isClientUser) {
      data = {
        ...data,
        roles: [],
        departments: [],
        timetable: [],
      }
    }

    const attributes = omit(
      {
        active: false,
        ...data,
        activationToken: randomBytes(32).toString('base64'),
        password: data.hashedPassword || (await hasher.hash(data.password)),
        isSuperAdmin: !!data.isSuperAdmin,
        isClientUser: !!data.isClientUser,
        passwordExpiresAt: await accountResource.generateNewExpirationDate(
          data.account || data.accountId,
          options,
          true,
        ),
      },
      ['account', 'roles', 'departments', 'timetable'],
    )

    const user = await userRepository.create(attributes, options)

    if (data.account) await user.setAccount(data.account, options)
    if (data.roles) await user.setRoles(data.roles, options)
    if (data.departments) await user.setDepartments(data.departments, options)

    await this.generateTokenFromInternalChat({ user, roles: data.roles }, options)

    if ((data?.account?.settings || {}).hasBeenSynchronizedWithInternalChat) {
      await internalChatService.createUserFromInternalChat({
        userId: user.id,
        roles: data.roles,
        accountId: data.account.id,
      })
    }

    if (data.organizationIds) {
      await user.setOrganizations(data.organizationIds)
    }

    // @ts-ignore
    this.emitCreated(user, options.eventTransaction)

    logger.logEvent('user_created', { userId: user.id, name: user.name })

    return user
  }

  async registerToken(model: UserInstance, data: UserInstance, options = {}) {
    if (data.isClientUser) {
      return
    }

    const attributes = {
      data: pick(data.data, ['oneSignalToken', 'expoPushToken', 'fromApp']),
    }

    const user = await userRepository.update(model, attributes, options)

    // @ts-ignore
    this.emitUpdated(user, options.eventTransaction)

    return user
  }

  async update(model, data, options = {}): Promise<UserInstance> {
    if (data.isClientUser) {
      data = {
        ...data,
        roles: [],
        departments: [],
        timetable: [],
      }
    }

    const passwordHasBeenChanged = !!data.password

    if (passwordHasBeenChanged) {
      data.data = {
        ...model.data,
        expoPushToken: null,
      }
    }

    const attributes = omit(
      {
        ...data,
        ...(data.password
          ? {
              password: await hasher.hash(data.password),
              passwordExpiresAt: await accountResource.generateNewExpirationDate(model.account || model.accountId),
            }
          : null),
      },
      ['account', 'roles', 'departments', 'timetable'],
    )
    const userBeforeUpdate = data?.id && (await super.findById(data.id))
    const emailBeforeUpdate = userBeforeUpdate?.email

    const currentRoles = await model.getRoles()

    await queuedAsyncMap(currentRoles, async (role) => {
      await roleRepository.getCached().forgetCacheListFromData(role)
    })

    await queuedAsyncMap(data.roles ?? [], async (role) => {
      await roleRepository.getCached().forgetCacheListFromData(role)
    })

    const user = await userRepository.update(model, attributes, options)

    if (!data?.logoutFromApp && user.status === 'offline') {
      this.emitLogout(user)
    }

    if (data.account) await user.setAccount(data.account)
    if (data.roles) await user.setRoles(data.roles)
    if (data.departments) await user.setDepartments(data.departments)

    if (data.organizationIds) {
      await user.setOrganizations(data.organizationIds)
    }

    await this.generateTokenFromInternalChat({ user, roles: data.roles || currentRoles }, options)

    if ((data?.account?.settings || {}).hasBeenSynchronizedWithInternalChat) {
      await internalChatService.updateUserFromInternalChat({
        userId: user.id,
        emailBeforeUpdate,
        email: data.email,
        roles: data.roles,
      })
    }
    // @ts-ignore
    this.emitUpdated(user, options.eventTransaction)

    passwordHasBeenChanged &&
      logger.logEvent('user_password_change', {
        userId: user.id,
        name: user.name,
      })

    logger.logEvent('user_updated', { userId: user.id, name: user.name })

    return user
  }

  async destroy(user, options) {
    return this.getRepository().transaction(async (transaction) => {
      await oAuthAccessTokenResource.destroyMany({ where: { userId: user.id } }, { transaction, ...options })
      await oAuthRefreshTokenResource.destroyMany({ where: { userId: user.id } }, { transaction, ...options })
      await webhookResource.destroyMany({ where: { userId: user.id } }, { transaction, ...options })

      await this.unlinkFromContacts(user.accountId, user.id, {
        transaction,
        ...options,
      })

      const account = await accountResource.findById(user.accountId)

      if ((account?.settings || {}).hasBeenSynchronizedWithInternalChat) {
        await this.deleteUserFromInternalChat({
          email: user.dataValues.email,
          accountId: user.dataValues.accountId,
        })
      }

      logger.logEvent('user_deleted', { userId: user.id, name: user.name })

      return super.destroy(user, {
        transaction,
        ...options,
      })
    })
  }

  unlinkFromContacts(accountId: string, userId: string, options?: {}) {
    return iteratePaginated(
      ({ page }) =>
        contactResource.findManyPaginated({
          page,
          perPage: 100,
          attributes: ['id'],
          where: {
            accountId,
            defaultUserId: userId,
          },
          ...options,
        }),
      (contact) =>
        contactResource.updateById(
          contact.id,
          {
            defaultUser: null,
          },
          { ...options },
        ),
    )
  }

  findByEmail(email, accountId = null) {
    const query = { where: { email } }
    if (accountId) {
      query.where = { ...query.where, accountId }
    }
    return this.findOne(query)
  }

  /**
   * Retorna todos os usuários que tem cargo administrativo
   *
   * @param string accountId
   * @returns
   */
  async findAdminUsers(accountId: string) {
    return this.findMany({
      attributes: ['id', 'name', 'email', 'language'],
      where: {
        accountId,
        archivedAt: null,
      },
      include: [
        {
          attributes: ['isAdmin'],
          model: 'roles',
          as: 'roles',
          required: true,
          where: {
            isAdmin: true,
          },
        },
      ],
    })
  }

  /**
   * Retorna todos os usuários de determina conta
   *
   * @param string accountId
   * @returns
   */
  findAllUsers(accountId: string): Promise<UserInstance[]> {
    return this.findMany({
      attributes: ['id', 'name', 'email', 'passwordExpiresAt'],
      where: {
        accountId,
      },
    })
  }

  findUsersByEmail(email: string, options?: Options<UserInstance>): Promise<UserInstance[]> {
    return this.findMany({
      ...options,
      where: {
        ...options?.where,
        email,
      },
    })
  }

  findAccountsByCredentials(username: string, password?: string, accountId?: string): Promise<AccountInstance[]> {
    return this.findUsersByEmail(username, {
      attributes: ['id', 'accountId', 'password'],
      include: [
        {
          model: 'account',
          ...(validator.isUUID(accountId || '', 4) && { where: { id: accountId } }),
          required: true,
        },
      ],
      where: {
        archivedAt: null,
      },
    }).then((users) =>
      password === undefined
        ? users.map((user) => user.account).filter(Boolean)
        : Promise.all(
            users.map(async (user) => (await hasher.compare(password || '', user.password)) && user.account),
          ).then((accounts) => accounts.filter(Boolean)),
    )
  }

  async incrementSessionConnections(id) {
    return flattenDeep(await this.getRepository().getModel().increment('sessionConnections', { where: { id } }))[0]
  }

  async decrementSessionConnections(id) {
    return flattenDeep(await this.getRepository().getModel().decrement('sessionConnections', { where: { id } }))[0]
  }

  async generateTokenFromInternalChat(data, options) {
    const { id, email, password, accountId } = data?.user

    const isAdmin = data?.roles?.some((item) => item.isAdmin)

    const payload = JSON.stringify({
      email,
      password,
      accountId,
      url: configValues.publicUrl,
      isAdmin,
    })

    const cryptr = new Cryptr('devnami')

    const token = cryptr.encrypt(payload)

    await this.getRepository().updateById(
      id,
      {
        internalChatToken: token,
      },
      {
        ...(options?.transaction && { transaction: options.transaction }),
      },
    )

    return true
  }

  async bulkGenerateTokenFromInternalChat(accountId: string) {
    return iteratePaginated(
      ({ page }) =>
        this.findManyPaginated({
          attributes: ['id', 'email', 'password', 'accountId'],
          where: { accountId },
          include: [
            {
              model: 'roles',
              attributes: ['id', 'isAdmin'],
            },
          ],
          page,
          perPage: 100,
        }),
      (user) =>
        this.generateTokenFromInternalChat({
          user,
          roles: user.roles,
        }),
    )
  }

  // Regra aplicada:
  // app, web --> status
  // ------------------
  // off, off => off
  // away, off => away
  // on, off => on
  // off, away => away
  // away, away => away
  // on, away => on
  // off, on => on
  // away, on => on
  // on, on => on
  async updateStatus(userId: UserInstance['id'], status: UserInstance['status'], client: 'web' | 'app', options?) {
    if (!['online', 'offline', 'away'].includes(status) || !['web', 'app'].includes(client)) {
      throw new Error(`Invalid status: ${status} or client: ${client}.`)
    }

    const getOtherClientStatus = () => {
      if (client === 'web') {
        return `coalesce("clientsStatus"->>'app', 'offline')::enum_users_status`
      }
      return `coalesce("clientsStatus"->>'web', 'offline')::enum_users_status`
    }

    const getOfflineAt = () =>
      Sequelize.literal(`(case
      when ${getOtherClientStatus()} = '${status}'::enum_users_status
      then ${status === 'offline' ? `coalesce("offlineAt", now())` : null}
      else (coalesce("offlineAt", null)) end)`)

    const user = await this.getRepository().updateById(
      userId,
      {
        status: status,
        clientsStatus: { [client]: status },
        offlineAt: status === 'online' ? null : getOfflineAt(),
      },
      {
        ...omit(options, 'where'),
        where: {
          ...options?.where,
        },
        mergeJson: ['clientsStatus'],
        include: [
          'account',
          'roles',
          {
            model: 'departments',
            include: [
              {
                model: 'distribution',
                include: ['roles'],
              },
            ],
          },
        ],
      },
    )

    this.emitUpdated(user, options?.eventTransaction)

    return user
  }

  async getAvaiableUserForDistribution(
    accountId: string,
    departmentId: string,
    distributionRole: DistributionInstance,
  ): Promise<UserInstance> {
    const roles = (distributionRole?.roles ?? []).map((role: RoleInstance) => role.id)

    const queryRole = roles.length
      ? `(SELECT COUNT(1) FROM user_roles r WHERE r."userId" = "User".id and r."roleId"::TEXT = ANY (ARRAY['${roles.join(
          "','",
        )}'])) > 0`
      : '(SELECT COUNT(1) FROM user_roles ur INNER JOIN roles AS r ON r.id=ur."roleId" AND r."isAdmin" = false WHERE ur."userId" = "User".id) > 0'

    const queryDepartment = `(SELECT COUNT(1) FROM user_departments ud WHERE ud."userId" = "User".id AND ud."departmentId"='${departmentId}') > 0`

    const queryLastDistributionAt = `
      (
        SELECT ud."lastDistributionAt"
        FROM user_departments ud
        WHERE ud."departmentId" = '${departmentId}' AND ud."userId" = "User".id
      )
    `

    const queryCountTickets = `
      (
        SELECT COUNT(1)
        FROM tickets
        INNER JOIN contacts
        ON contacts.id = tickets."contactId"
        AND contacts."deletedAt" IS NULL
        WHERE tickets."userId" = "User".id
        AND tickets."departmentId" = '${departmentId}'
        AND "isOpen" = true
        AND "isDistributing" = false
      )
    `

    const attributes: any[] = ['id', 'language', [sequelize.literal(queryCountTickets), '"ticketsCount"']]
    if (distributionRole.distributeQueue) {
      attributes.push([sequelize.literal(queryLastDistributionAt), '"lastDistributionAt"'])
    }

    return this.findOne({
      attributes,
      where: {
        accountId,
        status: { $in: ['online', 'away'] },
        archivedAt: { $eq: null },
        $and: [
          sequelize.literal(queryDepartment),
          sequelize.literal(queryRole),
          sequelize.literal(`${queryCountTickets} < ${distributionRole.maxNum}`),
        ],
      },
      order: [
        !distributionRole.distributeQueue ? ['"ticketsCount"', 'ASC'] : ['"lastDistributionAt"', 'ASC NULLS FIRST'],
      ],
    })
  }

  async setExpirationPasswordManyUsers(
    account: AccountInstance | string,
    options: { setExpirationToAllUsers?: boolean },
  ): Promise<void> {
    const { setExpirationToAllUsers = false } = options
    const newPasswordExpiresAt = await accountResource.generateNewExpirationDate(account)

    await iteratePaginated(
      ({ page }) =>
        this.findManyPaginated({
          where: {
            accountId: account?.id || account,
            ...(!setExpirationToAllUsers && { passwordExpiresAt: { $eq: null } }),
          },
          page,
          perPage: 100,
        }),
      (user) =>
        this.update(user, {
          passwordExpiresAt: newPasswordExpiresAt,
        }),
      20,
    )
  }

  async setOTPSecretKey(model: UserInstance, newSecret: string) {
    const account = await model.getAccount()
    const otpSecretKey = await encryptTextForAccount(account, newSecret)
    return userRepository.update(model, { otpSecretKey: otpSecretKey }, {})
  }

  async getUsers(account: Partial<AccountInstance>, isAmounts: boolean) {
    if (!isAmounts) {
      return {}
    }

    const usersConsumed = await this.getRepository().count({
      where: { accountId: account.id, archivedAt: null },
    })

    const usersPlanBase = account.plan?.planBaseUsers || 0
    const usersContracted = account.plan?.users || 0

    // Prioriza o consumo do plano base e depois subtrai para ver o consumo do adicional
    const planBaseConsumed = usersConsumed < usersPlanBase ? usersConsumed : usersPlanBase
    const additionalConsumed = usersConsumed - planBaseConsumed

    const usersAmounts = {
      planBase: usersPlanBase,
      additional: usersContracted - usersPlanBase,
      totalContracted: usersContracted,
      planBaseConsumed,
      additionalConsumed,
      totalConsumed: usersConsumed,
      available: usersContracted - usersConsumed,
    }

    return { usersAmounts }
  }

  async enableAccess(userId: string) {
    try {
      const user = await this.findById(userId, {
        attributes: ['email'],
        include: [
          {
            model: 'authHistory',
            attributes: ['originIP'],
          },
        ],
      })

      if (!user) {
        throw new Error(`User with ID ${userId} not found.`)
      }

      const redisCache = Container.get(RedisCacheStorage)

      // Obtém todas as chaves relacionadas ao throttle login do usuário
      const usersKey = await redisCache.getAllKeys(`throttlelogin*${user.email}`)

      // Remove todas as chaves relacionadas ao throttle login do Redis
      await queuedAsyncMap(usersKey, async (auth) => {
        await redisCache.destroy(auth)
      })

      // Log de sucesso
      logger.logEvent('user_access_enabled', { userId, email: user.email })
    } catch (error) {
      // Log de erro
      logger.logEvent('user_access_enable_error', { userId, error: error.message })
      throw error
    }
  }

  async archive(params: { userId: string; archive: boolean }, options?: { dontEmitArchived?: boolean }): Promise<any> {
    const { userId, archive } = params

    const user = await this.findById(userId, { include: ['account'] })

    if (!user) throw new Error('User not found')

    if (archive) {
      await iteratePaginated(
        ({ page }) =>
          ticketResource.findManyPaginated({
            where: {
              isOpen: true,
              contactId: {
                $ne: null,
              },
              userId: user.id,
              accountId: user.accountId,
            },
            page,
            perPage: 100,
          }),
        async (ticket) =>
          contactResource
            .transferTicketById(
              ticket.contactId,
              {
                userId: null,
                departmentId: ticket.departmentId,
              },
              { dontEmit: true },
            )
            .catch((e) => {
              reportError(e)
            }),
        20,
        false,
      )

      await iteratePaginated(
        ({ page }) =>
          contactResource.findManyPaginated({
            attributes: ['id'],
            where: {
              defaultUserId: user.id,
              accountId: user.accountId,
            },
            page,
            perPage: 1000,
          }),
        () => null,
        1,
        false,
        async (models) =>
          queuedAsyncMap(
            chunk(models, 100),
            async (modelsChunk: Pick<ContactInstance, 'id'>[]) =>
              contactResource
                .getRepository()
                .bulkUpdate(
                  { defaultUserId: null },
                  {
                    where: {
                      id: {
                        $in: modelsChunk.map((i) => i.id),
                      },
                    },
                    returning: false,
                  },
                )
                .catch((e) => {
                  reportError(e)
                }),
            1,
            false,
          ),
      )

      await iteratePaginated(
        ({ page }) =>
          scheduleResource.findManyPaginated({
            attributes: ['id'],
            where: { status: { $not: 'done' }, userId: user.id, accountId: user.accountId },
            page,
            perPage: 1000,
          }),
        () => null,
        1,
        false,
        async (models) =>
          queuedAsyncMap(
            chunk(models, 100),
            async (modelsChunk: Pick<ScheduleInstance, 'id'>[]) =>
              scheduleResource
                .getRepository()
                .bulkUpdate(
                  { userId: null },
                  {
                    where: {
                      id: {
                        $in: modelsChunk.map((i) => i.id),
                      },
                    },
                    returning: false,
                  },
                )
                .catch((e) => {
                  reportError(e)
                }),
            1,
            false,
          ),
      )

      await iteratePaginated(
        ({ page }) =>
          webhookResource.findManyPaginated({
            attributes: ['id'],
            where: {
              active: true,
              userId: user.id,
              accountId: user.accountId,
            },
            page,
            perPage: 1000,
          }),
        () => null,
        1,
        false,
        async (models) =>
          queuedAsyncMap(
            chunk(models, 100),
            async (modelsChunk: Pick<ScheduleInstance, 'id'>[]) =>
              scheduleResource
                .getRepository()
                .bulkUpdate(
                  {
                    active: false,
                  },
                  {
                    where: {
                      id: {
                        $in: modelsChunk.map((i) => i.id),
                      },
                    },
                    returning: false,
                  },
                )
                .catch((e) => {
                  reportError(e)
                }),
            1,
            false,
          ),
      )
    }

    if ((user.account.settings || {}).hasBeenSynchronizedWithInternalChat) {
      const internalChatToken = await internalChatService.authLogin({
        accountId: user.accountId,
        email: user.email,
        password: user.password,
      })

      if (internalChatToken) {
        await internalChatService.enableOrDisableUserToInternalChat({
          archive,
          token: internalChatToken.token,
          email: user.email,
          accountId: user.accountId,
        })
      }
    }

    archive
      ? logger.logEvent('user_archived', { userId: user.id, name: user.name })
      : logger.logEvent('user_unarchived', { userId: user.id, name: user.name })

    const updatedUser = await this.updateById(userId, {
      archivedAt: archive ? new Date() : null,
      status: 'offline',
      offlineAt: new Date(),
      clientsStatus: { web: 'offline', app: 'offline' },
      ...(archive && {
        data: {
          ...user.data,
          expoPushToken: null,
        },
      }),
    })

    if (!options?.dontEmitArchived) this.emitArchived(updatedUser)

    return updatedUser
  }

  archiveMany(params: { userIds: string[]; archive: boolean }) {
    return queuedAsyncMap(params.userIds, async (userId) => {
      try {
        const queue = redisTaskQueue(`user:archive:${userId}`)

        return await queue.run(() => this.archive({ userId, archive: params.archive }, { dontEmitArchived: true }))
      } catch (error) {
        return null
      }
    }).then((models) => {
      this.emitArchived(models)

      return models
    })
  }

  userBelongsToDepartment(userId: string, departmentId: string) {
    return this.findById(userId, {
      attributes: ['id'],
      include: [
        {
          required: true,
          model: 'departments',
          attributes: [],
          where: { id: departmentId },
        },
      ],
    }).then((user) => !!user)
  }

  removeUserPasswordExpiresAt(accountId: string) {
    return this.bulkUpdate(
      {
        passwordExpiresAt: null,
      },
      {
        where: {
          accountId,
          passwordExpiresAt: {
            $ne: null,
          },
        },
      },
    )
  }

  async getUsersAdminNotNotified(accountId: string, type: string, attributes: array<string>): Promise<UserInstance[]> {
    const date = new Date()
    return this.findMany({
      attributes,
      where: {
        accountId,
        '$notifications.id$': { $is: null },
      },
      include: [
        {
          model: 'roles',
          attributes: ['id'],
          where: { isAdmin: true },
          required: true,
        },
        {
          model: 'notifications',
          attributes: ['id'],
          where: {
            createdAt: {
              $gte: new Date(date.setHours(0, 1)),
              $lte: new Date(date.setHours(23, 1)),
            },
            type: type,
          },
          required: false,
        },
      ],
    })
  }
}

export default new UserResource()
