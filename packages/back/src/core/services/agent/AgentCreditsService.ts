import { Inject, Service } from 'typedi'
import Logger from '../logs/Logger'
import accountResource from '../../resources/accountResource'
import creditMovementResource from '../../resources/creditMovementResource'
import notificationResource from '../../resources/notificationResource'
import userResource from '../../resources/userResource'
import config from '../../config'

@Service()
export default class AgentCreditsService {
  @Inject()
  public logger: Logger

  async limitReached(accountId: string, shouldNotify: boolean = false): Promise<boolean> {
    try {
      const account = await accountResource.findById(accountId, { attributes: ['plan'] })

      if (!config('blockAiConsumption')) {
        return false
      }

      if (!account?.plan?.renewDate) {
        this.logger.log(`There is not a plan renew date for account id ${accountId}`, 'warn')

        if (shouldNotify) {
          await this.recordInsufficientCreditsAttempt(accountId)
        }

        return true
      }

      const credits = await creditMovementResource.balance(accountId, 'agent', account?.plan?.renewDate)
      const hasInsufficientCredits = credits <= 0

      if (hasInsufficientCredits && shouldNotify) {
        await this.recordInsufficientCreditsAttempt(accountId)
      }

      return hasInsufficientCredits
    } catch (error) {
      this.logger.log(`Error checking agent credits limit for account ${accountId}: ${error.message}`, 'error')
      return true
    }
  }

  async notifyAdminsAboutInsufficientCredits(accountId: string) {
    try {
      this.logger.log(`Creating agent credits notification for account: ${accountId}`, 'info')

      const adminUsers = await userResource.findAdminUsers(accountId)

      if (!adminUsers || adminUsers.length === 0) {
        this.logger.log(`No admin users found for account: ${accountId}`, 'warn')
        return
      }

      const account = await accountResource.findById(accountId)
      if (!account) {
        this.logger.log(`Account not found: ${accountId}`, 'warn')
        return
      }

      const notificationType = 'agent-credits-insufficient'

      for (const admin of adminUsers) {
        await notificationResource.create({
          accountId,
          userId: admin.id,
          type: notificationType,
          text: '',
          read: false,
          config: {
            accountName: account.name,
            timestamp: new Date().toISOString(),
          },
        })

        this.logger.log(`Created agent credits notification for admin user: ${admin.id}`, 'info')
      }

      this.logger.log(`Successfully created agent credits notifications for ${adminUsers.length} admin users`, 'info')
    } catch (error) {
      this.logger.log(`Error creating agent credits notifications for account ${accountId}: ${error.message}`, 'error')
      throw error
    }
  }

  async hasNotificationBeenSentToday(accountId: string): Promise<boolean> {
    try {
      const today = new Date()
      today.setHours(0, 0, 0, 0)

      const existingNotifications = await notificationResource.findMany({
        where: {
          accountId,
          type: 'agent-credits-insufficient',
          createdAt: {
            $gte: today,
          },
        },
        limit: 1,
      })

      return existingNotifications.length > 0
    } catch (error) {
      this.logger.log(`Error checking existing notifications for account ${accountId}: ${error.message}`, 'error')
      return false
    }
  }

  async recordInsufficientCreditsAttempt(accountId: string) {
    try {
      this.logger.log(`Recording insufficient credits attempt for account: ${accountId}`, 'warn')

      const notificationSentToday = await this.hasNotificationBeenSentToday(accountId)

      if (!notificationSentToday) {
        await this.notifyAdminsAboutInsufficientCredits(accountId)

        this.logger.log(`First insufficient credits attempt of the day for account: ${accountId}`, 'info')
      } else {
        this.logger.log(`Notification already sent today for account: ${accountId}`, 'info')
      }
    } catch (error) {
      this.logger.log(
        `Error recording insufficient credits attempt for account ${accountId}: ${error.message}`,
        'error',
      )
    }
  }
}
