import Axios from 'axios'
import { Inject, Service } from 'typedi'
import Logger from '../logs/Logger'
import HaystackIaApi from '../haystackIa'
import config from '../../configValues'
import creditMovementResource from '../../../core/resources/creditMovementResource'
import AgentCreditsService from './AgentCreditsService'
import {
  AgentConfig,
  AgentPayload,
  AgentResponse,
  VOICE_TONE,
  LANGUAGE_TYPE,
  AGENT_FUNCTION,
  AgentMessagePayload,
} from './utils'

const DEFAULT_AGENT_NAME = 'Assistente Virtual'
const DEFAULT_CREATIVITY = 0.5

@Service()
export default class AgentService {
  @Inject()
  public logger: Logger

  @Inject()
  public haystackApi: HaystackIaApi

  @Inject()
  public agentCreditsService: AgentCreditsService

  private validatePayload(payload: AgentConfig) {
    if (
      !payload.voiceTone ||
      !payload.languageType ||
      !payload.function ||
      !payload.companySegment ||
      !payload.companySubject ||
      !payload.companyServices ||
      !payload.maxAttempts ||
      !payload.prompt
    ) {
      throw new Error(
        `Missing required fields in payload: ${[
          !payload.voiceTone ? 'voiceTone' : '',
          !payload.languageType ? 'languageType' : '',
          !payload.function ? 'function' : '',
          !payload.companySegment ? 'companySegment' : '',
          !payload.companySubject ? 'companySubject' : '',
          !payload.companyServices ? 'companyServices' : '',
          !payload.prompt ? 'prompt' : '',
          !payload.maxAttempts ? 'maxAttempts' : '',
        ]
          .filter(Boolean)
          .join(', ')}`,
      )
    }

    if (!payload.actions || payload.actions.length === 0) {
      throw new Error('Missing required fields in payload: actions')
    }

    if (payload.actions.some(({ action }) => !action.description)) {
      throw new Error('Missing required fields in payload: action.description')
    }
  }

  private formatDescription(
    prompt: string,
    services: string,
    actions: AgentConfig['actions'],
    agentFunction: string,
  ): string {
    return `${prompt.trim()}

      Serviços prestados pela empresa: ${services.trim()}.

      ${
        agentFunction === 'triage'
          ? 'Objetivo: Realizar triagem inicial para qualquer tipo de operação (hospital, e-commerce, SaaS, indústria etc.), identificando rapidamente o motivo do contato, coletando no máximo 3 informações essenciais e encaminhando para um atendente humano. Não resolver problemas, apenas qualificar o atendimento. Saudação: 🤖 BOT: Olá! Bem-vindo(a). Vou fazer algumas perguntas rápidas para direcionar corretamente seu atendimento, tudo bem? Fluxo: 1) Identificar motivo do contato. 2) Coletar detalhe mínimo para direcionamento. 3) Confirmar transferência. 4) Acionar comando de transbordo. Perguntas de triagem: 1) Você pode me dizer, em poucas palavras, o motivo do seu contato? 2) Pode me dar um detalhe adicional para eu direcionar corretamente? (exemplos: produto/serviço, área de interesse ou tipo de solicitação) 3) Há alguma informação específica que o atendente precise saber antes de prosseguir? Política: Fazer no máximo 3 perguntas antes de transferir para um atendente humano. Encerramento: 🤖 BOT: Obrigado pelas informações! Vou transferir você agora para um atendente que dará continuidade ao seu atendimento.'
          : ''
      }

      Ações do robô:
      ${actions
        .map(({ action }, i) => `${action.name.trim() || `Caminho ${i + 1}`}: ${action.description.trim()}`)
        .join('\n')}`.trim()
  }

  async createAgent(payload: AgentConfig, accountId: string): Promise<AgentResponse> {
    if (!accountId) {
      throw new Error('Missing required configuration: accountId')
    }

    const aiUrl = `${config?.haystackIa?.url}/v1/bot-profiles/`

    if (!config?.haystackIa?.url) {
      throw new Error('Missing required configuration: haystackIa url')
    }

    this.validatePayload(payload)

    const agentFunction = AGENT_FUNCTION[payload.function]
    const voiceTone = VOICE_TONE[payload.voiceTone]
    const languageType = LANGUAGE_TYPE[payload.languageType]

    if (!agentFunction || !voiceTone || !languageType) {
      throw new Error(
        `Invalid agent configuration: ${[
          !agentFunction ? 'function' : '',
          !voiceTone ? 'voiceTone' : '',
          !languageType ? 'languageType' : '',
        ]
          .filter(Boolean)
          .join(', ')}`,
      )
    }

    const agentPayload: AgentPayload = {
      nome: DEFAULT_AGENT_NAME,
      empresa: payload.companySubject,
      setor_atuacao: payload.companySegment,
      expertise: agentFunction,
      objetivo: agentFunction,
      comunicacao: `${voiceTone} e ${languageType}`,
      descricao: this.formatDescription(payload.prompt, payload.companyServices, payload.actions, payload.function),
      criatividade: DEFAULT_CREATIVITY,
      account_id: accountId,
    }

    try {
      const res = await Axios.post<AgentResponse>(aiUrl, agentPayload, await this.haystackApi.getConfig())
      return res.data
    } catch (error) {
      this.logger.log(error, 'error')
      throw new Error('Error when creating agent')
    }
  }

  async updateAgent(agentId: string, payload: AgentConfig, accountId: string): Promise<AgentResponse> {
    if (!agentId || !accountId) {
      throw new Error('Missing required configuration: agentId or accountId')
    }

    const aiUrl = `${config?.haystackIa?.url}/v1/bot-profiles/${agentId}`

    if (!config?.haystackIa?.url) {
      throw new Error('Missing required configuration: haystackIa url')
    }

    this.validatePayload(payload)

    const agentFunction = AGENT_FUNCTION[payload.function]
    const voiceTone = VOICE_TONE[payload.voiceTone]
    const languageType = LANGUAGE_TYPE[payload.languageType]

    if (!agentFunction || !voiceTone || !languageType) {
      throw new Error(
        `Invalid agent configuration: ${[
          !agentFunction ? 'function' : '',
          !voiceTone ? 'voiceTone' : '',
          !languageType ? 'languageType' : '',
        ]
          .filter(Boolean)
          .join(', ')}`,
      )
    }

    const agentPayload: AgentPayload = {
      nome: DEFAULT_AGENT_NAME,
      empresa: payload.companySubject,
      setor_atuacao: payload.companySegment,
      expertise: agentFunction,
      objetivo: agentFunction,
      comunicacao: `${voiceTone} e ${languageType}`,
      descricao: this.formatDescription(payload.prompt, payload.companyServices, payload.actions, payload.function),
      criatividade: DEFAULT_CREATIVITY,
      account_id: accountId,
    }

    try {
      const res = await Axios.put<AgentResponse>(aiUrl, agentPayload, await this.haystackApi.getConfig())
      return res.data
    } catch (error) {
      this.logger.log(error, 'error')
      throw new Error('Error when updating agent')
    }
  }

  async deleteAgent(agentId: string): Promise<void> {
    if (!agentId) {
      throw new Error('Missing agentId')
    }

    const aiUrl = `${config?.haystackIa?.url}/v1/bot-profiles/${agentId}`

    if (!config?.haystackIa?.url) {
      throw new Error('Missing required configuration: haystackIa url')
    }

    try {
      await Axios.delete(aiUrl, await this.haystackApi.getConfig())
    } catch (error) {
      this.logger.log(error, 'error')
      throw new Error('Error when deleting agent')
    }
  }

  async generateAgentResponse(agentMessagePayload: AgentMessagePayload, accountId: string, serviceId: string) {
    try {
      const aiUrl = `${config?.haystackIa?.url}/v3/agent/decide-action`
      const aiConfig = await this.haystackApi.getConfig()
      const agentResponse = await Axios.post(aiUrl, agentMessagePayload, aiConfig)

      await creditMovementResource.createDebit({
        accountId,
        serviceType: 'agent',
        amount: 1,
        origin: 'single',
        serviceId,
      })

      return agentResponse
    } catch (error) {
      this.logger.log(error, 'error')
      throw new Error('Error when generating agent response')
    }
  }
}
