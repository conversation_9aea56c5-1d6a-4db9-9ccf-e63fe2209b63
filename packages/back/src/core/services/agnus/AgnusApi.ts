import config from '../../config'
import { HttpClient } from '../httpClient/HttpClient'
import { Container } from 'typedi'

export default class AgnusApi {
  protected getHttpClient() {
    return Container.get(HttpClient)
  }

  async getPlanUrl(agnusKey: string, username: string): Promise<string> {
    const data = {
      signature_id: agnusKey,
      user_name: username,
    }

    const agnusURL = `${config('agnusUrl')}/myplan/authenticate`

    return this.getHttpClient()
      .post(agnusURL, data, this.getConfig())
      .then((res) => res.data)
  }

  async updateAgnus(agnusKey: string, quantities: {}) {
    const data = {
      signature_id: agnusKey,
      ...quantities,
    }

    const agnusURL = `${config('agnusUrl')}/myplan/consume-update`

    return this.getHttpClient()
      .post(agnusURL, data, this.getConfig())
      .then((res) => res.data)
  }

  /**
   * Atualizar a quantidade de consumo da conta no Agnus e gerar um novo token de acesso para acessar o novo Meu Plano
   */
  async getPlanUrl_v2(signatureId: string, consumedUpdate = null) {
    const data = {
      signatureId,
      consumedUpdate,
    }

    const agnusURLv2 = `${config('agnusUrlv2')}/myplan/authenticate`

    return this.getHttpClient()
      .post(agnusURLv2, data, this.getConfig())
      .then((res) => res.data)
  }

  /**
   * Atualizar a quantidade de consumo da conta no Agnus, sem afetar o acesso ao novo Meu Plano
   */
  async updateAgnus_v2(signatureId: string, consumedUpdate = null) {
    const data = {
      signatureId,
      consumedUpdate,
    }

    const agnusURLv2 = `${config('agnusUrlv2')}/myplan/consume-update`

    return this.getHttpClient()
      .post(agnusURLv2, data, this.getConfig())
      .then((res) => res.data)
  }

  async getTermsNotAgreed(agnusKey: string, quantities: {}, expired: boolean) {
    if (!agnusKey) {
      return []
    }
    const data = {
      signatureId: agnusKey,
      services: quantities,
      expired,
    }
    const agnusURL = `${config('agnusUrl')}/term/not-agreed`
    return this.getHttpClient()
      .post(agnusURL, data, this.getConfig())
      .then((res) => res.data)
  }

  async agree(agnusKey: string, terms: Array<string>) {
    const data = {
      signatureId: agnusKey,
      terms,
      agreed: true,
    }
    const agnusURL = `${config('agnusUrl')}/term/agree`
    return this.getHttpClient()
      .patch(agnusURL, data, this.getConfig())
      .then((res) => res.data)
  }

  protected getConfig() {
    return {
      headers: {
        Authorization: config('agnusCloudToken'),
      },
    }
  }

  async getContract(contractId: string) {
    if (!contractId) {
      return []
    }
    const agnusURL = `${config('agnusUrl')}/contract-new/${contractId}`
    return this.getHttpClient()
      .get(agnusURL, this.getConfig())
      .then((res) => res.data)
  }

  async getSignature(signatureId: string) {
    if (!signatureId) {
      return []
    }
    const agnusURL = `${config('agnusUrl')}/signature/${signatureId}`
    return this.getHttpClient()
      .get(agnusURL, this.getConfig())
      .then((res) => res.data)
  }
}
