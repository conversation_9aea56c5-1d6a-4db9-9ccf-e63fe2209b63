import config from '../../config'
import { Container } from 'typedi'
import { HttpClient } from '../httpClient/HttpClient'

export default class AgnusEmail {
  static send(data) {
    const sgConfig = {
      from: config('emailFrom'),
      ...data,
    }
    return Container.get(HttpClient).post(`${config('agnusUrl')}/mail/send/template`, sgConfig, {
      headers: {
        Authorization: config('agnusCloudToken'),
      },
    })
  }
}
