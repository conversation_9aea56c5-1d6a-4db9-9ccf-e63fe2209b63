import { once } from 'lodash'
import setupSequelize from '../db/setupSequelize'
import onDeath from '../misc/onDeath'
import BootstrapApp from './BootstrapApp'
import setupMongoDB, { connection as mongoDbConnection } from '../setupMongoDB'
import EventsDispatcher from '../eventsDispatcher/EventsDispatcher'
import { Container, Service } from 'typedi'

@Service()
export default abstract class BaseApp extends BootstrapApp {
  protected withEventsDispatcher: boolean = false

  protected withSequelize: boolean = true

  protected withMongoDB: boolean = false

  async start() {
    await this.setup()

    if (this.withSequelize) {
      this.logger.log('[BaseApp] Setting up sequelize...')
      setupSequelize()
    }

    if (this.withMongoDB) {
      this.logger.log('[BaseApp] Setting up MongoDB...')
      await setupMongoDB()
    }

    if (this.withEventsDispatcher) {
      this.logger.log('[BaseApp] Setting up events dispatcher...')
      Container.get(EventsDispatcher).start()
    }

    this.logger.log('[BaseApp] Starting...')
    await this.onStart()

    const handleDeath = once(async () => {
      await this.onDeath()
      Container.get(EventsDispatcher).stop()
      if (this.withMongoDB) await mongoDbConnection.close()
    })
    onDeath(handleDeath)

    this.logger.log('[BaseApp] Started.')
  }

  abstract onStart(): Promise<void>

  abstract onDeath(): Promise<void>
}
