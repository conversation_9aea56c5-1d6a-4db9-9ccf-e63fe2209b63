import { once } from 'lodash'
import onDeath from '../misc/onDeath'
import { Inject, Service } from 'typedi'
import Logger from '../logs/Logger'
import reportError from '../logs/reportError'

@Service()
export default abstract class BootstrapApp {
  @Inject()
  protected logger: Logger

  async start() {
    this.logger.log(`[BootstrapApp] Starting...`)

    await this.setup()

    await this.onStart().catch((e) => {
      reportError(e)
      throw e
    })

    const handleDeath = once(async () => this.onDeath())
    onDeath(handleDeath)

    this.logger.log(`[BootstrapApp] Started.`)
  }

  async stop() {
    await this.onDeath()
  }

  protected async setup() {
    process.on('unhandledRejection', (e: Error) => {
      this.logger.log('unhandledRejection: %o', 'error', [e])
    })
    process.on('uncaughtException', (e: Error) => {
      this.logger.log('uncaughtException: %o', 'error', [e])
    })
  }

  abstract onStart(): Promise<void>

  abstract onDeath(): Promise<void>
}
