import oAuthAccessTokenResource from '../../resources/oAuthAccessTokenResource'
import oAuthRefreshTokenResource from '../../resources/oAuthRefreshTokenResource'

export const removeAccessToken = async (userId: string) => {
  await oAuthAccessTokenResource.destroyMany({
    where: {
      userId,
      accessTokenExpiresAt: {
        $ne: null,
      },
    },
  })

  await oAuthRefreshTokenResource.destroyMany({
    where: {
      userId,
    },
  })
}
