import crypto from 'crypto'
import OAuthServer, { Options } from 'express-oauth-server'
import OAuthAccessToken from '../../resources/oAuthAccessTokenResource'
import OAuthRefreshToken from '../../resources/oAuthRefreshTokenResource'
import OAuthClient from '../../resources/oAuthClientResource'
import userResource from '../../resources/userResource'
import hasher from '../../utils/crypt/hasher'
import roleResource from '../../resources/roleResource'

const getFutureDate = () => {
  const date = new Date()
  date.setFullYear(date.getFullYear() + 1)
  return date
}

export const model: Options['model'] = {
  getAccessToken(accessToken) {
    return OAuthAccessToken.findOne({
      where: { accessToken },
      include: [
        {
          model: 'user',
          where: { archivedAt: { $eq: null } },
          required: true,
          include: ['account', 'departments', 'timetable'],
        },
      ],
      cache: true,
    })
      .then(async (auth) => {
        if (!auth) return null

        if (auth.name && auth.deletedAt) return null
        if (auth.name && auth.accessTokenExpiresAt) {
          const now = new Date().getTime()
          const expiresAt = auth.accessTokenExpiresAt.getTime()
          if (now > expiresAt) {
            return null
          }
        }

        auth.user.roles = await roleResource.findMany({
          include: [
            'permissions',
            {
              model: 'users',
              where: {
                id: auth.user.id,
              },
              required: true,
              attributes: [],
            },
          ],
          cache: true,
        })

        return auth
      })
      .then((auth) => {
        if (!auth) return null
        return {
          accessTokenId: auth.id,
          accessToken: auth.accessToken,
          accessTokenExpiresAt: auth.accessTokenExpiresAt || getFutureDate(),
          scope: auth.scope,
          client: { id: auth.clientId },
          user: auth.user,
          outsideRequest: !!auth.name,
        }
      })
  },

  getRefreshToken(refreshToken) {
    return OAuthRefreshToken.findOne({
      where: { refreshToken },
      include: [
        {
          model: 'user',
          where: { archivedAt: { $eq: null } },
          required: true,
          include: ['departments', 'timetable'],
        },
      ],
    }).then(async (refreshToken) => {
      if (!refreshToken) return null

      refreshToken.user.roles = await roleResource.findMany({
        include: [
          'permissions',
          {
            model: 'users',
            where: {
              id: refreshToken.user.id,
            },
            required: true,
            attributes: [],
          },
        ],
        cache: true,
      })

      return {
        refreshToken: refreshToken.refreshToken,
        refreshTokenExpiresAt: refreshToken.refreshTokenExpiresAt,
        client: { id: refreshToken.clientId },
        user: refreshToken.user,
      }
    })
  },

  revokeToken({ refreshToken }) {
    return OAuthRefreshToken.findOne({
      where: { refreshToken },
    }).then((refreshToken) => OAuthRefreshToken.destroy(refreshToken))
  },

  saveToken(token, client, user) {
    return Promise.all([
      OAuthAccessToken.create({
        accessToken: token.accessToken,
        accessTokenExpiresAt: token.accessTokenExpiresAt,
        scope: token.scope,
        clientId: client.id,
        userId: user.id,
      }),
      OAuthRefreshToken.create({
        refreshToken: token.refreshToken,
        refreshTokenExpiresAt: token.refreshTokenExpiresAt,
        scope: token.scope,
        clientId: client.id,
        userId: user.id,
      }),
    ]).then(([accessToken, refreshToken]) => ({
      accessTokenId: accessToken.id,

      accessToken: accessToken.accessToken,
      accessTokenExpiresAt: accessToken.accessTokenExpiresAt,
      refreshToken: refreshToken.refreshToken,
      refreshTokenExpiresAt: refreshToken.refreshTokenExpiresAt,
      scope: accessToken.scope,
      client,
      user,
    }))
  },

  getClient(clientId, clientSecret) {
    return OAuthClient.findOne({
      where: { clientId, clientSecret },
    })
  },

  async getUser(username, password) {
    const [email, accountId] = username.split(':')

    const user = await userResource.findOne({
      where: {
        email: email || username,
        active: true,
        ...(accountId && { accountId }),
      },
      include: ['timetable'],
    })

    if (!user) return null

    const same = await hasher.compare(password, user.password)

    if (!same) return null

    user.roles = await roleResource.findMany({
      include: [
        'permissions',
        {
          model: 'users',
          where: {
            id: user.id,
          },
          required: true,
          attributes: [],
        },
      ],
      cache: true,
    })

    return user
  },

  getUserFromClient(client) {
    return client.getUser()
  },

  async generateAccessToken() {
    const buffer = await crypto.randomBytes(256)

    return crypto.createHash('sha1').update(buffer).digest('hex')
  },
}

export default new OAuthServer({
  model,
  grants: ['password', 'refresh_token'],
  allowBearerTokensInQueryString: true,
  useErrorHandler: true,
})
