import { Inject, Service } from 'typedi'
import sortBy from 'lodash/sortBy'
import { v4 as uuid } from 'uuid'
import CacheService from '../cache/CacheService'
import RedisCacheStorage from '../cache/RedisCacheStorage'
import Logger from '../logs/Logger'
import HttpJobsDispatcher from '../jobs/http/HttpJobsDispatcher'
import { InteractiveMessageInstance } from '../../dbSequelize/models/InteractiveMessage'
import SocketSenderJob from '../../../microServices/workers/jobs/socket/SocketSenderJob'
import { Event } from '../../../microServices/workers/jobs/bot/BaseBotService'
import BotSimulatorJob from '../../../microServices/workers/jobs/bot/BotSimulatorJob'
import botResourceSimulator from '../../../microServices/workers/jobs/bot/botSimulator/resources/botResource'
import { CustomFieldInstance } from '../../dbSequelize/models/CustomField'

type ItemKey = 'Messages' | 'Contact'

type EventKey = 'message-created' | 'message-updated' | 'contact-updated'

export type MessageType =
  | 'text_bot'
  | 'text_interactive_bot'
  | 'select_trigger_bot'
  | 'select_condition_bot'
  | 'text_contact'
  | 'chat_event'

export type MessageInteractive = InteractiveMessageInstance['interactive']

export interface MessageData {
  conditionNodeId?: string
  interactive?: MessageInteractive
  trigger?: {
    events: Event[]
  }
  selectedValue?: string | boolean
  customFieldData?: {
    type: string
    name: string
    id: string
    settings: CustomFieldInstance['settings']
  }
}

export interface MessageFile {
  id: string
  url: string
  publicFilename: string
}

export interface BotSimulatorMessage {
  id: string
  type: MessageType
  text: string
  data: MessageData
  createdAt: Date | string
  file?: MessageFile
}

export interface BotSimulatorContact {
  id: string
  name: string
  createdAt: Date
  session: {
    store: {
      context?: string
      contextPassCount?: number
    }
  } | null
}

interface BotSimulatorContactRedis {
  id: string
  name: string
  createdAt: string
  session: string
}

export interface BotSimulatorSendEvent {
  type: 'text_contact' | 'select_trigger' | 'select_condition'
  id?: string
  text?: string
  selectedValue?: string | boolean
}

export interface AccountBotUser {
  accountId: string
  botId: string
  userId: string
}

const messageTypes: MessageType[] = [
  'text_bot',
  'text_interactive_bot',
  'select_trigger_bot',
  'select_condition_bot',
  'text_contact',
  'chat_event',
]

@Service()
export default class BotSimulatorCacheService implements CacheService {
  @Inject()
  protected redisCacheStorage: RedisCacheStorage

  @Inject()
  protected jobDispatcher: HttpJobsDispatcher

  @Inject()
  protected logger: Logger

  async initCache(): Promise<void> {
    this.logger.log('Finished init cache', 'info')
  }

  protected makeKey({ accountId, botId, userId, itemKey }: AccountBotUser & { itemKey: ItemKey }): string {
    return `BotSimulator:${accountId}:${botId}:${userId}:${itemKey}`
  }

  getKeyParts(key: string): AccountBotUser & { prefix: string; itemKey: string } {
    if (typeof key !== 'string') return key

    const [prefix, accountId, botId, userId, itemKey] = key.split(':')

    return { prefix, accountId, botId, userId, itemKey }
  }

  protected sendBotSimulatorEvent(event: string, data: any) {
    if (!['text_contact', 'select_trigger', 'select_condition'].includes(event)) return

    this.jobDispatcher.dispatch<BotSimulatorJob>('bot-simulator', {
      event,
      data,
    })
  }

  protected sendSocketEvent({
    accountId,
    botId,
    userId,
    eventKey,
    payload,
  }: AccountBotUser & { eventKey: EventKey; payload: BotSimulatorMessage | BotSimulatorContact }): void {
    this.jobDispatcher.dispatch<SocketSenderJob>('socket-sender', {
      event: `bot-simulator.${eventKey}`,
      data: {
        ...payload,
        accountId,
        botId,
        userId,
      },
    })
  }

  async getMessagesByRange({
    messageKey,
    indexStart,
    indexEnd,
  }: {
    messageKey: string
    indexStart: number
    indexEnd: number
  }): Promise<BotSimulatorMessage[]> {
    if (!messageKey || typeof indexStart !== 'number' || typeof indexEnd !== 'number') {
      return []
    }

    return this.redisCacheStorage.getListRange<BotSimulatorMessage>(messageKey, indexStart, indexEnd)
  }

  contactStringify(contact: Partial<BotSimulatorContact>): Partial<BotSimulatorContactRedis> {
    return {
      ...contact,
      ...(contact?.createdAt && { createdAt: new Date(contact.createdAt).toISOString() }),
      ...(contact?.session && { session: JSON.stringify(contact.session) }),
    }
  }

  contactParse(contact: BotSimulatorContactRedis): BotSimulatorContact {
    return {
      ...contact,
      ...(contact?.createdAt && { createdAt: new Date(contact.createdAt) }),
      ...(contact?.session && { session: JSON.parse(contact.session) }),
    }
  }

  async startContactSimulator({
    contactKey,
    userName,
  }: {
    contactKey: string
    userName: string
  }): Promise<BotSimulatorContact> {
    const contact = {
      id: uuid(),
      name: userName,
      createdAt: new Date(),
      session: null,
    }

    await Promise.all([this.setContact({ contactKey, value: contact }), this.pushTriggerMessage({ contactKey })])

    return contact
  }

  async restartSimulator({
    accountId,
    botId,
    userId,
    userName,
  }: AccountBotUser & { userName: string }): Promise<{ restartDate: Date }> {
    const messageKey = this.makeKey({ accountId, botId, userId, itemKey: 'Messages' })
    const contactKey = this.makeKey({ accountId, botId, userId, itemKey: 'Contact' })

    const restartDate = new Date()

    await this.redisCacheStorage.destroy(messageKey)

    await this.startContactSimulator({ contactKey, userName })

    return {
      restartDate,
    }
  }

  async getContact({ contactKey, userName }: { contactKey: string; userName?: string }): Promise<BotSimulatorContact> {
    let contact = await this.redisCacheStorage.getHash<BotSimulatorContactRedis>(contactKey).then(this.contactParse)

    if (!contact?.id && userName) {
      contact = await this.startContactSimulator({ contactKey, userName })
    }

    return contact
  }

  async setContact({ contactKey, value }: { contactKey: string; value: Partial<BotSimulatorContact> }): Promise<void> {
    await this.redisCacheStorage.setHash<Partial<BotSimulatorContactRedis>>(contactKey, this.contactStringify(value))

    const contact = await this.getContact({ contactKey })

    const { accountId, botId, userId } = this.getKeyParts(contactKey)

    this.sendSocketEvent({ accountId, userId, botId, eventKey: 'contact-updated', payload: contact })
  }

  async getContactMessages({ accountId, botId, userId, userName }: AccountBotUser & { userName: string }): Promise<{
    contact: BotSimulatorContact
    messages: BotSimulatorMessage[]
  }> {
    const contactKey = this.makeKey({ accountId, botId, userId, itemKey: 'Contact' })

    const contact = await this.getContact({ contactKey, userName })

    const messages = await this.getMessagesByRange({
      messageKey: this.makeKey({ accountId, botId, userId, itemKey: 'Messages' }),
      indexStart: 0,
      indexEnd: -1,
    })

    return {
      contact,
      messages: sortBy(messages, 'createdAt'),
    }
  }

  async getMessagesLength({ accountId, botId, userId }: AccountBotUser): Promise<number> {
    if (!accountId || !botId || !userId) {
      return 0
    }

    return this.redisCacheStorage.getListLength(this.makeKey({ accountId, botId, userId, itemKey: 'Messages' }))
  }

  async pushTriggerMessage({ contactKey }: { contactKey: string }): Promise<void> {
    if (!contactKey) return

    const { accountId, botId, userId } = this.getKeyParts(contactKey)

    const bot = await botResourceSimulator.findById(botId)

    const botTriggerEvents = Object.keys(bot?.contexts?.['@INIT']?.triggers || {}) as Event[]

    await this.pushMessage({
      accountId,
      botId,
      userId,
      messageType: 'select_trigger_bot',
      messageData: { trigger: { events: botTriggerEvents } },
    })
  }

  async pushMessage({
    accountId,
    botId,
    userId,
    messageType,
    messageText,
    messageData,
    messageFile,
  }: AccountBotUser & {
    messageType: MessageType
    messageText?: string
    messageData?: MessageData
    messageFile?: MessageFile
  }): Promise<boolean> {
    if (!accountId || !botId || !userId || !messageType || !messageTypes.includes(messageType)) {
      return false
    }

    const message = {
      id: uuid(),
      type: messageType,
      text: messageText || '',
      data: messageData || {},
      createdAt: new Date(),
      ...(messageFile && { file: messageFile }),
    }

    const messageKey = this.makeKey({ accountId, botId, userId, itemKey: 'Messages' })
    const contactKey = this.makeKey({ accountId, botId, userId, itemKey: 'Contact' })

    const messageIndex = await this.redisCacheStorage.pushListItem<BotSimulatorMessage>(messageKey, message)

    this.sendBotSimulatorEvent(messageType, {
      message,
      messageKey,
      messageIndex,
      contactKey,
    })

    this.sendSocketEvent({ accountId, userId, botId, eventKey: 'message-created', payload: message })

    return true
  }

  async sendEvent({
    accountId,
    botId,
    userId,
    eventPayload,
  }: AccountBotUser & {
    eventPayload: BotSimulatorSendEvent
  }): Promise<boolean> {
    if (!accountId || !botId || !userId || !eventPayload) {
      return false
    }

    const contactKey = this.makeKey({ accountId, botId, userId, itemKey: 'Contact' })

    const { type: messageType, id: messageId, text: messageText, selectedValue } = eventPayload

    if (messageType === 'text_contact') {
      return this.pushMessage({ accountId, botId, userId, messageType, messageText })
    }

    const contact = await this.getContact({ contactKey })
    const currentContext = contact?.session?.store?.context || '@INIT'

    if (
      (messageId && messageType === 'select_trigger' && currentContext === '@INIT') ||
      (messageId && messageType === 'select_condition' && currentContext?.startsWith('CONDITION_NODE_'))
    ) {
      await this.updateMessageDataById({ accountId, userId, botId, messageId, newMessageData: { selectedValue } })
      this.sendBotSimulatorEvent(messageType, { contactKey, selectedValue })
      return true
    }

    return false
  }

  async updateMessageDataById({
    accountId,
    userId,
    botId,
    messageId,
    newMessageData,
  }: AccountBotUser & { messageId: string; newMessageData: Partial<MessageData> }): Promise<boolean> {
    if (!accountId || !userId || !botId || !messageId || !newMessageData) return false

    const messageKey = this.makeKey({ accountId, botId, userId, itemKey: 'Messages' })

    const messages = await this.getMessagesByRange({
      messageKey,
      indexStart: 0,
      indexEnd: -1,
    })

    const messageIndex = messages.findIndex((message) => message.id === messageId)

    if (messageIndex === -1) return false

    const newMessage = {
      ...messages[messageIndex],
      data: {
        ...messages[messageIndex].data,
        ...newMessageData,
      },
    }

    await this.redisCacheStorage.setListItem<BotSimulatorMessage>(messageKey, messageIndex, newMessage)

    this.sendSocketEvent({ accountId, userId, botId, eventKey: 'message-updated', payload: newMessage })

    return true
  }
}
