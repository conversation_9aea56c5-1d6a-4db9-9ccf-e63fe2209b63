import { AsyncLocalStorage } from 'async_hooks'

export default class Cls {
  protected asyncLocalStorage: AsyncLocalStorage<Map<string, any>>

  constructor() {
    this.asyncLocalStorage = new AsyncLocalStorage()
  }

  run<T>(fn: (...args: any[]) => T): T {
    return this.asyncLocalStorage.run(this.getStore(), fn)
  }

  // @deprecated
  runAndReturn<T>(fn: (...args: any[]) => T): T {
    return this.run(fn)
  }

  enter() {
    this.asyncLocalStorage.enterWith(this.getStore())
  }

  bind<T>(fn: (...args: any[]) => T) {
    return AsyncLocalStorage.bind(fn)
  }

  set<T>(key: string, value: T): T {
    this.getStore().set(key, value)
    return value
  }

  get(key: string, defaultValue = undefined): any {
    const store = this.getStore()
    if (store.has(key)) return store.get(key)
    return defaultValue
  }

  protected getStore() {
    return this.asyncLocalStorage.getStore() ?? new Map()
  }
}
