import { Container, Token } from 'typedi'
import { ObjectType } from 'typedi/types/ObjectType'
import AppContextCls from '../cls/AppContextCls'

const getApp = () => Container.get(AppContextCls)

export default class AppContainer extends Container {
  static get<T>(
    type:
      | ObjectType<T>
      | string
      | Token<T>
      | {
          service: T
        },
  ): T {
    const containerName = getApp().getContainer()

    if (containerName === 'default') {
      // @ts-ignore
      return Container.get(type)
    }

    // @ts-ignore
    return Container.of(containerName).get(type)
  }
}
