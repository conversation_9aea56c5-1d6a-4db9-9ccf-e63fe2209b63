import { get } from 'lodash'
import { Inject, Service } from 'typedi'
import Env from './Env'
import type configValues from '../../configValues'

type AcceptableValueTypes = string | number | boolean | ConfigValues | string[] | undefined

type ConfigValues =
  | typeof configValues
  | {
      [key: string]: AcceptableValueTypes
    }

@Service()
export default class Config<V extends ConfigValues> {
  @Inject()
  protected env: Env

  protected values: V = {} as V

  get<T extends keyof typeof this.values, D extends (typeof this.values)[T]>(
    key: T,
    defaults: D = null,
  ): (typeof this.values)[typeof key] {
    return get(this.values, key, defaults)
  }

  set<T extends keyof typeof this.values>(key: T | string, value: AcceptableValueTypes) {
    this.values = {
      ...this.values,
      [key]: value,
    }
    return this
  }

  setValues(value: any) {
    this.values = value
    return this
  }

  getValues() {
    return this.values
  }
}
