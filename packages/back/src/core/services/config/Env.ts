import DotEnv from 'dotenv'
import { Service } from 'typedi'

@Service()
export default class Env {
  constructor() {
    const envMap = {
      test: '.env.test',
      'front-test': '.env.test',
      default: '.env',
    }
    const nodeEnv = process.env.NODE_ENV

    const path: string = envMap[nodeEnv] || envMap.default
    DotEnv.config({ path })
  }

  string(key: string, defaultVal?: any) {
    return process.env[key] || defaultVal
  }

  number(key: string, defaultVal?: number): number {
    return process.env[key] ? Number(process.env[key]) : defaultVal
  }

  boolean(key: string, defaultVal: boolean = false): boolean {
    return process.env[key] ? process.env[key] === 'true' || process.env[key] === '1' : defaultVal
  }
}
