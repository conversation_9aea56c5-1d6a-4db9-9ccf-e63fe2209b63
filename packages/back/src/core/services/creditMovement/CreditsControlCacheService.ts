import { Inject, Service } from 'typedi'
import CacheService from '../cache/CacheService'
import RedisCacheStorage from '../cache/RedisCacheStorage'
import { creditControlCacheQueue } from '../../queues/creditControl'
import queuedAsyncMap from '../../utils/array/queuedAsyncMap'
import accountResource from '../../resources/accountResource'
import contractedCreditResource from '../../resources/contractedCreditResource'
import { AccountInstance } from '../../dbSequelize/models/Account'
import castObject from '../../utils/types/castObject'
import castValue from '../../utils/types/castValue'
import { ServiceType } from '../../../microServices/workers/jobs/account/updateContractPlan/Types'
import Logger from '../logs/Logger'

export const mustConsumeServiceTypes: ServiceType[] = [
  'email',
  'facebook-messenger',
  'google-business-message',
  'instagram',
  'reclame-aqui',
  'telegram',
  'webchat',
  'whatsapp',
]

type PendingCampaign = {
  campaignId: string
  requiredCredits: number
}

export type CreditMessageService = {
  messageId: string
  serviceId: string
}

type Accounts = {
  [key: string]: boolean
}

type ConsumeSettings = {
  canConsumeIn?: boolean
  canConsumeOut?: boolean
  allowExceedLimit?: boolean
}

type Amounts = {
  consumed?: number
  reserved?: number
  available?: number //current - consumed - reserved
  current?: number //totalIn - totalOut
  totalIn?: number
  totalOut?: number
}

type TotalInOut = {
  // Créditos de entrada
  planCredits?: number
  additionalCredits?: number
  // Créditos de saída (consolidado por plano ou adicional)
  planBaseConsumed?: number
  additionalConsumed?: number
  // Créditos de saída (consolidado por conexão)
  email?: number
  'facebook-messenger'?: number
  'google-business-message'?: number
  instagram?: number
  'reclame-aqui'?: number
  'sms-wavy'?: number
  telegram?: number
  webchat?: number
  whatsapp?: number
  'whatsapp-business'?: number
}

const asConsumeSettings = castObject<ConsumeSettings>({
  canConsumeIn: false,
  canConsumeOut: false,
  allowExceedLimit: false,
})

const asAmounts = castObject<Amounts>({
  consumed: 0,
  reserved: 0,
  available: 0,
  current: 0,
  totalIn: 0,
  totalOut: 0,
})

const asTotalInOut = castObject<TotalInOut>({
  // Créditos de entrada
  planCredits: 0,
  additionalCredits: 0,
  // Créditos de saída (consolidado por plano ou adicional)
  planBaseConsumed: 0,
  additionalConsumed: 0,
  // Créditos de saída (consolidado por conexão)
  email: 0,
  'facebook-messenger': 0,
  'google-business-message': 0,
  instagram: 0,
  'reclame-aqui': 0,
  'sms-wavy': 0,
  telegram: 0,
  webchat: 0,
  whatsapp: 0,
  'whatsapp-business': 0,
})

@Service()
export default class CreditsControlCacheService implements CacheService {
  @Inject()
  protected cacheStorage: RedisCacheStorage

  @Inject()
  protected logger: Logger

  protected statusAccountsCache: Map<string, 'pending' | 'loading' | 'finished'> = new Map()

  protected makeKey(key: string) {
    return `CreditsControl:${key}`
  }

  protected async getAccountKey(accountId: string, subkey: string) {
    if (this.statusAccountsCache.get(accountId) !== 'loading' && !(await this.isCreditsControlEnabled(accountId))) {
      throw new Error('Account credits control is disabled')
    }

    return this.makeKey(`${accountId}:${subkey}`)
  }

  async getCachedAccounts(): Promise<Accounts> {
    const accounts = await this.cacheStorage.getHash<Accounts>(this.makeKey('Accounts'))

    // Converte os valores de accounts para booleano de fato, pois o getHash retorna os valores como string
    Object.keys(accounts).forEach((key) => {
      accounts[key] = castValue(accounts[key], 'boolean')
    })

    return accounts
  }

  addCachedAccount(value: Accounts): Promise<void> {
    return this.cacheStorage.setHash(this.makeKey('Accounts'), value)
  }

  isAccountCached(accountId: string) {
    return this.cacheStorage.isHashFieldExists(this.makeKey('Accounts'), accountId)
  }

  async isCreditsControlEnabled(accountId: string): Promise<boolean> {
    const account = await this.cacheStorage.getHashField<boolean>(this.makeKey('Accounts'), accountId)
    return castValue(account, 'boolean')
  }

  async getConsumeSettings(accountId: string): Promise<ConsumeSettings> {
    return this.cacheStorage
      .getHash<ConsumeSettings>(await this.getAccountKey(accountId, 'ConsumeSettings'))
      .then(asConsumeSettings)
  }

  async setConsumeSettings(accountId: string, value: ConsumeSettings) {
    await this.cacheStorage.setHash(await this.getAccountKey(accountId, 'ConsumeSettings'), value)
  }

  async getAmounts(accountId: string): Promise<Amounts> {
    return this.cacheStorage.getHash<Amounts>(await this.getAccountKey(accountId, 'Amounts')).then(asAmounts)
  }

  async setAmounts(accountId: string, value: Amounts) {
    await this.cacheStorage.setHash(await this.getAccountKey(accountId, 'Amounts'), value)
  }

  async incrementAmount(accountId: string, value: Amounts): Promise<Amounts> {
    return this.cacheStorage.incrementHashFields(await this.getAccountKey(accountId, 'Amounts'), value).then(asAmounts)
  }

  async getTotalInOut(accountId: string): Promise<TotalInOut> {
    return this.cacheStorage.getHash<TotalInOut>(await this.getAccountKey(accountId, 'TotalInOut')).then(asTotalInOut)
  }

  async setTotalInOut(accountId: string, value: TotalInOut) {
    await this.cacheStorage.setHash(await this.getAccountKey(accountId, 'TotalInOut'), value)
  }

  async incrementTotalInOut(accountId: string, value: TotalInOut): Promise<TotalInOut> {
    return this.cacheStorage
      .incrementHashFields(await this.getAccountKey(accountId, 'TotalInOut'), value)
      .then(asTotalInOut)
  }

  async getPendingCampaigns(accountId: string): Promise<PendingCampaign[]> {
    return this.cacheStorage.setGet<PendingCampaign[]>(await this.getAccountKey(accountId, 'PendingCampaigns'))
  }

  async addPendingCampaign(accountId: string, value?: PendingCampaign): Promise<Amounts> {
    await this.cacheStorage.setAdd<PendingCampaign>(await this.getAccountKey(accountId, 'PendingCampaigns'), value)
    const { requiredCredits: reserved } = value

    const amount = await this.incrementAmount(accountId, { reserved, available: -reserved })
    return amount
  }

  async removePendingCampaign(accountId: string, campaignId: string): Promise<void> {
    const pendingCampaigns = (await this.getPendingCampaigns(accountId)) || []
    pendingCampaigns.forEach(async (pendingCampaign) => {
      if (pendingCampaign.campaignId === campaignId) {
        await this.cacheStorage.setRemove(await this.getAccountKey(accountId, 'PendingCampaigns'), pendingCampaign)
      }
    })
  }

  async campaignMustConsumeCredits(accountId: string, serviceType: ServiceType): Promise<boolean> {
    if (!mustConsumeServiceTypes.includes(serviceType)) return false

    const hasCreditsControlEnabled = await this.isCreditsControlEnabled(accountId)
    if (!hasCreditsControlEnabled) return false

    return true
  }

  async campaignReserveCreditsIfNeeded(
    accountId: string,
    campaignId: string,
    requiredCredits: number,
    serviceType: ServiceType,
  ): Promise<boolean> {
    const mustConsumeCredits = await this.campaignMustConsumeCredits(accountId, serviceType)
    if (!mustConsumeCredits) {
      return true
    }

    const insufficientCredits = await this.insufficientCreditsToCreateMessage(accountId, true)
    if (insufficientCredits) {
      return false // Não conseguiu reservar
    }

    const { available } = await this.getAmounts(accountId)
    if (requiredCredits > available) {
      return false // Não conseguiu reservar
    }

    await this.addPendingCampaign(accountId, {
      campaignId: campaignId,
      requiredCredits: requiredCredits,
    })

    return true
  }

  async campaignRefundCreditsIfNeeded(accountId: string, serviceType: ServiceType) {
    const mustConsumeCredits = await this.campaignMustConsumeCredits(accountId, serviceType)
    if (!mustConsumeCredits) {
      return
    }

    await this.incrementAmount(accountId, {
      available: 1,
      reserved: -1,
    })
  }

  async getMessagesByRange(
    accountId: string,
    serviceType: ServiceType,
    start: number,
    end: number,
  ): Promise<CreditMessageService[]> {
    return this.cacheStorage.getListRange(await this.getAccountKey(accountId, `Messages-${serviceType}`), start, end)
  }

  async trimMessages(accountId: string, serviceType: ServiceType, start: number, end: number): Promise<void> {
    await this.cacheStorage.trimList(await this.getAccountKey(accountId, `Messages-${serviceType}`), start, end)
  }

  async getMessagesLength(accountId: string, serviceType: ServiceType): Promise<number> {
    return this.cacheStorage.getListLength(await this.getAccountKey(accountId, `Messages-${serviceType}`))
  }

  async pushMessage(accountId: string, serviceType: ServiceType, value: CreditMessageService, fromCampaign = false) {
    if (!mustConsumeServiceTypes.includes(serviceType)) {
      this.logger.log(`Service type ${serviceType} must not consumed`, 'error')
      return
    }

    await this.cacheStorage.pushListItem(await this.getAccountKey(accountId, `Messages-${serviceType}`), value)
    const { available } = await this.incrementAmount(accountId, {
      consumed: 1,
      ...(fromCampaign ? { reserved: -1 } : { available: -1 }),
    })
    if (available <= 0) {
      await this.blockConsumeCredits(accountId)
    }
  }

  async blockConsumeCredits(accountId: string) {
    const { allowExceedLimit } = await this.getConsumeSettings(accountId)

    await Promise.all([
      this.setConsumeSettings(accountId, {
        canConsumeOut: false,
        canConsumeIn: allowExceedLimit,
      }),
      accountResource.accountUpdateCreditsControl(accountId, {
        withoutCreditsAvailable: true,
      }),
    ])
  }

  async insufficientCreditsToCreateMessage(accountId: string, isOut: boolean) {
    const { canConsumeIn, canConsumeOut, allowExceedLimit } = await this.getConsumeSettings(accountId)

    if (isOut && !canConsumeOut) return true
    if (!isOut && !canConsumeIn && !allowExceedLimit) return true

    return false
  }

  async getAvailableCredits(accountId: string) {
    const { consumed = 0, reserved = 0, current = 0 } = await this.getAmounts(accountId)
    return current - (reserved + consumed)
  }

  async getCredits(account: Partial<AccountInstance>, isAmounts: boolean) {
    if (!isAmounts) {
      return {}
    }

    const [credits, amounts, totalConsumedPercent] = await Promise.all([
      contractedCreditResource.getActiveCreditSystemResume(account.id),
      this.getAmounts(account.id),
      this.getCreditsConsumedPercent(account.id),
    ])

    const consumedPercent: { [key: string]: number } = {}

    for (const [serviceType, count] of Object.entries(credits.consumedServiceTypes)) {
      consumedPercent[serviceType] = credits.totalIn > 0 ? Math.floor((count / credits.totalIn) * 100) : 0
    }

    const creditsAmounts = {
      planBase: credits.totalPlanIn,
      planBaseConsumed: credits.totalPlanOut,
      planBaseConsumedPercent:
        credits.totalPlanIn > 0 ? Math.floor((credits.totalPlanOut / credits.totalPlanIn) * 100) : 0,
      quantityPlanActive: credits.quantityPlanActive,
      additional: credits.totalAdditionalIn,
      additionalConsumed: credits.totalAdditionalOut,
      additionalConsumedPercent:
        credits.totalAdditionalIn > 0 ? Math.floor((credits.totalAdditionalOut / credits.totalAdditionalIn) * 100) : 0,
      quantityAdditionalActive: credits.quantityAdditionalActive,
      nextAdditionalInExpire: credits.nextAdditionalInExpire,
      nextAdditionalExpireAt: credits.nextAdditionalExpireAt,
      totalContracted: credits.totalIn,
      totalConsumed: amounts.consumed + credits.totalOut,
      consumed: credits.consumedServiceTypes,
      reserved: amounts.reserved,
      available: amounts.available,
      consumedPercent,
      totalConsumedPercent,
      nextRenewalDate: account.creditsControl?.nextRenewalDate || null,
    }

    return { creditsAmounts }
  }

  /**
   * Obter a porcentagem de consumo dos créditos de uma respectiva conta
   * @param accountId - ID da conta
   * @returns A porcentagem de consumo representando o total utilizado / total adquirido
   */
  async getCreditsConsumedPercent(accountId: string) {
    const { consumed = 0, totalIn = 0, totalOut = 0 } = await this.getAmounts(accountId)

    if (totalIn <= 0) {
      // Se o totalIn for menor ou igual a zero, devemos retornar o valor 0 diretamente
      // Para evitar a divisão por zero no próximo passo e resultar em erro
      return 0
    }

    // Soma o que já saiu (totalOut) com o consumo atual não consolidado (consumed)
    // Divide a soma pelo total de entrada (totalIn)
    // O resultado da divisão deve ser um valor entre 0 e 1
    const creditsConsumed = (totalOut + consumed) / totalIn

    // Transforma o resultado da divisão para porcentagem
    // Arredonda o resultado para baixo (inteiro mais próximo)
    return Math.floor(creditsConsumed * 100)
  }

  async tryLoadCacheAccount(accountId: string): Promise<boolean> {
    try {
      this.logger.log(`Starting tryLoadCacheAccount ${accountId}`, 'info')

      // Busca a conta informada que esteja ativa, não expirada e com controle de crédito habilitado
      const account = await accountResource.creditsControlEnabledById(accountId)

      if (!account) {
        // Nenhuma conta foi encontrada, retorna
        this.logger.log('No account was found with credits control enabled', 'warn')
        return false
      }

      // Chama o carregamento do cache para a conta encontrada
      const loadedAccount = await this.loadCacheAccount(account)

      this.logger.log(`Finished tryLoadCacheAccount ${accountId}`, 'info')

      return loadedAccount
    } catch (error) {
      this.logger.log('Error in tryLoadCacheAccount: %o', 'error', [error])
      return false
    }
  }

  async initCache(): Promise<void> {
    // Carregar o cache de controle de créditos para todas as contas
    this.logger.log('Starting tryLoadCacheAllAccounts', 'info')
    const result = await this.tryLoadCacheAllAccounts(true)
    this.logger.log(`Finished tryLoadCacheAllAccounts: ${result} accounts`, 'info')
  }

  async tryLoadCacheAllAccounts(forceReload = false): Promise<number> {
    try {
      // Busca as contas ativas, não expiradas e com controle de crédito habilitado
      const accounts = await accountResource.creditsControlEnabledMany()

      if (!accounts?.[0]) {
        // Nenhuma conta foi encontrada, retorna
        this.logger.log('No account was found with credits control enabled', 'warn')
        return 0
      }

      // Chama o carregamento do cache para cada uma das contas encontradas
      const loadedAccounts = await queuedAsyncMap(accounts, (account) => this.loadCacheAccount(account, forceReload))

      return loadedAccounts.reduce((acc, isLoaded) => (isLoaded ? acc + 1 : acc), 0)
    } catch (error) {
      this.logger.log('Error in tryLoadCacheAllAccounts: %o', 'error', [error])
      return 0
    }
  }

  protected async loadCacheAccount(account: AccountInstance, forceReload = false): Promise<boolean> {
    if (!account || !account.id || !account.creditsControl) {
      // Campos obrigatório para realizar o carregamento
      this.logger.log('The account is required to load the cache', 'error')
      return false
    }

    return creditControlCacheQueue(account.id)
      .run(async () => {
        return this.calculateCreditsQuantities(account, forceReload)
      })
      .catch((error) => {
        this.logger.log('Error in loadCacheAccount: %o', 'error', [error])
        this.statusAccountsCache.set(account.id, 'pending')
        return false
      })
  }

  /**
   * Essa função só deve ser chamada dentro da creditControlCacheQueue
   * @param account - Registro da conta na tabela de accounts
   * @param forceReload - Flag para forçar o recarregamento quando já estiver carregado em cache
   * @param resetNotificationsSent - Flag para resetar as notificações enviadas da conta
   * @returns Booleano indicando se a operação foi um sucesso
   */
  async calculateCreditsQuantities(
    account: AccountInstance,
    forceReload = false,
    resetNotificationsSent = false,
  ): Promise<boolean> {
    // Verificar se o controle de crédito está carregado e habilitado
    const creditsControlEnabled = await this.isCreditsControlEnabled(account.id)

    if (creditsControlEnabled && !forceReload) {
      // Está carregado, habilitado e não precisa forçar o recarregamento, retorna
      this.statusAccountsCache.set(account.id, 'finished')
      this.logger.log('The account is already loaded and enabled', 'info')
      return false
    }

    // Inicia o processo de carregamento
    this.logger.log(`Processing accountId: ${account.id}`, 'info')
    this.statusAccountsCache.set(account.id, 'loading')

    // Busca os créditos vigentes e as quantidades no cache
    const [credits, amounts] = await Promise.all([
      contractedCreditResource.getActiveCreditSystemResume(account.id),
      this.getAmounts(account.id),
    ])

    const { consumed = 0, reserved = 0 } = amounts
    const updatedCurrentValue = credits.totalIn - credits.totalOut
    const updatedAvailableValue = updatedCurrentValue - consumed - reserved
    const allowExceedLimit = !!account.creditsControl?.allowExceedLimit

    // Atualiza todas as informações
    await Promise.all([
      this.setAmounts(account.id, {
        ...(consumed === 0 && { consumed }),
        ...(reserved === 0 && { reserved }),
        available: updatedAvailableValue,
        current: updatedCurrentValue,
        totalIn: credits.totalIn,
        totalOut: credits.totalOut,
      }),
      this.setConsumeSettings(account.id, {
        canConsumeIn: updatedAvailableValue > 0 || allowExceedLimit,
        canConsumeOut: updatedAvailableValue > 0,
        allowExceedLimit,
      }),
      accountResource.accountUpdateCreditsControl(account.id, {
        withoutCreditsAvailable: updatedAvailableValue <= 0,
        ...(resetNotificationsSent && updatedAvailableValue > 0 && { notificationsSent: [] }),
      }),
    ])

    // Habilita o cache
    this.statusAccountsCache.set(account.id, 'finished')
    await this.addCachedAccount({ [account.id]: true })

    return true
  }
}
