import * as cryptor from '../../utils/crypt/cryptor'
import config from '../../config'

const appEncryptionKey = Buffer.from(config('encryptionKey'), 'hex')

export const createEncryptionKey = () => cryptor.randomBytes(32)

export const createEncryptedEncryptionKey = () =>
  cryptor.encryptBufferWithConcatenatedIv(createEncryptionKey(), appEncryptionKey)

export const decryptEncryptionKey = (encryptedKey) =>
  cryptor.decryptBufferWithConcatenatedIv(encryptedKey, appEncryptionKey)

export const getAccountEncryptionKey = (account) => {
  if (!account) throw new Error('Account is required.')

  if (!account.encryptionKey) {
    if (!account.settings.encryptionDisabled) throw new Error(`No encryptionKey on account #${account.id}.`)
  }
  return decryptEncryptionKey(Buffer.from(account.encryptionKey, 'hex'))
}

export const encryptTextForAccount = (account, text, force = false) => {
  if (!force && account.settings.encryptionDisabled) {
    return text
  }

  const encryptionKey = getAccountEncryptionKey(account)

  return cryptor.encryptText(typeof text === 'string' ? text : '', encryptionKey)
}

export const decryptTextForAccount = (account, text, force = false) => {
  if (!force && account.settings.encryptionDisabled) {
    return text
  }

  const encryptionKey = getAccountEncryptionKey(account)

  return cryptor.decryptText(text, encryptionKey)
}

export const encryptBufferForAccountWithConcatenatedIv = (account, buffer) => {
  const encryptionKey = getAccountEncryptionKey(account)
  return cryptor.encryptBufferWithConcatenatedIv(buffer, encryptionKey)
}

export const decryptBufferForAccountWithConcatenatedIv = (account, buffer) => {
  const encryptionKey = getAccountEncryptionKey(account)
  return cryptor.decryptBufferWithConcatenatedIv(buffer, encryptionKey)
}

export const encryptBufferForAccountWithSeparatedIv = (account, buffer) => {
  const encryptionKey = getAccountEncryptionKey(account)
  return cryptor.encryptBufferWithSeparatedIv(buffer, encryptionKey)
}

export const decryptBufferForAccountWithSeparatedIv = (account, buffer) => {
  const encryptionKey = getAccountEncryptionKey(account)
  return cryptor.decryptBufferWithSeparatedIv(buffer, encryptionKey)
}

export const createCipherWithIvForAccount = (account) => {
  const encryptionKey = getAccountEncryptionKey(account)
  return cryptor.createCipherWithIv(encryptionKey)
}

export const createDecipherForAccount = (iv, account) => {
  const encryptionKey = getAccountEncryptionKey(account)
  return cryptor.createDecipher(iv, encryptionKey)
}
