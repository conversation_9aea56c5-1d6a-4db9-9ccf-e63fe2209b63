import 'reflect-metadata'
import { Container } from 'typedi'
import { Sequelize, Op } from 'sequelize'
import storage from '../storage'
import config from '../../config'
import Logger from '../logs/Logger'

const fs = storage.getDriver('fs')

const operatorsAliases = {
  $eq: Op.eq,
  $ne: Op.ne,
  $gte: Op.gte,
  $gt: Op.gt,
  $lte: Op.lte,
  $lt: Op.lt,
  $not: Op.not,
  $in: Op.in,
  $notIn: Op.notIn,
  $is: Op.is,
  $like: Op.like,
  $notLike: Op.notLike,
  $iLike: Op.iLike,
  $notILike: Op.notILike,
  $regexp: Op.regexp,
  $notRegexp: Op.notRegexp,
  $iRegexp: Op.iRegexp,
  $notIRegexp: Op.notIRegexp,
  $between: Op.between,
  $notBetween: Op.notBetween,
  $overlap: Op.overlap,
  $contains: Op.contains,
  $contained: Op.contained,
  $adjacent: Op.adjacent,
  $strictLeft: Op.strictLeft,
  $strictRight: Op.strictRight,
  $noExtendRight: Op.noExtendRight,
  $noExtendLeft: Op.noExtendLeft,
  $and: Op.and,
  $or: Op.or,
  $any: Op.any,
  $all: Op.all,
  $values: Op.values,
  $col: Op.col,
}

const logger = Container.get(Logger)

const logSql = (sql: string, timing?: number) => logger.log('SQL: %s \nTIMING: %s', 'info', [sql, timing])

const sequelize = new Sequelize(config('dbDatabase'), null, null, {
  dialect: config('dbDialect'),
  logging: config('dbLogSql') ? logSql : false,
  operatorsAliases,
  pool: {
    max: config('dbMaxConnections'),
    min: 0,
    idle: 5 * 1000,
    acquire: 30 * 1000,
  },
  replication: {
    read: [
      {
        host: config('dbReadHost'),
        port: config('dbPort'),
        username: config('dbReadUsername'),
        password: config('dbReadPassword'),
      },
      ...(config('dbRead2Host')
        ? [
            {
              host: config('dbRead2Host'),
              port: config('dbPort'),
              username: config('dbRead2Username'),
              password: config('dbRead2Password'),
            },
          ]
        : []),
      ...(config('dbRead3Host')
        ? [
            {
              host: config('dbRead3Host'),
              port: config('dbPort'),
              username: config('dbRead3Username'),
              password: config('dbRead3Password'),
            },
          ]
        : []),
    ],
    write: {
      host: config('dbWriteHost'),
      port: config('dbPort'),
      username: config('dbWriteUsername'),
      password: config('dbWritePassword'),
    },
  },
  dialectOptions: {
    ssl:
      (!!config('dbSSLCA') && {
        rejectUnauthorized: true,
        ca: fs.readSync(config('dbSSLCA')),
      }) ||
      config('dbSSL'),
  },
})

export default sequelize
