import 'reflect-metadata'
import models from '../../dbSequelize/models'
import sequelize from './sequelize'

let associated = false

export const initAssociations = () => {
  if (!associated) {
    Object.values(models).forEach((model) => {
      if (model.associate) {
        model.associate(models)
        return
      }

      // Se não foi definido model.associate, verificar o model.prototype.associate
      // O prototype é uma maneira de definir uma nova função dentro da instância de model
      // Essa abordagem serve para o TypeScript não ficar acusando erro dentro dos arquivos de model
      if (model.prototype?.associate) {
        model.prototype.associate(models)
        return
      }
    })
    associated = true
  }
}

// don't try to put inside sequelize.js (circular dependency)

export const start = () => {
  initAssociations()
  return models
}
export const destroy = () => sequelize.close()

export const refreshDb = async () => {
  initAssociations()

  const tableNames = Object.values(models).map((model) => model.tableName)
  await sequelize
    .query(`TRUNCATE TABLE ${tableNames.join(', ')} CASCADE`, null, {
      raw: true,
    })
    .catch(() => {})

  return models
}

export default start
