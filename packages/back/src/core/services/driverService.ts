/* eslint-disable import/no-cycle */
import * as whatsappRemoteRpc from '../rpcConsumers/whatsappRemoteRpc'
import * as queuedWhatsappRemoteRpc from '../rpcConsumers/queuedWhatsappRemoteRpc'
import * as whatsappBusinessRpc from '../rpcConsumers/whatsappBusinessRpc'
import * as telegramRpc from '../rpcConsumers/telegramRpc'
import * as smsWavyRpc from '../rpcConsumers/smsWavyRpc'
import * as webchatRpc from '../rpcConsumers/webchatRpc'
import * as emailRpc from '../rpcConsumers/emailRpc'
import * as facebookMessengerRpc from '../rpcConsumers/facebookMessengerRpc'
import serviceResource from '../resources/serviceResource'
import { asyncSingletonFactory } from '../utils/asyncSingleton'
import type { WhatsappWorkerRpcInterface } from '../rpcConsumers/whatsappRemoteRpc'
import NotFoundHttpError from '../utils/error/NotFoundHttpError'
import * as googleBusinessMessageRpc from '../rpcConsumers/googleBusinessMessageRpc'
import * as reclameAquiRpc from '../rpcConsumers/reclameAquiRpc'

const consumerFactoryAsyncSingleton = asyncSingletonFactory()
const getServiceRpcAsyncSingleton = asyncSingletonFactory()

const CONSUMERS_MAP = {
  whatsapp: whatsappRemoteRpc.createConsumer,
  'queued-whatsapp-remote': queuedWhatsappRemoteRpc.createConsumer,
  'whatsapp-business': whatsappBusinessRpc.createConsumer,
  'whatsapp-remote': whatsappRemoteRpc.createConsumer,
  telegram: telegramRpc.createConsumer,
  'sms-wavy': smsWavyRpc.createConsumer,
  webchat: webchatRpc.createConsumer,
  email: emailRpc.createConsumer,
  'facebook-messenger': facebookMessengerRpc.createConsumer,
  instagram: facebookMessengerRpc.createConsumer,
  'google-business-message': googleBusinessMessageRpc.createConsumer,
  'reclame-aqui': reclameAquiRpc.createConsumer,
}

const consumerFactory = (serviceType: string): Promise<whatsappRemoteRpc.WhatsappWorkerRpcConsumer> =>
  consumerFactoryAsyncSingleton(serviceType, CONSUMERS_MAP[serviceType])

const getServiceTypeByIdOrType = async (serviceIdOrType: string): Promise<string> => {
  if (
    [
      'queued-whatsapp-remote',
      'whatsapp-business',
      'whatsapp',
      'whatsapp-remote',
      'telegram',
      'sms-wavy',
      'webchat',
      'email',
      'facebook-messenger',
      'instagram',
      'google-business-message',
      'reclame-aqui',
    ].includes(serviceIdOrType)
  ) {
    return serviceIdOrType
  }

  const service = await serviceResource.findById(serviceIdOrType, {
    cache: true,
  })

  if (!service) throw new NotFoundHttpError(`Service #${serviceIdOrType} does not exists.`)

  return service.type
}

export const getServiceRpc = async (serviceIdOrType: string): Promise<WhatsappWorkerRpcInterface> =>
  getServiceRpcAsyncSingleton(serviceIdOrType, async () => {
    const serviceType = await getServiceTypeByIdOrType(serviceIdOrType)
    return consumerFactory(serviceType)
  })

export default <WhatsappWorkerRpcInterface>new Proxy(
  {},
  {
    get: (target, methodName) => {
      return (serviceId, ...params) =>
        getServiceRpc(serviceId).then((consumer) => consumer[methodName](serviceId, ...params))
    },
  },
)
