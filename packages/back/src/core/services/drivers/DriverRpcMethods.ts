import config from '../../config'
import Manager from './Manager'
import AdapterInterface from './AdapterInterface'

export default class DriverRpcMethods<TAdapter extends AdapterInterface> {
  protected manager: Manager<TAdapter>

  constructor(manager: Manager<TAdapter>) {
    this.manager = manager
  }

  syncGroupParticipants = async (serviceId: string, contactId: string) =>
    (await this.manager.getAdapter(serviceId)).syncGroupParticipantsById(contactId)

  contactExists = async (serviceId: string, contactId: string) =>
    (await this.manager.getAdapter(serviceId)).contactExistsById(contactId, serviceId)

  getGroups = async (serviceId: string) => (await this.manager.getAdapter(serviceId)).getGroups()

  createGroup = async (serviceId: string, name: string, participantIds: string[]) =>
    (await this.manager.getAdapter(serviceId)).createGroup(name, participantIds)

  getGroupParticipants = async (serviceId: string, contactId: string) =>
    (await this.manager.getAdapter(serviceId)).getGroupParticipants(contactId)

  addParticipantGroup = async (serviceId: string, idGroup: string, idParticipant: string) =>
    (await this.manager.getAdapter(serviceId)).addParticipantGroup(idGroup, idParticipant)

  removeParticipantGroup = async (serviceId: string, idGroup: string, idParticipant: string) =>
    (await this.manager.getAdapter(serviceId)).removeParticipantGroup(idGroup, idParticipant)

  promoteParticipantAdminGroup = async (serviceId: string, idGroup: string, idParticipant: string) =>
    (await this.manager.getAdapter(serviceId)).promoteParticipantAdminGroup(idGroup, idParticipant)

  demoteParticipantAdminGroup = async (serviceId: string, idGroup: string, idParticipant: string) =>
    (await this.manager.getAdapter(serviceId)).demoteParticipantAdminGroup(idGroup, idParticipant)

  getValidId = async (serviceId: string, contactId: string) =>
    (await this.manager.getAdapter(serviceId)).getValidId(contactId)

  syncContact = async (serviceId: string, contactId: string) =>
    (await this.manager.getAdapter(serviceId)).syncContactById(contactId).then(Boolean)

  loadEarlierMessages = async (serviceId: string, contactId: string, timestamp: number) =>
    (await this.manager.getAdapter(serviceId)).loadEarlierMessagesById(contactId, timestamp).then(Boolean)

  sendAndSave = async (serviceId: string, data, options) =>
    (await this.manager.getAdapter(serviceId)).sendAndSave(data, options)

  start = async (serviceId: string) => (await this.manager.getAdapter(serviceId)).start().then(Boolean)

  refresh = async (serviceId: string) => (await this.manager.getAdapter(serviceId)).refresh(serviceId).then(Boolean)

  newToken = async (serviceId: string) => (await this.manager.getAdapter(serviceId)).newToken().then(Boolean)

  refreshTemplates = async (serviceId: string) => (await this.manager.getAdapter(serviceId)).refreshTemplates()

  createTemplate = async (serviceId: string, templateId: string) =>
    (await this.manager.getAdapter(serviceId)).createTemplate(templateId)

  deleteTemplate = async (serviceId: string, templateId: string) =>
    (await this.manager.getAdapter(serviceId)).deleteTemplate(templateId)

  takeover = async (serviceId: string) => (await this.manager.getAdapter(serviceId)).takeover().then(Boolean)

  shutdown = async (serviceId: string) => (await this.manager.getAdapter(serviceId)).destroy().then(() => true)

  restart = async (serviceId: string) =>
    (await this.manager.getAdapter(serviceId))
      .destroy()
      .then(() => this.manager.kill(serviceId))
      .then(async () => (await this.manager.getAdapter(serviceId)).start())
      .then(Boolean)

  isConnected = async (serviceId: string) => (await this.manager.getAdapter(serviceId)).isConnected().then(Boolean)

  logout = async (serviceId: string) => (await this.manager.getAdapter(serviceId)).logout().then(Boolean)

  screenshot = async (serviceId: string): Promise<string> => (await this.manager.getAdapter(serviceId)).screenshot()

  ping = async () => ({ pong: true })

  kill = async (serviceId: string): Promise<void> => this.manager.kill(serviceId)

  getMeta = async () => ({
    version: config('version'),
  })

  revokeMessageById = async (serviceId: string, messageId: string) =>
    (await this.manager.getAdapter(serviceId)).revokeMessageById(messageId)

  syncMessageFileById = async (serviceId: string, messageId: string) =>
    (await this.manager.getAdapter(serviceId)).syncMessageFileById(messageId).then(() => true)

  webhook = async (serviceId, data, options) => (await this.manager.getAdapter(serviceId)).webhook(data, options)

  processReceivedFile = async (serviceId: string, data: any): Promise<void> =>
    (await this.manager.getAdapter(serviceId)).processReceivedFile(data)

  processSentFile = async (serviceId: string, data: any): Promise<void> =>
    (await this.manager.getAdapter(serviceId)).processSentFile(data)

  sendMessageToBroker = async (serviceId: string, data: any): Promise<void> => {
    return (await this.manager.getAdapter(serviceId)).sendMessageToBroker(data)
  }

  sendReactionByMessage = async (
    serviceId: string,
    messageId: string,
    reactionEmojiRendered: string,
    reactionCode?: string,
  ) => (await this.manager.getAdapter(serviceId)).sendReactionByMessage(messageId, reactionEmojiRendered, reactionCode)

  revokeReactionByMessage = async (serviceId: string, messageId: string) =>
    (await this.manager.getAdapter(serviceId)).revokeReactionByMessage(messageId)
}
