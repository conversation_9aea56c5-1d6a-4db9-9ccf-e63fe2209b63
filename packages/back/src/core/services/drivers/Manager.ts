import Container from '../../utils/Container'
import AdapterInterface from './AdapterInterface'

export default abstract class Manager<TAdapter extends AdapterInterface> {
  container: Container<TAdapter>

  constructor() {
    this.container = new Container()
    this.container.setResolver('*', this.createAdapter)
  }

  async getAdapter(serviceId: string, dontCreate: boolean = false): Promise<TAdapter> {
    return this.container.get(serviceId, dontCreate)
  }

  async kill(serviceId: string): Promise<void> {
    const adapter = await this.getAdapter(serviceId, true)
    if (adapter) await adapter.kill()
    this.container.remove(serviceId)
  }

  start(): Promise<void> {}

  abstract createAdapter(serviceId: string): Promise<TAdapter>
}
