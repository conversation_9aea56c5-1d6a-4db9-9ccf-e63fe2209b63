import { Container, Inject, Service } from 'typedi'
import RequestContextCls from '../cls/RequestContextCls'
import QueueManagerDispatcher from '../jobs/queue/QueueManagerDispatcher'
import Config from '../config/Config'
import configValues from '../../configValues'
import Logger from '../logs/Logger'
import { EventEmitter } from 'events'
import messageResource from '../../resources/messageResource'
import contactResource from '../../resources/contactResource'
import serviceResource from '../../resources/serviceResource'
import accountResource from '../../resources/accountResource'
import userResource from '../../resources/userResource'
import tagResource from '../../resources/tagResource'
import webhookResource from '../../resources/webhookResource'
import campaignResource from '../../resources/campaignResource'
import botResource from '../../resources/botResource'
import ticketResource from '../../resources/ticketResource'
import roleResource from '../../resources/roleResource'
import personResource from '../../resources/personResource'
import departmentResource from '../../resources/departmentResource'
import organizationResource from '../../resources/organizationResource'
import ticketTopicResource from '../../resources/ticketTopicResource'
import quickRepliesResources from '../../resources/quickReplyResource'
import oAuthAccessTokenResource from '../../resources/oAuthAccessTokenResource'
import questionResource from '../../resources/questionResource'
import scheduleResource from '../../resources/scheduleResource'
import notificationResource from '../../resources/notificationResource'
import customFieldsResource from '../../resources/customFieldsResource'
import timetableResource from '../../resources/timetableResource'
import integrationResource from '../../resources/integrationResource'
import distributionResource from '../../resources/distributionResource'
import WhatsappEventResource from '../../../microServices/workers/jobs/whatsapp/adapterLowLevel/WhatsappEventResource'
// import whatsappBusinessTemplateResource from '../../resources/whatsappBusinessTemplateResource'
import acceptanceTermsResource from '../../resources/acceptanceTermsResource'
import stickerResource from '../../resources/stickerResource'
import blockMessageRuleResource from '../../resources/blockMessageRuleResource'
import pipelineResource from '../../resources/pipelineResource'
import cardResource from '../../resources/cardResource'

export type EventResource = {
  getEvents(): string[]
  getEmitter(): EventEmitter
  build(data: any, options: { isNewRecord: boolean }): any
  emit(event: string, data: any): void
  findById?(id: string, options?: any): Promise<any>
}

export const resourcesMap: { [key: string]: EventResource } = {
  message: messageResource,
  contact: contactResource,
  service: serviceResource,
  account: accountResource,
  user: userResource,
  tag: tagResource,
  webhook: webhookResource,
  campaign: campaignResource,
  bot: botResource,
  ticket: ticketResource,
  role: roleResource,
  people: personResource,
  department: departmentResource,
  organization: organizationResource,
  ticketTopics: ticketTopicResource,
  quickReplies: quickRepliesResources,
  accessToken: oAuthAccessTokenResource,
  question: questionResource,
  schedule: scheduleResource,
  notifications: notificationResource,
  customFields: customFieldsResource,
  timetable: timetableResource,
  integration: integrationResource,
  distribution: distributionResource,
  whatsapp: Container.get(WhatsappEventResource),
  acceptanceTerms: acceptanceTermsResource,
  sticker: stickerResource,
  blockMessageRule: blockMessageRuleResource,
  pipeline: pipelineResource,
  card: cardResource,
}

@Service()
export default class EventsDispatcher {
  @Inject()
  protected requestContextCls: RequestContextCls

  @Inject()
  protected dispatcher: QueueManagerDispatcher

  @Inject()
  protected logger: Logger

  @Inject()
  protected config: Config<typeof configValues>

  protected handlers: { resource: EventResource; event: string; handler: (event: string, eventData: any) => void }[] =
    []

  start() {
    Object.entries(resourcesMap).forEach(([resourceName, resource]) => {
      const events = resource.getEvents()
      const emitter = resource.getEmitter()

      for (const eventAction of events) {
        const event = `${resourceName}.${eventAction}`

        const handler = (eventData: any) => {
          if (Array.isArray(eventData)) {
            eventData.forEach((data) => this.handle(event, data))
            return
          }
          return this.handle(event, eventData)
        }

        emitter.on(eventAction, handler)
        this.handlers.push({ resource, event, handler })
      }
    })
  }

  stop() {
    for (const { resource, event, handler } of this.handlers) {
      resource.getEmitter().off(event, handler)
    }
  }

  protected handle(event: string, eventData: any) {
    this.logger.log('Starting event dispatcher: %s - Id: %s - ContactId: %s', 'info', [
      event,
      eventData?.id,
      eventData?.contactId,
    ])

    const metadata = this.requestContextCls.getContext()

    const resourceName = event.split('.')[0]
    const eventType = event.split('.')[1]

    let data = eventData

    if (eventData.id && ['created', 'updated', 'destroyed'].includes(eventType)) {
      data = {
        id: eventData.id,
        accountId: eventData.accountId,
      }
    }

    const payload = {
      event,
      fromApp: this.config.get('appName'),
      fromInstanceId: this.config.get('instanceId'),
      data,
      metadata,
    }

    const accountId = resourceName === 'account' ? eventData.id : eventData.accountId
    const serviceId = resourceName === 'service' ? eventData.id : eventData.serviceId
    const contactId = resourceName === 'contact' ? eventData.id : eventData.contactId
    const ticketId = resourceName === 'ticket' ? eventData.id : eventData.ticketId || eventData.currentTicketId

    const hashKey = `${accountId || 'default'}_${contactId || ticketId || serviceId || resourceName}`

    return this.dispatcher.dispatch(event, payload, { hashKey })
  }
}
