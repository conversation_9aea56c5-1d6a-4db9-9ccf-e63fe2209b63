import { Service, Inject } from 'typedi'
import * as xlsLib from 'excel4node'
import Logger from '../logs/Logger'
import serviceEventsResource from '../../resources/serviceEventsResource'
import formatDate from '../../utils/date/formatDate'

@Service()
export default class ServiceEventsBlockMessageRules {
  @Inject()
  protected logger: Logger

  getWorksheetBlockMessageRules(language: string) {
    return {
      'pt-BR': 'Status do bloqueio por DDD',
      'en-US': 'Block status by area code',
      es: 'Estado del bloqueo por código de área',
    }[language || 'pt-BR']
  }

  getHeadersBlockMessageRules(language: string) {
    return [
      {
        title: {
          'pt-BR': 'Início',
          'en-US': 'Start',
          es: 'Inicio',
        }[language || 'pt-BR'],
        width: 20,
      },
      {
        title: {
          'pt-BR': 'Fim',
          'en-US': 'End',
          es: 'Fin',
        }[language || 'pt-BR'],
        width: 20,
      },
      {
        title: {
          'pt-BR': 'Status',
          'en-US': 'Status',
          es: 'Estado',
        }[language || 'pt-BR'],
        width: 12,
      },
      {
        title: {
          'pt-BR': 'Descrição',
          'en-US': 'Description',
          es: 'Descripción',
        }[language || 'pt-BR'],
        width: 35,
      },
    ]
  }

  getStatusBlockMessageRules(reason: string, language: string): string {
    if (['enabled_block_message_rules_active', 'enabled_unblock_by_receive_message'].includes(reason)) {
      return {
        'pt-BR': 'Habilitado',
        'en-US': 'Enabled',
        es: 'Activado',
      }[language || 'pt-BR']
    }

    if (['disabled_block_message_rules_active', 'disabled_unblock_by_receive_message'].includes(reason)) {
      return {
        'pt-BR': 'Desabilitado',
        'en-US': 'Disabled',
        es: 'Desactivado',
      }[language || 'pt-BR']
    }

    return ''
  }

  getDescriptionBlockMessageRules(reason: string, language: string): string {
    if (['enabled_block_message_rules_active', 'disabled_block_message_rules_active'].includes(reason)) {
      return {
        'pt-BR': 'Bloqueio do chat por horário',
        'en-US': 'Chat blocking by time',
        es: 'Bloqueo de chat por tiempo',
      }[language || 'pt-BR']
    }

    if (['enabled_unblock_by_receive_message', 'disabled_unblock_by_receive_message'].includes(reason)) {
      return {
        'pt-BR': 'Desbloqueio com mensagem receptiva',
        'en-US': 'Unlocking with incoming message',
        es: 'Desbloqueo con mensaje entrante',
      }[language || 'pt-BR']
    }

    return ''
  }

  async exportBlockMessageRules(query, userLanguage, account) {
    const events = await serviceEventsResource.findMany(query)

    if (!events?.length) {
      this.logger.log('Service events block message rules not found', 'warn')
      return
    }

    const wb = new xlsLib.Workbook()

    const ws = wb.addWorksheet(this.getWorksheetBlockMessageRules(userLanguage))

    const headers = this.getHeadersBlockMessageRules(userLanguage)

    headers.forEach(({ title, width }, indexColumn) => {
      ws.cell(1, indexColumn + 1).string(title)
      ws.column(indexColumn + 1).setWidth(width)
    })

    events.forEach((event, indexLine) => {
      const startedAt = event.startedAt
        ? formatDate(event.startedAt, 'dd/MM/yyyy HH:mm:ss', account?.settings?.timezone)
        : ''
      const endedAt = event.endedAt ? formatDate(event.endedAt, 'dd/MM/yyyy HH:mm:ss', account?.settings?.timezone) : ''

      ws.cell(indexLine + 2, 1).string(startedAt)
      ws.cell(indexLine + 2, 2).string(endedAt)
      ws.cell(indexLine + 2, 3).string(this.getStatusBlockMessageRules(event.reason, userLanguage))
      ws.cell(indexLine + 2, 4).string(this.getDescriptionBlockMessageRules(event.reason, userLanguage))
    })

    const result = await wb.writeToBuffer().then((buffer) => Buffer.from(buffer, 'binary'))

    return result
  }
}
