import Axios, { AxiosRequestConfig } from 'axios'
import { Service, Inject } from 'typedi'
import FormData from 'form-data'
import pick from 'lodash/pick'
import Qs from 'qs'
import { MessageInstance } from '../../dbSequelize/models/Message'
import config from '../../../core/configValues'
import messageResource from '../../resources/messageResource'
import creditMovementResource from '../../resources/creditMovementResource'
import TranscriptService from '../../../core/services/transcript'
import { SMSCreditOrigin } from '../../../core/dbSequelize/models/CreditMovement'

export type Data = {
  accountId: string
  type: 'urls' | 'files' | 'messages'
  name?: string
}

export type Request = {
  documents: Array<{
    text: string
    meta: { name: string; data: Date }
  }>
}

@Service()
export default class HaystackIaApi {
  @Inject()
  transcriptService: TranscriptService

  async getBearerToken(): Promise<string> {
    try {
      const data = Qs.stringify({
        username: config?.haystackIa?.username,
        password: config?.haystackIa?.password,
      })

      const url = `${config?.haystackIa?.url}/v1/auth/token`
      const response = await Axios.post(url, data)
      return response?.data?.access_token
    } catch (error) {
      throw new Error(`Failed to get bearer token: ${error.message ?? error?.response?.data?.message}`)
    }
  }

  async getConfig(payload?: any, type?: string): Promise<AxiosRequestConfig> {
    try {
      const token = await this.getBearerToken()
      return {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type':
            !type || type === 'messages'
              ? 'application/json'
              : `multipart/form-data; boundary=${payload.getBoundary()}`,
        },
      }
    } catch (error) {
      throw new Error(`Failed to get config: ${error.message}`)
    }
  }

  async createKnowledge(source: any, data: Data) {
    try {
      const url = `${config?.haystackIa?.url}/v1/vectorstore/collections/${data?.accountId}/documents/${data?.type}`
      const payload = this.getPayload(source, data)
      const response = await Axios.post(url, payload, await this.getConfig(payload, data?.type))
      const values = Object.values(response?.data?.details)?.[0]

      return { docIds: values?.doc_ids ?? [], sourceId: values?.source_id }
    } catch (err) {
      throw new Error(err?.response?.data?.detail ?? err?.message)
    }
  }

  async findKnowledge(documentIds: Array<string>, accountId: string) {
    try {
      const url = `${config?.haystackIa?.url}/v1/vectorstore/collections/${accountId}/documents/filter-messages`
      const payload = {
        filters: {
          field: 'id',
          operator: 'in',
          value: documentIds,
        },
      }
      const response = await Axios.post(url, payload, await this.getConfig(payload))
      return (response?.data ?? []).map((doc: any) => pick(doc, ['id', 'content', 'meta', 'blob']))
    } catch (error) {
      throw new Error(`Failed to search for knowledge: ${error.message}`)
    }
  }

  async deleteKnowledgeDocs(documentIds: Array<string>, accountId: string): Promise<void> {
    try {
      const url = `${config?.haystackIa?.url}/v1/vectorstore/collections/${accountId}/documents/delete-messages`
      const payload = { document_ids: documentIds }
      const response = await Axios.delete(url, { ...(await this.getConfig()), data: payload })
      if (!response?.data?.message) {
        throw new Error(`Failed to delete documents of knowledge. DocumentIds: ${documentIds.join(',')}`)
      }
    } catch (error) {
      throw new Error(`Failed to delete documents of knowledge: ${error.message}`)
    }
  }

  async suggestResponseByMessage(
    messageId: string,
    accountId: string,
    outsideTheKnowledgeBase: boolean,
    origin?: SMSCreditOrigin,
  ): Promise<{ message: string; subjectNotFound?: boolean }> {
    try {
      const url = `${config?.haystackIa?.url}/v2/copilot/suggest-response`
      const message = await messageResource.findById(messageId, {
        attributes: ['isFromMe', 'isFromBot', 'text', 'type', 'accountId', 'serviceId'],
      })

      const messageText =
        ['audio', 'ptt'].includes(message?.type) && !message?.text
          ? await this.transcriptService.transcribe(messageId, false, origin)
          : await messageResource.decryptMessageText(message)

      if (
        typeof messageText === 'string' &&
        messageText.length > 0 &&
        ['TRANSCRIPTION_DISABLED', 'TRANSCRIPTION_LIMIT_REACHED'].includes(messageText)
      ) {
        return { message: messageText }
      }

      const payload = {
        identification_id: accountId,
        messages: [{ ...message?.dataValues, text: messageText }],
        outsideTheKnowledgeBase: outsideTheKnowledgeBase,
      }

      const response = await Axios.post(url, payload, await this.getConfig())
      if (!response?.data?.message) {
        throw new Error(`Failed to suggest a response. MessageId: ${messageId}`)
      }

      await creditMovementResource.createDebit({
        accountId,
        serviceType: 'copilot',
        amount: 1,
        origin: 'single',
        serviceId: message?.serviceId,
      })

      return {
        message: response?.data?.message,
        subjectNotFound: response?.data?.subjectNotFound === true,
      }
    } catch (error) {
      if (error.response?.data?.detail) {
        throw new Error(error.response?.data?.detail)
      }
      throw new Error(`Failed to suggest a response: ${error.message}`)
    }
  }

  async suggestResponseByTicket(
    messages: MessageInstance[],
    outsideTheKnowledgeBase: boolean,
    origin?: SMSCreditOrigin,
  ): Promise<{
    message: string
    subjectNotFound: boolean
  }> {
    try {
      const payload = {
        identification_id: messages?.[0]?.accountId,
        messages: messages,
        outsideTheKnowledgeBase: outsideTheKnowledgeBase,
      }

      const url = `${config?.haystackIa?.url}/v2/copilot/suggest-response`

      const response = await Axios.post(url, payload, await this.getConfig())
      if (!response?.data?.message) {
        throw new Error(`Failed to suggest a response. ticketId: ${messages?.[0]?.ticketId}`)
      }

      await creditMovementResource.createDebit({
        accountId: messages?.[0]?.accountId,
        serviceType: 'copilot',
        amount: 1,
        origin: 'single',
        serviceId: messages?.[0]?.serviceId,
      })

      return {
        message: response?.data?.message,
        subjectNotFound: response?.data?.subjectNotFound === true,
      }
    } catch (error) {
      if (error.response?.data?.detail) {
        throw new Error(error.response?.data?.detail)
      }
      throw new Error(`Failed to suggest a response: ${error.message}`)
    }
  }

  private getPayload(source: any, data: any): FormData | Request {
    switch (data?.type) {
      case 'urls':
        const dataUrls = new FormData()
        dataUrls.append('urls', source)
        dataUrls.append('metadata', JSON.stringify({ name: data?.name, data: new Date() }))
        return dataUrls
      case 'files':
        const dataFiles = new FormData()
        dataFiles.append('file', source, data?.name)
        dataFiles.append('metadata', JSON.stringify({ name: data?.name, data: new Date() }))
        return dataFiles
      case 'messages':
        return { documents: [{ text: source, meta: { name: data?.name, data: new Date() } }] }
      default:
        throw new Error(`Unknown type: ${data?.type}`)
    }
  }
}
