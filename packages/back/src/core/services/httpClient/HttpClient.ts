// HttpClient.ts
import axios, { AxiosInstance } from 'axios'
import { Service } from 'typedi'
import http from 'http'
import https from 'https'
import {
  IHttpClient,
  HttpClientConfig,
  HttpClientRequestConfig,
  HttpClientResponse,
  HttpClientInterceptors,
} from './types'

@Service()
export class HttpClient implements IHttpClient {
  private axiosInstance: AxiosInstance

  private httpAgent: http.Agent

  private httpsAgent: https.Agent

  constructor() {
    // Create default HTTP agents
    this.httpAgent = new http.Agent({
      keepAlive: true,
      keepAliveMsecs: 60_000,
      maxSockets: 100,
    })

    this.httpsAgent = new https.Agent({
      keepAlive: true,
      keepAliveMsecs: 60_000,
      maxSockets: 100,
    })

    // Initialize the Axios instance with the default agents
    this.axiosInstance = this.createAxiosInstance()
  }

  /**
   * Creates an Axios instance with the provided configuration or default values
   */
  private createAxiosInstance(config?: HttpClientConfig): AxiosInstance {
    // Default config with current agents
    const defaultConfig: HttpClientConfig = {
      httpAgent: this.httpAgent,
      httpsAgent: this.httpsAgent,
      timeout: 60_000, // Default timeout of 10 seconds
    }

    // Create and return the Axios instance with merged config
    const axiosInstance = axios.create({
      ...defaultConfig,
      ...config,
    })

    // Apply retry logic if configured
    if (config?.retryConfig) {
      this.setupRetryInterceptor(axiosInstance, config.retryConfig)
    }

    return axiosInstance
  }

  // Setup retry logic that can be applied to an Axios instance
  private setupRetryInterceptor(instance: AxiosInstance, retryConfig: HttpClientConfig['retryConfig']) {
    if (!retryConfig) return

    instance.interceptors.response.use(undefined, async (error) => {
      const { config, response } = error

      // Skip if this request has already been retried or isn't configured for retry
      if (!config || config.__retryCount === undefined) {
        config.__retryCount = 0
      } else {
        config.__retryCount++
      }

      // Check if we should retry the request
      if (
        config.__retryCount < retryConfig.maxRetries &&
        response &&
        retryConfig.retryStatusCodes.includes(response.status)
      ) {
        // Wait before retrying
        await new Promise((resolve) => setTimeout(resolve, retryConfig.retryDelay))
        return instance(config)
      }

      return Promise.reject(error)
    })
  }

  // Mirror the Axios API
  async request<T = any>(config: HttpClientRequestConfig): Promise<HttpClientResponse<T>> {
    return this.axiosInstance.request<T>(config)
  }

  async get<T = any>(url: string, config?: HttpClientRequestConfig): Promise<HttpClientResponse<T>> {
    return this.axiosInstance.get<T>(url, config)
  }

  async delete<T = any>(url: string, config?: HttpClientRequestConfig): Promise<HttpClientResponse<T>> {
    return this.axiosInstance.delete<T>(url, config)
  }

  async head<T = any>(url: string, config?: HttpClientRequestConfig): Promise<HttpClientResponse<T>> {
    return this.axiosInstance.head<T>(url, config)
  }

  async options<T = any>(url: string, config?: HttpClientRequestConfig): Promise<HttpClientResponse<T>> {
    return this.axiosInstance.options<T>(url, config)
  }

  async post<T = any>(url: string, data?: any, config?: HttpClientRequestConfig): Promise<HttpClientResponse<T>> {
    return this.axiosInstance.post<T>(url, data, config)
  }

  async put<T = any>(url: string, data?: any, config?: HttpClientRequestConfig): Promise<HttpClientResponse<T>> {
    return this.axiosInstance.put<T>(url, data, config)
  }

  async patch<T = any>(url: string, data?: any, config?: HttpClientRequestConfig): Promise<HttpClientResponse<T>> {
    return this.axiosInstance.patch<T>(url, data, config)
  }

  // Expose interceptors
  get interceptors(): HttpClientInterceptors {
    return this.axiosInstance.interceptors
  }

  /**
   * Creates a new HttpClient instance with the provided configuration
   * If no HTTP or HTTPS agents are specified, it passes the current instance's agents
   */
  create(config?: HttpClientConfig): HttpClient {
    const newHttpClient = new HttpClient()

    // If httpAgent/httpsAgent are not specified, use the ones from the current instance
    const configWithAgents = {
      ...config,
      // Only override agent if not specified in config
      httpAgent:
        config?.httpAgent ||
        // Only create new agent if httpAgentOptions is provided
        (config?.httpAgentOptions ? new http.Agent(config.httpAgentOptions) : this.httpAgent),
      httpsAgent:
        config?.httpsAgent ||
        // Only create new agent if httpsAgentOptions is provided
        (config?.httpsAgentOptions ? new https.Agent(config.httpsAgentOptions) : this.httpsAgent),
    }

    // Store the agents in the new instance
    newHttpClient.httpAgent = configWithAgents.httpAgent as http.Agent
    newHttpClient.httpsAgent = configWithAgents.httpsAgent as https.Agent

    // Create the axios instance with the agents
    newHttpClient.axiosInstance = axios.create({
      ...configWithAgents,
    })

    // Apply retry logic if configured
    if (config?.retryConfig) {
      this.setupRetryInterceptor(newHttpClient.axiosInstance, config.retryConfig)
    }

    return newHttpClient
  }
}
