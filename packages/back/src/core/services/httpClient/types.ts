// HttpClientTypes.ts
import type {
  AxiosResponse as BaseAxiosResponse,
  AxiosRequestConfig as BaseAxiosRequestConfig,
  AxiosError as BaseAxiosError,
  AxiosInterceptorManager,
} from 'axios'

// Re-export types that we want to maintain compatibility with
export type HttpClientRequestConfig = BaseAxiosRequestConfig
export type HttpClientResponse<T = any> = BaseAxiosResponse<T>
export type HttpClientError<T = any> = BaseAxiosError<T>

// Custom interceptor types
export interface HttpClientRequestInterceptor {
  onFulfilled?: (value: HttpClientRequestConfig) => HttpClientRequestConfig | Promise<HttpClientRequestConfig>
  onRejected?: (error: any) => any
}

export interface HttpClientResponseInterceptor<T = any> {
  onFulfilled?: (value: HttpClientResponse<T>) => HttpClientResponse<T> | Promise<HttpClientResponse<T>>
  onRejected?: (error: HttpClientError<T>) => any
}

// Interceptor manager types
export interface HttpClientInterceptors {
  request: AxiosInterceptorManager<HttpClientRequestConfig>
  response: AxiosInterceptorManager<HttpClientResponse>
}

// HTTP method enum
export enum HttpMethod {
  GET = 'get',
  POST = 'post',
  PUT = 'put',
  DELETE = 'delete',
  HEAD = 'head',
  OPTIONS = 'options',
  PATCH = 'patch',
}

// Agent options
export interface HttpAgentOptions {
  keepAlive?: boolean
  keepAliveMsecs?: number
  maxSockets?: number
  maxFreeSockets?: number
  timeout?: number
}

// Enhanced client configuration with our custom options
export interface HttpClientConfig extends BaseAxiosRequestConfig {
  httpAgentOptions?: HttpAgentOptions
  httpsAgentOptions?: HttpAgentOptions
  retryConfig?: {
    maxRetries: number
    retryDelay: number
    retryStatusCodes: number[]
  }
}

// Interface for the HttpClient class
export interface IHttpClient {
  request<T = any>(config: HttpClientRequestConfig): Promise<HttpClientResponse<T>>
  get<T = any>(url: string, config?: HttpClientRequestConfig): Promise<HttpClientResponse<T>>
  delete<T = any>(url: string, config?: HttpClientRequestConfig): Promise<HttpClientResponse<T>>
  head<T = any>(url: string, config?: HttpClientRequestConfig): Promise<HttpClientResponse<T>>
  options<T = any>(url: string, config?: HttpClientRequestConfig): Promise<HttpClientResponse<T>>
  post<T = any>(url: string, data?: any, config?: HttpClientRequestConfig): Promise<HttpClientResponse<T>>
  put<T = any>(url: string, data?: any, config?: HttpClientRequestConfig): Promise<HttpClientResponse<T>>
  patch<T = any>(url: string, data?: any, config?: HttpClientRequestConfig): Promise<HttpClientResponse<T>>

  interceptors: HttpClientInterceptors
  create(config?: HttpClientConfig): IHttpClient
}
