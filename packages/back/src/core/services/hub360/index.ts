import config from '../../configValues'
import BalanceResponse from './interfaces/BalanceResponse'
import moment from 'moment'
import { format, addMonths } from 'date-fns'
import { Container } from 'typedi'
import Logger from '../logs/Logger'
import { HttpClient } from '../httpClient/HttpClient'

const hub360Api = config.hub360Api
const endpoints = {
  auth: `${hub360Api.baseUrl}/token`,
  balance: `${hub360Api.baseUrl}/partners/`,
  channel: `${hub360Api.baseUrl}/partners/`,
}

const logger = Container.get(Logger)

const log = logger.log

class Hub360 {
  accessToken: string

  protected getHttpClient() {
    return Container.get(HttpClient)
  }

  async auth(hub360PartnerId = '5urmEBPA'): Promise<boolean> {
    const getPayload = () => {
      if (hub360PartnerId === hub360Api.partnerId) {
        return {
          username: hub360<PERSON><PERSON>.username,
          password: hub360Api.password,
        }
      }
      return {
        username: hub360Api.usernameExternalBilling,
        password: hub360Api.passwordExternalBilling,
      }
    }
    const payload = getPayload()

    try {
      const response = await this.getHttpClient().post(endpoints.auth, payload)

      if (!response?.data?.access_token) {
        log('Error to generate hub360 token')
        return false
      }

      this.accessToken = response.data.access_token
      return true
    } catch (err) {
      log(`Error to generate hub360 token: ${err?.response?.statusText || 'Generic error'}`)
      return false
    }
  }

  getHeader() {
    return {
      headers: {
        Authorization: `Bearer ${this.accessToken}`,
        'Content-Type': 'application/json',
      },
    }
  }

  async getClientId(idHub360: string): Promise<string> {
    try {
      if (!this.accessToken) {
        if (!(await this.auth())) return null
      }

      const responseChannel = await this.getHttpClient().get(
        `${endpoints.channel}/${idHub360}/channels/?filters={"id": "${idHub360}"}`,
        this.getHeader(),
      )

      const client = responseChannel.data?.partner_channels?.[0]?.client

      if (!client?.id) {
        log(`Error to get client ${idHub360}: Client not found`)
        return null
      }

      return client?.id
    } catch (err) {
      log(err)
      log(`Error to get client ${idHub360}: ${err?.response?.statusText || 'Generic error'}`)
      return null
    }
  }

  async balance(idPartnerHub360: string, idClient: string, query: any): Promise<BalanceResponse> {
    try {
      if (!this.accessToken) {
        if (!(await this.auth())) return null
      }

      const getFilters = () => {
        if (query.groupedBy === 'month') {
          const date = new Date(query.year, query.month, 1)
          const startPeriod = addMonths(date, -1)
          const startMonth = format(startPeriod, 'MM')
          const startYear = format(startPeriod, 'yyyy')
          const startDate = moment(`${startYear}-${startMonth}-01 00:00:00`)
          const endDate = startDate.clone().endOf('month')
          return `start_date=${startDate.format('X')}&end_date=${endDate.format('X')}&granularity=month`
        }

        return `start_date=${query.startDay}&end_date=${query.endDay}&granularity=day`
      }

      const filters = getFilters()
      const response = await this.getHttpClient().get(
        `${endpoints.balance}/${idPartnerHub360}/clients/${idClient}/info/balance?${filters}`,
        this.getHeader(),
      )
      return response.data
    } catch (err) {
      log(`Error to get balance client ${idClient}: ${err?.response?.statusText || 'Generic error'}`)
      return null
    }
  }

  async updateApiKey(channelId: string, hub360PartnerId = '5urmEBPA') {
    try {
      if (!this.accessToken) {
        if (!(await this.auth(hub360PartnerId))) return null
      }

      const response = await this.getHttpClient().post(
        `${endpoints.channel}/${hub360PartnerId}/channels/${channelId}/api_keys`,
        {},
        this.getHeader(),
      )

      return response.data
    } catch (err) {
      log(`Error to create api-key for channel ${channelId}: ${err?.response?.statusText || 'Generic error'}`)
      return null
    }
  }
}

export default new Hub360()
