import config from '../../config'
import userResource from '../../resources/userResource'
import reportError from '../logs/reportError'
import { dataAuthLogin, dataUserArchive } from './types'
import { Container } from 'typedi'
import Logger from '../logs/Logger'
import { HttpClient } from '../httpClient/HttpClient'

const url = `${config('internalChatUrl')}`

const logger = Container.get(Logger)

const log = logger.log

const getHttpClient = () => Container.get(HttpClient)

const authLogin = async (data: dataAuthLogin) => {
  const { accountId, email, password } = data

  return getHttpClient()
    .post(`${url}/auth/login`, {
      accountId,
      email,
      password,
    })
    .then((res) => res.data)
    .catch(reportError)
}

const createUserFromInternalChat = async (data) => {
  const { userId, accountId, roles } = data
  const { name, email, password } = await userResource.findById(userId)

  await getHttpClient()
    .post(`${url}/contacts-webhook`, {
      name,
      email,
      password,
      accountId,
      roles,
    })
    .then((res) => res?.status === 200 && log(`User created ${email}`))
    .catch(reportError)
}

const enableOrDisableUserToInternalChat = async (data: dataUserArchive) => {
  const { accountId, email, archive, token } = data

  await getHttpClient()
    .post(
      `${url}/contacts-webhook/archive`,
      {
        accountId,
        email,
        archive,
      },
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      },
    )
    .then((res) => {
      if (res?.status === 200) {
        log(`User ${email}: ${archive ? 'disabled' : 'enable'}`)
      }
    })
    .catch(reportError)
}

const updateUserFromInternalChat = async (data) => {
  const { userId, roles, emailBeforeUpdate } = data
  const { name, email, password, accountId } = await userResource.findById(userId)

  await getHttpClient()
    .put(`${url}/contacts-webhook`, {
      name,
      email,
      emailBeforeUpdate,
      password,
      accountId,
      roles,
    })
    .then((res) => res?.status === 200 && log(`User updated ${email}`))
    .catch(reportError)
}

const internalChatService = {
  authLogin,
  createUserFromInternalChat,
  enableOrDisableUserToInternalChat,
  updateUserFromInternalChat,
}

export default internalChatService
