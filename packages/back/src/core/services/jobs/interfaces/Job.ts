import Class from '../../../utils/types/Class'

export default interface Job {
  handle(payload: any): Promise<any | void>
}

export interface JobClass<T extends Job = Job> extends Class<T> {
  jobName: string
}

type EventJobModel = {
  id: string
  accountId?: string
}

export type EventJobData<T extends EventJobModel = EventJobModel> = {
  id: T['id']
  accountId?: T['accountId']
}

export type EventJobPayload<T extends EventJobModel = EventJobModel> = {
  event: string
  data: EventJobData<T> | EventJobData<T>[]
}
