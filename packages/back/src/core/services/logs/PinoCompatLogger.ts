import Logger, { Level } from './Logger'

const levels = ['debug', 'info', 'warn', 'error', 'critical']

export class PinoCompatLogger {
  constructor(protected logger: Logger, protected minLogLevel: Level = 'debug') {}

  logMsg =
    (level: Level) =>
    (msg: string, ...args: any[]) => {
      if (levels.indexOf(level) < levels.indexOf(this.minLogLevel)) return

      const splatsStrings = ' %o'.repeat(args.length)

      this.logger.log(`${msg}${splatsStrings}`, level, args)
    }

  fatal = this.logMsg('critical')

  error = this.logMsg('error')

  warn = this.logMsg('warn')

  info = this.logMsg('info')

  debug = this.logMsg('debug')

  trace = this.logMsg('debug')
}
