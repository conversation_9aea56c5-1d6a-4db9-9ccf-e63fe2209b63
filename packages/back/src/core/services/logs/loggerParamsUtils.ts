import { createHmac } from 'node:crypto'
import config from '../../config'
import { intersection, isPlainObject } from 'lodash'
import type { Event } from './Logger'

const lgpdKeys = ['password']

const obfuscateKeys = [
  'password',
  'token',
  'access_token',
  'accessToken',
  'refresh_token',
  'refreshToken',
  'passwordHash',
  'password_hash',
]

export const hasLgpdData = (payload?: {}) => {
  if (!payload) return false
  return intersection(Object.keys(payload), lgpdKeys).length > 0
}

export const obfuscateLgpdData = (payload?: {}) => {
  // bypass for dev or test
  if (config('env') !== 'production') return payload

  if (!payload) return payload

  return Object.entries(payload).reduce((obj, [key, value]) => {
    // nested object
    if (isPlainObject(value)) {
      obj[key] = obfuscateLgpdData(value)
      return obj
    }

    // first level
    obj[key] = obfuscateKeys.includes(key) ? '*'.repeat(8) : value
    return obj
  }, {})
}

export const getLogType = (event: Event) => {
  return (
    {
      authn: 'AUD',
      msg: 'AUD',
      user: 'AUD',
    }[event?.name.split('_')[0]] || 'TBS'
  )
}

export const createSignature = (payload: string, secret: string) => {
  return createHmac('sha256', secret).update(payload).digest('base64')
}

export const getHostAttributes = () => {
  const url = new URL(config('publicUrl'))

  return {
    protocol: url.protocol.replace(':', ''),
    hostname: url.hostname,
    host: url.host,
  }
}
