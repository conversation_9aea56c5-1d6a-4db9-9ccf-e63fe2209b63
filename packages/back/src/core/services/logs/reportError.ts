import { Request, Response } from 'express'
import { AxiosError } from 'axios'
import { Container } from 'typedi'
import ValidationError from '../../utils/error/ValidationError'
import Logger from './Logger'
import { TracerToken } from '../tracer/Tracer'

const logger = Container.get(Logger)
const tracer = Container.get(TracerToken)

export default (err: Error | AxiosError, context: { req?: Request; res?: Response } = {}) => {
  if (
    err instanceof ValidationError ||
    ((err as AxiosError)?.response?.status >= 400 && (err as AxiosError)?.response?.status < 500)
  )
    return err

  const { req, res } = context

  const errorToLog = (err as AxiosError)?.response?.data || err

  logger.log('%o', 'error', [errorToLog])

  tracer.captureError(err, {
    response: res,
    request: req,
  })

  return err
}
