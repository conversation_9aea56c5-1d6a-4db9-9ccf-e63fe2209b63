import { Inject, Service } from 'typedi'
import express from 'express'
import { Server } from 'http'
import bodyParser from 'body-parser'
import Logger from '../logs/Logger'
import Config from '../config/Config'

@Service()
export default class HealthCheckHttpServer {
  @Inject()
  protected logger: Logger

  @Inject()
  protected config: Config

  protected httpServer: Server

  async start(appName: string, port: number) {
    if (!port) throw new Error('Port is required.')

    const app = express()

    app.use(bodyParser.json())

    app.get('/health', (req, res) => {
      res.sendStatus(200)
    })

    this.httpServer = await new Promise((resolve) => {
      const server = app.listen(port, () => resolve(server))
    })

    this.logger.log(`Started ${appName} health check API on port ${port}.`)
  }

  async stop() {
    await new Promise((resolve) => this.httpServer.close(resolve))
  }
}
