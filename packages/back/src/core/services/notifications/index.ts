import { Container } from 'typedi'
import { UserInstance } from '../../dbSequelize/models/User'
import queuedAsyncMap from '../../utils/array/queuedAsyncMap'
import { MessageInstance } from '../../dbSequelize/models/Message'
import { decryptTextForAccount } from '../crypt/accountCryptor'
import NotificationResource from '../../resources/notificationResource'
import UserResource from '../../resources/userResource'
import NotificationAnnouncement from './types/NotificationAnnouncement'
import Logger from '../logs/Logger'
import Config from '../config/Config'
import configValues from '../../../core/configValues'
import { HttpClient } from '../httpClient/HttpClient'

const logger = Container.get(Logger)
const config = <Config<typeof configValues>>Container.get(Config)

export default class Notification {
  sendPushNotification = async (infoMessage: MessageInstance, user: UserInstance) => {
    if (infoMessage.contact?.isSilenced) return

    if (
      (infoMessage.origin === null || infoMessage.origin === 'bot' || infoMessage.origin === 'chat') &&
      !infoMessage.isFromMe
    ) {
      const restKey = user.id
      const appID = config.get('oneSignalAppId')
      const midia = infoMessage.type
      let msMidia = ''
      if (!midia) return
      if (midia === 'image') {
        msMidia = 'Uma imagem foi enviada'
      }
      if (midia === 'ptt' || midia === 'audio') {
        msMidia = 'Um áudio foi enviado'
      }
      if (midia === 'video') {
        msMidia = 'Um vídeo foi enviado'
      }
      if (
        midia !== 'video' &&
        midia !== 'ptt' &&
        midia !== 'image' &&
        midia !== 'ticket' &&
        midia !== 'email' &&
        midia !== 'chat'
      ) {
        msMidia = 'Um arquivo foi enviado'
      }
      const { data, name, internalName, id, account, isSilenced, currentTicket, avatar, unread } = infoMessage.contact
      const body = {
        app_id: appID,
        headings: { en: name },
        contents: {
          en:
            infoMessage.type === 'chat' || infoMessage.type === 'email'
              ? decryptTextForAccount(infoMessage.account, infoMessage.text)
              : msMidia,
        },
        include_player_ids: [user.data?.oneSignalToken],
        data: {
          data,
          name,
          internalName,
          id,
          account,
          isSilenced,
          currentTicket,
          avatar,
          unread,
        },
        small_icon: 'ya',
        content_available: !!isSilenced,
        large_icon: avatar != null ? avatar.url : '',
        ios_badgeType: 'Increase',
        ios_badgeCount: unread,
        alert: false,
      }

      if (!currentTicket || !isSilenced) {
        await Container.get(HttpClient)
          .post(config.get('oneSignalUrl'), JSON.stringify(body), {
            headers: {
              authorization: `Basic ${restKey}`,
              'content-type': 'application/json',
            },
          })
          .then(() => {
            logger.log('Notification sent with success!')
          })
          .catch((error) => {
            new Error(`Error during push in ${name}(${id}): ${error.response}`)
          })
      }
    }
  }

  // Enviado pelo aplicativo após o usuário fazer login
  sendNotification = async (data: MessageInstance) => {
    if (!data) {
      logger.log('data info notification is empty', 'warn')
      return
    }
    return queuedAsyncMap(data.account.users, (user) => {
      if (
        data.contact.currentTicket &&
        !data.account.settings.isQueueNotificationActive &&
        user.id === data.contact.currentTicket.userId
      ) {
        return this.sendPushNotification(data, user)
      }
      if (
        (data.contact.currentTicket &&
          data.account.settings.isQueueNotificationActive &&
          user.departments?.some(
            (department) =>
              !data.contact.currentTicket.userId && department.id === data.contact.currentTicket.departmentId,
          )) ||
        user.id === data.contact.currentTicket?.userId
      ) {
        return this.sendPushNotification(data, user)
      }
    })
  }

  createAnnouncement = async (notificationAnnouncement: NotificationAnnouncement) => {
    let users = []

    if (notificationAnnouncement.to === 'admin') {
      users = await UserResource.findAdminUsers(notificationAnnouncement.accountId)
    } else {
      users = await UserResource.findAllUsers(notificationAnnouncement.accountId)
    }

    if (users.length) {
      users.map(async (user) => {
        await NotificationResource.create({
          userId: user.id,
          type: 'announcement',
          text: notificationAnnouncement.text,
          accountId: notificationAnnouncement.accountId,
          image: notificationAnnouncement.image,
        })
      })
    }
  }
}
