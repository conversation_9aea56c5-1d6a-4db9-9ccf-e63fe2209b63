import puppeteer from 'puppeteer-core'
import config from '../../config'
import reportError from '../logs/reportError'

const getBrowser = () => {
  const externalBrowser = config('externalBrowser')
  const browserWSEndpoint = config('browserWSEndpointPdfGenerator')
  const chromeExecutablePath = config('chromeExecutablePath')

  if (externalBrowser) {
    return puppeteer.connect({
      browserWSEndpoint,
    })
  }

  return puppeteer.launch({
    executablePath: chromeExecutablePath,
    headless: true,
    args: ['--no-sandbox', '--disable-setuid-sandbox', '--disable-dev-shm-usage', '--single-process'],
  })
}

const generatePdf = async (body: string, header: string, footer: string): Promise<Buffer> => {
  const browser = await getBrowser()
  const page = await browser.newPage()
  page.setDefaultNavigationTimeout(0)

  // "workaround" pra quando vir URLs parciais
  const frontUrl = config('frontUrl')
  const bodyReplaced = body
    .replace(new RegExp('href="/', 'g'), `href="${frontUrl}/`)
    .replace(new RegExp('src="/', 'g'), `src="${frontUrl}/`)

  await page.setContent(bodyReplaced, { waitUntil: 'networkidle0' })

  const pdfBuffer = await page.pdf({
    format: 'a4',
    printBackground: true,
    scale: 1,
    displayHeaderFooter: true,
    headerTemplate: header,
    footerTemplate: footer,
    margin: {
      top: '2cm',
      bottom: '2cm',
      left: '1cm',
      right: '1cm',
    },
  })

  try {
    await page.close()
    await browser.close()
  } catch (e) {
    reportError(e)
  }

  return pdfBuffer
}

export default generatePdf
