import { Container, Service, Inject } from 'typedi'
import QueueJobsDispatcher from '../../../core/services/jobs/queue/QueueJobsDispatcher'
import QueueAttachContactToPipelineJob from '../../../microServices/workers/jobs/pipeline/QueueAttachContactToPipelineJob'

@Service()
export default class PipelineService {
  @Inject()
  queueJobsDispatcher: QueueJobsDispatcher

  async attachContactPipeline(payload) {
    const queueJobsDispatcher = Container.get(QueueJobsDispatcher)

    await queueJobsDispatcher.dispatch<QueueAttachContactToPipelineJob>('queue-attach-contact-to-pipeline', payload, {
      hashKey: payload.pipelineId,
    })
  }
}
