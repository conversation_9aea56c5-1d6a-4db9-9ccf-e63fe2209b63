import { Container } from 'typedi'
import { Lock } from 'redlock'
import { Options, Task, TaskQueue, TimeoutError } from './taskQueue'
import RedisLock from '../redis/RedisLock'
import Cls from '../cls/Cls'

export { TimeoutError }

const requestTask = (key: string, options: Options & { priority?: number }): PromiseLike<Lock> =>
  Container.get(RedisLock).lock(key)

const releaseTask = async (lock: Lock) => lock.release()

const taskQueueClsSession = new Cls()

export default (key: string = 'default', options: Options = {}): TaskQueue => {
  const promisePush = async <T>(task: Task<T>, priority: number = 1): Promise<T> => {
    const timeoutError = new TimeoutError()

    const lockOptions = {
      concurrency: 1,
      timeout: 60_000,
      ...options,
    }

    const { timeout } = lockOptions
    return new Promise<T>(async (resolve, reject) => {
      try {
        const lock = await requestTask(key, lockOptions)
        let timeoutRef = setTimeout(() => {
          reject(timeoutError)
        }, timeout)

        try {
          const res = await task()

          resolve(res)
        } catch (e) {
          reject(e)
        } finally {
          clearTimeout(timeoutRef)
          timeoutRef = null
          if (lock) await releaseTask(lock)
        }
      } catch (e) {
        reject(e)
      }
    })
  }

  const run = async <T>(task: Task<T>, priority: number = 1): Promise<T> => {
    if (typeof task !== 'function') {
      throw new Error('Task must be a function.')
    }

    // @ts-ignore
    return taskQueueClsSession.runAndReturn<T>(() => {
      if (typeof task !== 'function') {
        throw new Error('Task must be a function.')
      }

      if (taskQueueClsSession.get('flag')) {
        return task()
      }

      taskQueueClsSession.set('flag', true)

      // @ts-ignore
      return promisePush(taskQueueClsSession.bind(task), priority)
    })
  }

  return { run }
}
