import Redis from 'ioredis'
import { Inject, Service } from 'typedi'
import Config from '../config/Config'

@Service()
export default class RedisClientContainer {
  protected client: Redis

  constructor(@Inject(() => Config) protected config: Config) {
    const url = this.config.get('redisUrl')

    if (!url) throw new Error('redisUrl not found on config.')

    this.client = new Redis(url)
  }

  getClient(): Redis {
    return this.client
  }

  stop() {
    this.getClient().disconnect()
  }
}
