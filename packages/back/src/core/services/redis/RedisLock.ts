import { Inject, Service } from 'typedi'
import Redlock, { Lock, Settings } from 'redlock'
import { v4 as uuid } from 'uuid'
import RedisClientContainer from './RedisClientContainer'
import Logger from '../logs/Logger'

@Service()
export default class RedisLock {
  protected redlock: Redlock

  @Inject()
  protected logger: Logger

  constructor(
    @Inject(() => RedisClientContainer)
    protected clientContainer: RedisClientContainer,
  ) {
    this.redlock = new Redlock([clientContainer.getClient()], {
      retryCount: 1000,
    })
  }

  async lock(resource: string, ttl = 2 * 60 * 1000) {
    return this.redlock.acquire([`lock:${resource}`], ttl)
  }

  async using<T extends any>(
    resources: string[],
    ttt: number,
    fn: () => Promise<T>,
    options?: Partial<Settings>,
  ): Promise<T> {
    const redlock = options ? this.makeClient(options) : this.getClient()

    let response

    await redlock.using(resources, ttt, options, async (signal) => {
      this.logger.log('Redis using lock acquired..', 'info')

      response = await fn()

      if (signal.aborted) {
        throw signal.error
      }

      this.logger.log('Redis using lock released.', 'info')
    })

    return response
  }

  async run<T extends any>(
    fn: () => Promise<T>,
    resource: string,
    ttl?: number,
    options?: Partial<Settings>,
  ): Promise<T> {
    const redlock = options ? this.makeClient(options) : this.getClient()
    return this.runWithClient(redlock, fn, resource, ttl)
  }

  async runWithClient<T extends any>(
    redlock: Redlock,
    fn: () => Promise<T>,
    resource: string = `run/${uuid()}`,
    ttl = 2 * 60 * 1000,
  ): Promise<T> {
    let lock: Lock
    let response

    try {
      lock = await redlock.acquire([resource], ttl)
      this.logger.log(`Redis lock acquired (${lock.value})..`, 'info')
      response = await fn()
    } finally {
      if (lock) {
        await lock.release()
        this.logger.log(`Redis lock released (${lock.value}).`, 'info')
      }
    }

    return response
  }

  makeClient(options: Partial<Settings>) {
    return new Redlock([this.clientContainer.getClient()], options)
  }

  getClient() {
    return this.redlock
  }
}
