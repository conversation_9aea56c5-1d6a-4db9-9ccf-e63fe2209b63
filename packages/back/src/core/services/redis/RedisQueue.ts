import { Inject, Service } from 'typedi'
import { Processor, Queue, Worker } from 'bullmq'
import * as BullBoard from 'bull-board'
import RedisClientContainer from './RedisClientContainer'

const DEFAULT_QUEUE = 'default'

@Service()
export default class RedisQueue {
  protected queues: Map<string, Queue> = new Map()

  constructor(
    @Inject(() => RedisClientContainer)
    protected clientContainer: RedisClientContainer,
  ) {
    this.queues.set(
      DEFAULT_QUEUE,
      new Queue(DEFAULT_QUEUE, {
        connection: this.clientContainer.getClient(),
      }),
    )
    BullBoard.setQueues([...this.queues.values()])
  }

  addJob<T>(name: string, payload: T) {
    if (!this.queues.has(name)) {
      this.queues.set(
        name,
        new Queue(name, {
          connection: this.clientContainer.getClient(),
        }),
      )

      BullBoard.setQueues([...this.queues.values()])
    }

    return this.queues.get(name).add(name, payload)
  }

  makeWorker(name: string, processor: Processor) {
    return new Worker(name, processor, {
      connection: this.clientContainer.getClient(),
    })
  }
}
