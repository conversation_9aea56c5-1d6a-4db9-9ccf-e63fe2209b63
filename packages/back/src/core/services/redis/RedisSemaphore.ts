import { Inject, Service } from 'typedi'
import { Semaphore, TimeoutOptions } from 'redis-semaphore'
import RedisClientContainer from './RedisClientContainer'

@Service()
export default class RedisSemaphore {
  @Inject()
  protected redisClientContainer: RedisClientContainer

  async acquire(key: string, limit = 1, timeoutOptions?: TimeoutOptions) {
    const semaphore = new Semaphore(this.redisClientContainer.getClient(), key, limit, timeoutOptions)

    await semaphore.acquire()

    return {
      release: semaphore.release.bind(semaphore),
    }
  }
}
