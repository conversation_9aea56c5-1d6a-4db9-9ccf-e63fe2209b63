import { Inject, Service } from 'typedi'
import { uniq, random } from 'lodash'
import md5 from 'md5'
import TagCache from './TagCache'
import Logger from '../logs/Logger'
import Config from '../config/Config'

export type CachedData = any
export type RawKey = any
export type RawTag = any

@Service()
export default class ResourceCache {
  @Inject()
  protected logger: Logger

  @Inject()
  protected tagCache: TagCache

  @Inject()
  protected config: Config

  protected hitCount = 0

  protected missCount = 0

  protected minTimeout = 60 * 60

  protected maxTimeout = 3 * 60 * 60

  prepareKey = (rawKey: RawKey, dontHashKey?: boolean): string => {
    const dbVersion = this.config.get('dbSchemaVersion')

    const stringKey = typeof rawKey === 'string' ? rawKey : JSON.stringify(rawKey)

    const hashed = () => `${(Array.isArray(rawKey) && rawKey[0]) || 'no-model'}:${md5(stringKey)}`

    return `${dbVersion}:${dontHashKey ? stringKey : hashed()}`
  }

  prepareTag = (rawTag: RawTag): string =>
    `${this.config.get('dbSchemaVersion')}:${typeof rawTag === 'string' ? rawTag : JSON.stringify(rawTag)}`

  set = <T = CachedData>(
    rawKey: RawKey,
    value: T,
    rawTags: RawTag[],
    options?: { dontHashKey?: boolean; timeout?: number },
  ): Promise<void> => {
    const key = this.prepareKey(rawKey, options?.dontHashKey)
    const tags = rawTags.map(this.prepareTag)

    return this.tagCache.set(key, { rawKey, value }, tags, {
      timeout: options?.timeout || this.getRandomTimeout(),
    })
  }

  get = async <T = CachedData>(rawKey: RawKey): Promise<T> => {
    const key = this.prepareKey(rawKey)
    const res = await this.tagCache.get(key)

    if (!res) return res

    return res.value
  }

  remember = async <T = CachedData>(
    rawKey: RawKey,
    getValue: () => Promise<T>,
    getTags?: (value: T) => Promise<RawTag[]> | RawTag[],
    options?: { dontHashKey?: boolean; timeout?: number },
  ): Promise<T> => {
    if (!this.isEnabled()) return getValue()

    const isDev = this.config.get('env') !== 'production'

    const key = this.prepareKey(rawKey, options?.dontHashKey)

    const cacheValue = await this.tagCache.get(key)
    if (cacheValue) {
      if (isDev) {
        this.log(`Cache hit: ${key}. (${this.incrementHitCount().toFixed(2)} hit ratio)`)
      }
      return cacheValue.value
    }

    if (isDev) {
      this.log(`Cache miss: ${key}. (${this.incrementMissCount().toFixed(2)} hit ratio)`)
    }

    const value = await getValue()

    if (!value) return value

    const rawTags = (getTags && (await getTags(value))) || []

    const tags = rawTags.map(this.prepareTag)

    if (isDev) {
      this.log(`Setting cache: ${tags}`)
    }

    await this.tagCache.set(key, { rawKey, tags, value }, tags, {
      timeout: options?.timeout || this.getRandomTimeout(),
    })

    return value
  }

  forgetByTags = async (rawTags: RawTag[]): Promise<void> => {
    if (!this.isEnabled()) return

    const tags = uniq(rawTags).map(this.prepareTag)
    this.log(`Forgetting tags: ${tags}.`)
    await this.tagCache.invalidate(...tags)
  }

  protected getRandomTimeout() {
    return random(this.minTimeout, this.maxTimeout, false)
  }

  protected isEnabled() {
    return true
    // return this.config.get('resourceCacheEnabled')
  }

  protected incrementHitCount() {
    this.hitCount += 1
    return this.getHitRatio()
  }

  protected incrementMissCount() {
    this.missCount += 1
    return this.getHitRatio()
  }

  protected getHitRatio() {
    return this.hitCount / (this.missCount + this.hitCount)
  }

  protected log(message: string) {
    if (!this.config.get('resourceCacheLogsEnabled')) return

    this.logger.log(message)
  }
}
