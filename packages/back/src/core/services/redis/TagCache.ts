import { Inject, Service } from 'typedi'
import RedisClientContainer from './RedisClientContainer'

type CacheData = any

// stolen from https://github.com/withspectrum/redis-tag-cache
@Service()
export default class TagCache {
  protected options: {
    defaultTimeout: number
    dataPrefix: string
    tagPrefix: string
  }

  constructor(
    @Inject(() => RedisClientContainer)
    protected redisClientContainer: RedisClientContainer,
  ) {
    this.options = {
      defaultTimeout: 60 * 60, // Expire records after an hour (even if they weren't invalidated)
      dataPrefix: 'tagcache:data:',
      tagPrefix: 'tagcache:tag:',
    }
  }

  async set(
    key: string,
    data: CacheData,
    tags: string[],
    options: {
      timeout?: number
    } = {},
  ): Promise<void> {
    // NOTE(@mxstbr): This is a multi execution because if any of the commands is invalid
    // we don't want to execute anything
    const multi = await this.getRedis().multi()

    // Add the key to each of the tag sets
    tags.forEach((tag) => {
      multi.sadd(`${this.options.tagPrefix}${tag}`, key)
    })

    const timeout = (options && options.timeout) || this.options.defaultTimeout
    // Add the data to the key
    if (typeof timeout === 'number') {
      multi.set(`${this.options.dataPrefix}${key}`, JSON.stringify(data), 'ex', timeout)
    } else {
      multi.set(`${this.options.dataPrefix}${key}`, JSON.stringify(data))
    }
    await multi.exec()
  }

  async get(...keys: string[]): Promise<CacheData> {
    return this.getRedis()
      .mget(keys.map((key) => `${this.options.dataPrefix}${key}`))
      .then((res) => {
        try {
          // Special case for single element gets
          if (res.length === 1) return JSON.parse(res[0])
          return res.map((elem) => JSON.parse(elem))
        } catch (err) {
          return res
        }
      })
  }

  // How invalidation by tag works:
  // 1. Get all the keys associated with all the passed-in tags (tags:${tag})
  // 2. Delete all the keys data (data:${key})
  // 3. Delete all the tags (tags:${tag})
  async invalidate(...tags: string[]): Promise<void> {
    // NOTE(@mxstbr): [].concat.apply([],...) flattens the array
    const keys = [].concat.apply(
      [],
      await Promise.all(tags.map((tag) => this.getRedis().smembers(`${this.options.tagPrefix}${tag}`))),
    )

    const pipeline = await this.getRedis().pipeline()

    keys.forEach((key) => {
      pipeline.del(`${this.options.dataPrefix}${key}`)
    })

    tags.forEach((tag) => {
      pipeline.del(`${this.options.tagPrefix}${tag}`)
    })

    await pipeline.exec()
  }

  async invalidateAll(afterPrefixPattern: string = ''): Promise<void> {
    await Promise.all([
      this.invalidateByMatch(`${this.options.dataPrefix}${afterPrefixPattern}*`),
      this.invalidateByMatch(`${this.options.tagPrefix}${afterPrefixPattern}*`),
    ])
  }

  protected invalidateByMatch(match: string) {
    const redis = this.getRedis()

    const stream = redis.scanStream({
      match,
      count: 100,
    })
    let pipeline = redis.pipeline()
    let localKeys = []

    return new Promise((resolve, reject) => {
      stream.on('data', (resultKeys) => {
        for (let i = 0; i < resultKeys.length; i += 1) {
          localKeys.push(resultKeys[i])
          pipeline.del(resultKeys[i])
        }
        if (localKeys.length > 100) {
          localKeys = []
          pipeline = redis.pipeline()
        }
      })

      stream.on('end', async () => {
        pipeline.exec().then(() => resolve(), reject)
      })

      stream.on('error', async (err) => {
        reject(err)
      })
    })
  }

  protected getRedis() {
    return this.redisClientContainer.getClient()
  }
}
