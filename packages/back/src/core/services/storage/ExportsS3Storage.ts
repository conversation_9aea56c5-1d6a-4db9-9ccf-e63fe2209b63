import { Inject, Service } from 'typedi'
// @ts-ignore
import Config from '../config/Config'
import S3Storage from './S3Storage'

@Service()
export default class ExportsS3Storage extends S3Storage {
  constructor(@Inject(() => Config) protected config: Config) {
    super(config)

    this.awsConfig = {
      endpoint: this.config.get('awsExportsS3Endpoint'),
      region: this.config.get('awsExportsS3Region'),
      accessKeyId: this.config.get('awsExportsS3AccessKeyId'),
      secretAccessKey: this.config.get('awsExportsS3SecretAccessKey'),
      params: { Bucket: this.config.get('awsExportsS3BucketName') },
      signatureVersion: 'v4',
    }
  }
}
