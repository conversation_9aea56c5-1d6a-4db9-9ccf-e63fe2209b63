import { Readable } from 'stream'
import { Service } from 'typedi'
// @ts-ignore
import path from 'path'
import { existsSync, mkdirSync } from 'fs'
import fs from 'fs-jetpack'
import waitStream from '../../utils/stream/waitStream'
import type Storage from './Storage'

const basePath = 'storage'

@Service()
export default class FsStorage implements Storage {
  resolvePath = (filename) => path.resolve(basePath, filename)

  ensureDirectoryExistence = (filename) => {
    const dirname = path.dirname(this.resolvePath(filename))
    if (existsSync(dirname)) return true
    mkdirSync(dirname, { recursive: true })
    return true
  }

  write = (filename: string, data: string | Buffer | Readable, options) => {
    if (data instanceof Readable) {
      const ws = this.createWriteStream(filename, options)
      return waitStream(data.pipe(ws))
    }

    return fs.writeAsync(this.resolvePath(filename), data, options)
  }

  createReadStream = (filename: string, options) => fs.createReadStream(this.resolvePath(filename), options)

  createWriteStream = (filename: string, options) => {
    this.ensureDirectoryExistence(filename)
    return fs.createWriteStream(this.resolvePath(filename), options)
  }

  exists = (filename: string) => fs.existsAsync(this.resolvePath(filename)).then(Boolean)

  remove = (filename: string, options) => fs.remove(this.resolvePath(filename))

  read = (filename: string) => fs.readAsync(this.resolvePath(filename))

  readSync = (filename: string) => fs.read(this.resolvePath(filename))
}
