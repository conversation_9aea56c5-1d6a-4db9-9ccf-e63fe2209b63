import AWS, { S3 } from 'aws-sdk'
import { Readable } from 'stream'
import { Inject, Service } from 'typedi'
// @ts-ignore
import type Storage from './Storage'
import Config from '../config/Config'
import Logger from '../logs/Logger'
import retry from '../../utils/retry'

@Service()
export default class OracleFallbackStorage implements Storage {
  protected oracleConfig: S3.Types.ClientConfiguration

  @Inject()
  protected logger: Logger

  constructor(@Inject(() => Config) protected config: Config) {
    this.oracleConfig = {
      endpoint: this.config.get('oracleEndpoint'),
      region: this.config.get('oracleRegion'),
      accessKeyId: this.config.get('oracleAccessKeyId'),
      secretAccessKey: this.config.get('oracleSecretAccessKey'),
      params: { Bucket: this.config.get('oracleBucketNameFallback') },
      s3ForcePathStyle: true,
      signatureVersion: 'v4',
    }
  }

  getBucket = () => new AWS.S3(this.oracleConfig)

  write = async (
    filename: string,
    data: string | Buffer | Readable,
    options?: any,
  ): Promise<S3.ManagedUpload.SendData> => {
    const retryWrite = await retry<S3.ManagedUpload.SendData>(
      async () =>
        this.getBucket()
          .upload({ ...options, Body: data, Key: filename })
          .promise(),
      this.logger,
    )

    return retryWrite
  }

  createReadStream = (filename: string, options?: any) =>
    this.getBucket()
      .getObject({
        ...options,
        Key: filename,
      })
      .createReadStream()

  exists = (filename: string): Promise<boolean> =>
    // @ts-ignore
    this.getBucket().headObject({ Key: filename }).promise().then(Boolean)

  read = (filename: string, options?: any): Promise<Buffer> =>
    // @ts-ignore
    this.getBucket()
      .getObject({
        ...options,
        Key: filename,
      })
      .promise()

  getOracleConfig = () => this.oracleConfig

  getMetadata = async (filename: string, options?) =>
    this.getBucket()
      .headObject({
        ...options,
        Key: filename,
      })
      .promise()

  copy = async (sourceKey, targetKey) =>
    this.getBucket()
      .copyObject({
        CopySource: `${this.oracleConfig.params.Bucket}/${sourceKey}`,
        Bucket: this.oracleConfig.params.Bucket,
        Key: targetKey,
      })
      .promise()

  getSignedUrl = async (
    operation: 'putObject' | 'getObject',
    params: {
      Bucket?: string
      Key: string
      Expires?: number
      ContentType?: string
    },
  ) => this.getBucket().getSignedUrlPromise(operation, params)

  copyToAnotherBucket = async (sourceBucket, sourceObjectKey, newObjectKey, bucketDestination) =>
    this.getBucket()
      .copyObject({
        CopySource: `${sourceBucket}/${sourceObjectKey}`,
        Bucket: bucketDestination,
        Key: newObjectKey,
      })
      .promise()

  delete = async (filename) =>
    this.getBucket()
      .deleteObject({
        Bucket: this.oracleConfig.params.Bucket,
        Key: filename,
      })
      .promise()
}
