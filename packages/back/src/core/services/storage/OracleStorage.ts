import AWS, { S3 } from 'aws-sdk'
import { Readable } from 'stream' // Importe Readable
import { Inject, Service } from 'typedi'
import Config from '../config/Config'
import Logger from '../logs/Logger'
// @ts-ignore
import type Storage from './Storage'

@Service({ transient: true })
export default class OracleStorage implements Storage {
  protected oracleConfig: S3.Types.ClientConfiguration

  protected currentBucket: string

  @Inject()
  protected logger: Logger

  constructor(@Inject(() => Config) protected config: Config) {
    this.oracleConfig = {
      endpoint: this.config.get('oracleEndpoint'),
      region: this.config.get('oracleRegion'),
      accessKeyId: this.config.get('oracleAccessKeyId'),
      secretAccessKey: this.config.get('oracleSecretAccessKey'),
      params: { Bucket: this.currentBucket },
      s3ForcePathStyle: true,
      signatureVersion: 'v4',
    }
  }

  setBucket = (bucket: string = this.config.get('oracleBucketName')) => {
    this.currentBucket = bucket
    this.oracleConfig.params = { Bucket: bucket }
    return this
  }

  getBucket = (bucket: string = this.currentBucket) => {
    const config = {
      ...this.oracleConfig,
      params: { Bucket: bucket },
    }
    return new AWS.S3(config)
  }

  write = (filename: string, data: string | Buffer | Readable, options?: any): Promise<S3.ManagedUpload.SendData> =>
    this.getBucket(this.currentBucket)
      .upload({ ...options, Body: data, Key: filename })
      .promise()

  createReadStream = (filename: string, options?: any) =>
    this.getBucket(this.currentBucket)
      .getObject({
        ...options,
        Key: filename,
      })
      .createReadStream()

  exists = (filename: string): Promise<boolean> =>
    // @ts-ignore
    this.getBucket(this.currentBucket).headObject({ Key: filename }).promise().then(Boolean)

  read = (filename: string, options?: any): Promise<Buffer> =>
    // @ts-ignore
    this.getBucket(this.currentBucket)
      .getObject({
        ...options,
        Key: filename,
      })
      .promise()

  getOracleConfig = () => this.oracleConfig

  createWriteStream = (filename, data, options) => {
    throw new Error('Not yet implemented.')
  }

  getMetadata = async (filename: string, options?) =>
    this.getBucket(this.currentBucket) // Alteração aqui
      .headObject({
        ...options,
        Key: filename,
      })
      .promise()

  copy = async (sourceKey, targetKey) =>
    this.getBucket(this.currentBucket)
      .copyObject({
        CopySource: `${this.currentBucket}/${sourceKey}`,
        Bucket: this.currentBucket,
        Key: targetKey,
      })
      .promise()

  getSignedUrl = async (
    operation: 'putObject' | 'getObject',
    params: {
      Bucket?: string
      Key: string
      Expires?: number
      ContentType?: string
    },
  ) => {
    const bucket = params.Bucket || this.currentBucket
    return this.getBucket(bucket).getSignedUrlPromise(operation, params)
  }

  copyToAnotherBucket = async (sourceBucket, sourceObjectKey, newObjectKey, bucketDestination) =>
    this.getBucket(sourceBucket)
      .copyObject({
        CopySource: `${sourceBucket}/${sourceObjectKey}`,
        Bucket: bucketDestination,
        Key: newObjectKey,
      })
      .promise()

  delete = async (filename) =>
    this.getBucket(this.currentBucket)
      .deleteObject({
        Bucket: this.currentBucket,
        Key: filename,
      })
      .promise()
}
