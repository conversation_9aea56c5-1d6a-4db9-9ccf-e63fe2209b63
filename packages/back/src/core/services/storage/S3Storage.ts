import AWS, { S3 } from 'aws-sdk'
import { Readable } from 'stream'
import { Inject, Service } from 'typedi'
// @ts-ignore
import type Storage from './Storage'
import Config from '../config/Config'
import retry from '../../utils/retry'
import Logger from '../logs/Logger'

@Service()
export default class S3Storage implements Storage {
  protected awsConfig: S3.Types.ClientConfiguration

  @Inject()
  protected logger: Logger

  constructor(@Inject(() => Config) protected config: Config<any>) {
    this.awsConfig = {
      endpoint: this.config.get('awsS3Endpoint'),
      region: this.config.get('awsRegion'),
      accessKeyId: this.config.get('awsS3AccessKeyId'),
      secretAccessKey: this.config.get('awsS3SecretAccessKey'),
      params: { Bucket: this.config.get('awsBucketName') },
    }
  }

  getS3 = () => new AWS.S3(this.awsConfig)

  write = (filename: string, data: string | Buffer | Readable, options?: any): Promise<S3.ManagedUpload.SendData> =>
    this.getS3()
      .upload({ ...options, Body: data, Key: filename })
      .promise()

  createReadStream = (filename: string, options?: any) =>
    this.getS3()
      .getObject({
        ...options,
        Key: filename,
      })
      .createReadStream()

  exists = (filename: string): Promise<boolean> =>
    // @ts-ignore
    this.getS3().headObject({ Key: filename }).promise().then(Boolean)

  read = (filename: string, options?: any): Promise<Buffer> =>
    // @ts-ignore
    this.getS3()
      .getObject({
        ...options,
        Key: filename,
      })
      .promise()

  getAwsConfig = () => this.awsConfig

  createWriteStream = (filename, data, options) => {
    throw new Error('Not yet implemented.')
  }

  getMetadata = async (filename: string, options?) =>
    this.getS3()
      .headObject({
        ...options,
        Key: filename,
      })
      .promise()

  getSignedUrl = async (
    operation: 'putObject' | 'getObject',
    params: {
      Bucket?: string
      Key: string
      Expires?: number
      ContentType?: string
      ResponseContentDisposition?: string
    },
  ) => this.getS3().getSignedUrlPromise(operation, params)

  copy = async (sourceKey, targetKey) =>
    this.getS3()
      .copyObject({
        CopySource: `${this.awsConfig.params.Bucket}/${sourceKey}`,
        Bucket: this.awsConfig.params.Bucket,
        Key: targetKey,
      })
      .promise()

  copyToAnotherBucket = async (sourceBucket, sourceObjectKey, newObjectKey, bucketDestination) =>
    this.getS3()
      .copyObject({
        CopySource: `${sourceBucket}/${sourceObjectKey}`,
        Bucket: bucketDestination,
        Key: newObjectKey,
      })
      .promise()

  delete = async (filename) =>
    this.getS3()
      .deleteObject({
        Bucket: this.awsConfig.params.Bucket,
        Key: filename,
      })
      .promise()
}
