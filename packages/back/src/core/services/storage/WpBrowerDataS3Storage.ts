import { Inject, Service } from 'typedi'
// @ts-ignore
import Config from '../config/Config'
import S3Storage from './S3Storage'

@Service()
export default class WpBrowerDataS3Storage extends S3Storage {
  constructor(@Inject(() => Config) protected config: Config) {
    super(config)

    this.awsConfig = {
      endpoint: this.config.get('wpBrowserDataS3Endpoint'),
      region: this.config.get('wpBrowserDataS3Region'),
      accessKeyId: this.config.get('wpBrowserDataS3AccessKeyId'),
      secretAccessKey: this.config.get('wpBrowserDataS3SecretAccessKey'),
      params: { Bucket: this.config.get('wpBrowserDataS3BucketName') },
      signatureVersion: 'v4',
    }
  }
}
