import { Inject, Service } from 'typedi'
// @ts-ignore
import Config from '../config/Config'
import S3Storage from './S3Storage'

@Service()
export default class WpLvS3Storage extends S3Storage {
  constructor(@Inject(() => Config) protected config: Config) {
    super(config)

    this.awsConfig = {
      endpoint: this.config.get('wpLvS3Endpoint'),
      region: this.config.get('wpLvS3Region'),
      accessKeyId: this.config.get('wpLvS3AccessKeyId'),
      secretAccessKey: this.config.get('wpLvS3SecretAccessKey'),
      params: { Bucket: this.config.get('wpLvS3BucketName') },
      signatureVersion: 'v4',
    }
  }
}
