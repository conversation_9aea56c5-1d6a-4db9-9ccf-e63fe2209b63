import { Container } from 'typedi'
import S3Storage from './S3Storage'
import OracleStorage from './OracleStorage'
import FsStorage from './FsStorage'
import Config from '../config/Config'
import WpLvS3Storage from './WpLvS3Storage'
import WpBrowerDataS3Storage from './WpBrowerDataS3Storage'
import ExportsS3Storage from './ExportsS3Storage'
// @ts-ignore
import Storage from './Storage'

const config = Container.get(Config)

const buckets = config.get('buckets') as string

const drivers: { [key: string]: Storage } = {
  fs: Container.get(FsStorage),
  s3: Container.get(S3Storage),
  wpLvS3: Container.get(WpLvS3Storage),
  wpBrowserDataS3: Container.get(WpBrowerDataS3Storage),
  exportsS3: Container.get(ExportsS3Storage),
}

buckets.split(',').map((bucketName, index) => {
  const driverName = `oracle${index === 0 ? '' : index + 1}`
  drivers[driverName] = Container.get(OracleStorage).setBucket(bucketName)
})

export const getDriver = (name: keyof typeof drivers) => {
  if (!drivers[name]) throw new Error(`Storage driver "${name}" does not exist.`)
  return drivers[name]
}

export const getStorage = (storage?) => getDriver(storage || Container.get(Config).get('storageDriver'))

export default {
  getDriver,
  getStorage,
}
