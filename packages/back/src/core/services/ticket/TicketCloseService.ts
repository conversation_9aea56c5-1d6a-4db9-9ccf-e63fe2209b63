import { Container } from 'typedi'
import { differenceInSeconds } from 'date-fns'
import { ContactInstance } from '../../dbSequelize/models/Contact'
import { EventTransaction } from '../../resources/BaseResource'
import ticketResource from '../../resources/ticketResource'
import clientFeedbackResource from '../../resources/clientFeedbackResource'
import ticketTransfersResource from '../../resources/ticketTransfersResource'
import messageResource from '../../resources/messageResource'
import contactResource from '../../resources/contactResource'
import { getWaitingTime, getTicketTime } from './TicketTransferService'
import { getCloseTicketQueue } from '../../queues/tickets'
import SendMailCloseTicketWebchat from '../../../microServices/workers/jobs/contact/SendMailCloseTicketWebchat'
import HttpJobsDispatcher from '../jobs/http/HttpJobsDispatcher'
import QueueJobsDispatcher from '../jobs/queue/QueueJobsDispatcher'
import QueueStartSummaryJob from '../../../microServices/workers/jobs/summary/QueueStartSummaryJob'
import accountResource from '../../resources/accountResource'

const httpJobsDispatcher = Container.get(HttpJobsDispatcher)
const queueJobsDispatcher = Container.get(QueueJobsDispatcher)
export default class TicketCloseService {
  async closeTicket(
    data: {
      contact: ContactInstance
      byUserId?: string
      comments?: string
      ticketTopicIds?: string[] | string
      aiSummaryRating?: string
      transferredByBot?: Boolean
      context?: Object
    },
    options: {
      dontEmit?: boolean
      transaction?: any
      eventTransaction?: EventTransaction
    } = {},
  ) {
    const { contact, comments, ticketTopicIds, aiSummaryRating, byUserId } = data

    if (!contact) {
      throw new Error(`Contact is required to close ticket.`)
    }

    const currentTicket = contact.currentTicket

    if (!contact.currentTicketId) {
      throw new Error(`Contact #${contact.id} does not have an open ticket.`)
    }

    if (!contact.currentTicket) {
      throw new Error(`contact.currentTicket is required.`)
    }

    if (!currentTicket.currentTicketTransfer) {
      throw new Error(`currentTicket.currentTicketTransfer is required.`)
    }

    return getCloseTicketQueue(contact.id).run(() =>
      ticketResource.maybeEventTransaction(
        (eventTransaction) =>
          ticketResource.nestedTransaction(async (transaction) => {
            const transactions = { eventTransaction, transaction }
            const { dontEmit = false } = options
            const now = new Date()

            const lastTicketTransfer = currentTicket.currentTicketTransfer

            if (lastTicketTransfer.firstMessageId) {
              lastTicketTransfer.firstMessage = await messageResource.findById(
                currentTicket.currentTicketTransfer.firstMessageId,
              )
            }

            const waitingTime = await getWaitingTime(
              {
                ticketTransfer: lastTicketTransfer,
                ticket: currentTicket,
                now,
              },
              { transaction, eventTransaction },
            )

            const ticketTime = await getTicketTime({
              ticketTransfer: lastTicketTransfer,
              now,
            })

            const account = await accountResource.findById(currentTicket?.accountId, { attributes: ['settings'] })

            if ((account?.settings?.flags || {})['enable-smart-summary']) {
              await queueJobsDispatcher.dispatch<QueueStartSummaryJob>(
                'queued-start-summary',
                { currentTicket, type: 'finalize', contact },
                {
                  hashKey: contact.accountId + '_' + contact.serviceId + '_' + currentTicket?.id,
                },
              )
            }

            // Atualiza ticketTransfer adicionado endedAt e métrica de ticketTime
            await ticketTransfersResource.update(
              lastTicketTransfer,
              {
                endedAt: now,
                metrics: {
                  ...lastTicketTransfer.metrics,
                  ticketTime,
                  waitingTime,
                },
              },
              { ...transactions, dontEmit },
            )

            // Cria mensagem de fechamando
            const ticketTransferMessage = await messageResource.createTicketCloseMessage(
              {
                contact,
                ticketId: currentTicket.id,
                contactId: contact.id,
                serviceId: contact.serviceId,
                accountId: contact.accountId,
                timestamp: now,
              },
              { ...transactions, dontEmit },
            )

            // Cria novo ticketTransfer
            await ticketTransfersResource.create(
              {
                action: 'closed',
                accountId: contact.accountId,
                toDepartmentId: null,
                fromDepartmentId: currentTicket.departmentId,
                toUserId: null,
                fromUserId: currentTicket.userId,
                transferredMessageId: ticketTransferMessage.id,
                ticketId: currentTicket.id,
                byUserId,
                comments,
                startedAt: now,
              },
              { ...transactions, dontEmit },
            )

            // Atualiza ticket fechando o chamado e adicionado métrica do ticketTime
            await ticketResource.updateById(
              currentTicket.id,
              {
                isOpen: false,
                comments,
                endedAt: now,
                currentTicketTransferId: null,
                metrics: {
                  ...currentTicket.metrics,
                  ticketTime: differenceInSeconds(now, currentTicket.startedAt),
                },
              },
              { ...transactions, returning: false, dontEmit },
            )

            const ticket = await ticketResource.findById(currentTicket.id, {
              include: [
                {
                  model: 'user',
                  include: [
                    'account',
                    'roles',
                    {
                      model: 'departments',
                      include: ['distribution'],
                    },
                  ],
                },
              ],
              ...transactions,
            })

            await ticket.addTicketTopics(
              Array.isArray(ticketTopicIds) ? ticketTopicIds : [ticketTopicIds],
              transactions,
            )

            if (aiSummaryRating) {
              // Adiciona feedback do cliente
              await clientFeedbackResource.create(
                {
                  ticketId: currentTicket.id,
                  feedback: aiSummaryRating,
                },
                { ...transactions, dontEmit },
              )
            }

            // Atualiza contato limpando o currentTicket
            const updatedContact = await contactResource.updateById(
              contact.id,
              { currentTicketId: null },
              { ...transactions, dontEmit },
            )

            // Emite evento de fechamento de chamado
            if (!dontEmit) ticketResource.emitClosed(ticket, eventTransaction)

            if (contact.service?.type === 'webchat') {
              // Manter sem await para não bloqueio
              httpJobsDispatcher.dispatch<SendMailCloseTicketWebchat>('send-mail-close-ticket-webchat', {
                contact,
                origin: 'auto',
              })
            }

            return updatedContact
          }, options.transaction),
        options.eventTransaction,
      ),
    )
  }
}
