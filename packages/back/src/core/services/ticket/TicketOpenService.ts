import { Container } from 'typedi'
import { subMilliseconds } from 'date-fns'
import accountResource from '../../resources/accountResource'
import ticketResource from '../../resources/ticketResource'
import messageResource from '../../resources/messageResource'
import ticketTransfersResource from '../../resources/ticketTransfersResource'
import contactResource from '../../resources/contactResource'
import { ContactInstance } from '../../dbSequelize/models/Contact'
import { MessageInstance } from '../../dbSequelize/models/Message'
import { EventTransaction } from '../../resources/BaseResource'
import { AccountInstance } from '../../dbSequelize/models/Account'
import { TicketInstance } from '../../dbSequelize/models/Ticket'
import interpolate from '../../utils/interpolate'
import answersResource from '../../resources/answersResource'
import redisTaskQueue from '../queue/redisTaskQueue'
import RedisClientContainer from '../redis/RedisClientContainer'
import Logger from '../logs/Logger'
import sequelize from '../db/sequelize'
import { clearContactWebchatStages } from '../../../microServices/workers/jobs/contact/WebchatContactIdleStagesJob'
import userResource from '../../../core/resources/userResource'
import botsSessionsResource from '../../../core/resources/botsSessionsResource'

const logger = Container.get(Logger)

export default class TicketOpenService {
  getProtocolFormat(format = '{{date}}{{count}}'): string {
    if (!format.includes('{{count}}')) {
      return format + '{{count}}'
    }

    return format
  }

  async generateProtocolAndCount(account: AccountInstance, options = {}): Promise<{ protocol: string; count: number }> {
    const date = new Date().toISOString().split('T')[0].replace(/-/g, '')

    const redisClient = Container.get(RedisClientContainer)

    const key = `tickets-protocol-count:${account.id}`

    let count = Number(await redisClient.getClient().get(key))

    if (!count) {
      const queue = redisTaskQueue(`generate-protocol-count-queue:${account.id}`)

      await queue.run(async () => {
        if (await redisClient.getClient().get(key)) {
          return
        }

        const maxCount = (await ticketResource.max('count', { where: { accountId: account.id }, ...options })) || 0

        await redisClient.getClient().set(key, maxCount)
      })
    }

    count = await redisClient.getClient().incr(key)

    return {
      protocol: interpolate(this.getProtocolFormat(account.settings.protocolFormat), {
        date,
        count,
      }),
      count,
    }
  }

  async openTicket(
    data: {
      contact: ContactInstance
      message?: MessageInstance
      departmentId: string
      userId: string
      byUserId?: string
      origin: TicketInstance['origin']
      startedAt?: Date
      comments?: string
    },
    options: {
      transaction?: any
      eventTransaction?: EventTransaction
      dontEmit?: boolean
    } = {},
  ) {
    return ticketResource.maybeEventTransaction(
      (eventTransaction) =>
        ticketResource.nestedTransaction(async (transaction) => {
          const { contact, message, departmentId, byUserId, origin, startedAt, comments } = data
          let { userId } = data

          const account = contact.account || (await accountResource.findById(contact.accountId, { transaction }))

          const transactionOptions = { transaction, eventTransaction, dontEmit: options.dontEmit }

          if (!contact?.account) {
            logger.log('contact came without account, had to fetch.', 'warn')
          }

          const now = new Date()

          if (contact.data.survey) {
            await answersResource.unflagSurveyFromContact(contact, transactionOptions)
          }
          if (contact.data.webchat) {
            await clearContactWebchatStages(contact, transactionOptions)
          }

          // Verifica se o usuário ainda está vinculado ao departamento, se não estiver, transfere para a fila do departamento selecionado
          if (userId) {
            const userBelongsToDepartment = await userResource.userBelongsToDepartment(userId, departmentId)

            if (!userBelongsToDepartment) {
              userId = null
            }
          }

          const createTicket = async () => {
            return sequelize.transaction({ transaction }, async (transactionSavepoint) => {
              // Gera count e protocolo para o ticket
              const { protocol, count } = await this.generateProtocolAndCount(account, {
                ...transactionOptions,
                transaction: transactionSavepoint,
              })

              // Cria o ticket
              return ticketResource.create(
                {
                  isOpen: true,
                  startedAt: startedAt || now,
                  departmentId,
                  userId,
                  protocol,
                  count,
                  origin,
                  contactId: contact.id,
                  accountId: contact.accountId,
                  firstMessageId: message && message.id,
                  lastMessageId: message && message.id,
                },
                { ...transactionOptions, transaction: transactionSavepoint },
              )
            })
          }

          const ticket = await createTicket().catch((e) => {
            if (e.name === 'SequelizeUniqueConstraintError') {
              logger.log('Trying again create ticket...', 'warn')
              return createTicket() // executa uma retentativa em caso de erro
            }
            throw e
          })

          // Cria a mensagem de abertura
          const ticketOpenMessage = await messageResource.createTicketOpenMessage(
            {
              timestamp: message && message.timestamp ? subMilliseconds(message.timestamp, 1) : now,
              ticketId: ticket.id,
              contactId: contact.id,
              serviceId: contact.serviceId,
              accountId: contact.accountId,
              contact,
            },
            transactionOptions,
          )

          // Cria o ticketTransfer
          const ticketTransfer = await ticketTransfersResource.create(
            {
              action: 'opened',
              accountId: contact.accountId,
              toDepartmentId: data.departmentId,
              toUserId: userId,
              comments,
              transferredMessageId: ticketOpenMessage.id,
              ticketId: ticket.id,
              byUserId,
              startedAt: now,
              firstMessageId: message && message.id,
              lastMessageId: message && message.id,
            },
            transactionOptions,
          )

          let updatedMessage

          // Atualiza a mensagem, se existir, setando os ids
          if (message) {
            // if it has a message I.E. started from a contact message, update it setting
            // the ticketId
            updatedMessage = await messageResource.update(
              message,
              {
                ticketId: ticket.id,
                ticketUserId: ticket.userId,
                ticketDepartmentId: ticket.departmentId,
              },
              transactionOptions,
            )
          }

          // Atualiza o ticket setando o currentTicketTransfer
          const updatedTicket = await ticketResource.update(
            ticket,
            {
              currentTicketTransferId: ticketTransfer.id,
            },
            transactionOptions,
          )

          // Atualiza o contato setando o novo ticket
          // @ts-ignore
          const contactData = {
            ...(!byUserId && contact.service?.botId
              ? {
                  data: { botIsRunning: true, botFinishedAt: null },
                }
              : (contact.data?.botIsRunning && {
                  data: { botIsRunning: false, botFinishedAt: new Date() },
                }) ||
                {}),
          }

          const updatedContact = await contactResource.update(
            contact,
            {
              currentTicketId: ticket.id,
              ...contactData,
            },
            { ...transactionOptions, ...(contactData?.data && { mergeJson: ['data'] }) },
          )
          if (contactData.data?.botIsRunning) {
            logger.log(`Bot is running for contactId: ${contact.id} ticketId: ${contact.currentTicketId}`, 'info')
          }

          // Reseta sessões ativas do bot para contexto inicial
          await botsSessionsResource.bulkUpdate(
            { store: { context: '@INIT', contextPassCount: 0 } },
            {
              where: {
                contactId: contact.id,
                $or: [{ 'store.keepContext': { $eq: null } }, { 'store.keepContext': { $eq: false } }],
              },
              dontEmit: true,
            },
          )

          // Emite o evento de abertura de chamado
          ticketResource.emitOpened(updatedTicket, eventTransaction)

          return {
            message: updatedMessage,
            ticket: updatedTicket,
            ticketOpenMessage,
            updatedContact,
            ticketTransfer,
            updatedMessage,
          }
        }, options.transaction),
      options.eventTransaction,
    )
  }
}
