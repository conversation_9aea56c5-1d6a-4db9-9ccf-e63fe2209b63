import { Transaction } from 'sequelize'
import { Container } from 'typedi'
import userResource, { USER_TRANSFER } from '../../resources/userResource'
import { ContactInstance } from '../../dbSequelize/models/Contact'
import { TicketTransferInstance } from '../../dbSequelize/models/TicketTransfer'
import { EventTransaction } from '../../resources/BaseResource'
import ticketResource, { TRANSFER } from '../../resources/ticketResource'
import ticketTransfersResource from '../../resources/ticketTransfersResource'
import messageResource from '../../resources/messageResource'
import contactResource from '../../resources/contactResource'
import TicketOpenService from './TicketOpenService'
import { TicketInstance } from '../../dbSequelize/models/Ticket'
import differenceInSeconds from '../../utils/date/differenceInSeconds'
import { getTransferTicketQueue } from '../../queues/tickets'
import answersResource from '../../resources/answersResource'
import Logger from '../logs/Logger'
import QueueJobsDispatcher from '../jobs/queue/QueueJobsDispatcher'
import QueueStartSummaryJob from '../../../microServices/workers/jobs/summary/QueueStartSummaryJob'
import accountResource from '../../resources/accountResource'

const logger = Container.get(Logger)

const queueJobsDispatcher = Container.get(QueueJobsDispatcher)

export const updateTicketMetrics = async (
  data: {
    ticket: TicketInstance
    currentTicketTransfer: TicketTransferInstance
    transferredByBot: Boolean
    now: Date
  },
  options: {
    dontEmit?: boolean
    transaction?: any
    eventTransaction?: EventTransaction
  } = {},
): Promise<void> => {
  const { ticket, currentTicketTransfer } = data

  // Paulo pediu pra remover essa regra de ignorar o BOT
  // if (transferredByBot) return

  const firstMessage = await messageResource.findOne({
    where: {
      ticketId: ticket.id,
      type: { $ne: 'ticket' },
      visible: true,
    },
    order: [['timestamp', 'asc']],
    ...options,
  })

  const isActiveTicket = firstMessage && firstMessage.isFromMe

  // Ignorar uma vez a contagem se isActiveTicket for true
  if (isActiveTicket && !ticket.metrics.isActiveTicket) {
    await ticketResource.update(
      ticket,
      {
        metrics: { isActiveTicket: true },
      },
      { ...options, mergeJson: ['metrics'], dontEmit: true },
    )
    return
  }

  let { ticketTransferCount = 0, waitingTimeTransfersSum = 0, waitingTimeTransfersAvg = 0 } = ticket.metrics

  ticketTransferCount++
  waitingTimeTransfersSum += currentTicketTransfer.metrics.waitingTime
  waitingTimeTransfersAvg = Math.ceil(waitingTimeTransfersSum / ticketTransferCount)

  if (waitingTimeTransfersAvg * ticketTransferCount !== waitingTimeTransfersSum) {
    logger.log('Possible difference in waitingTimeTransfersSum generated by metrics when transferring ticket', 'warn')
  }
  if (Math.ceil(waitingTimeTransfersSum / ticketTransferCount) !== waitingTimeTransfersAvg) {
    logger.log('Possible difference in waitingTimeTransfersAvg generated by metrics when transferring ticket', 'warn')
  }
  if (Math.ceil(waitingTimeTransfersSum / waitingTimeTransfersAvg) !== ticketTransferCount) {
    logger.log('Possible difference in ticketTransferCount generated by metrics when transferring ticket', 'warn')
  }

  await ticketResource.update(
    ticket,
    {
      metrics: {
        waitingTimeTransfersSum,
        waitingTimeTransfersAvg,
        ticketTransferCount,
      },
    },
    { ...options, mergeJson: ['metrics'] },
  )
}

export const getWaitingTime = async (
  data: {
    ticketTransfer: TicketTransferInstance
    ticket: TicketInstance
    now: Date
  },
  options: {
    dontEmit?: boolean
    eventTransaction?: EventTransaction
    transaction?: Transaction
  } = {},
): Promise<Number> => {
  const { ticketTransfer, ticket, now } = data
  const { firstMessage } = ticketTransfer

  if (!ticketTransfer.startedAt) throw new Error('Invalid date')

  if (!(ticketTransfer.startedAt instanceof Date) || isNaN(ticketTransfer.startedAt.getTime()))
    throw new Error('Invalid date')

  if (!firstMessage) return differenceInSeconds(ticketTransfer.endedAt || now, ticketTransfer.startedAt)
  if (firstMessage.isFromMe && firstMessage.timestamp < ticketTransfer.startedAt) return 0

  const firstOperatorMessage = await messageResource.findOne({
    where: {
      ticketId: ticket.id,
      isFromMe: true,
      isFromBot: false,
      origin: {
        $ne: 'bot',
      },
      timestamp: {
        $gte: ticketTransfer.startedAt,
      },
    },
    order: [['timestamp', 'asc']],
    ...options,
  })

  const endTime = firstOperatorMessage?.createdAt ?? ticketTransfer.endedAt ?? now
  return differenceInSeconds(endTime, ticketTransfer.startedAt)
}

export const getTicketTime = (data: { ticketTransfer: TicketTransferInstance; now: Date }): Number => {
  const { ticketTransfer, now } = data

  if (!ticketTransfer.startedAt) throw new Error('Invalid date')

  if (!(ticketTransfer.startedAt instanceof Date) || isNaN(ticketTransfer.startedAt.getTime()))
    throw new Error('Invalid date')

  return differenceInSeconds(ticketTransfer.endedAt || now, ticketTransfer.startedAt)
}

export default class TicketTransferService {
  protected ticketOpenService: TicketOpenService

  constructor(ticketOpenService: TicketOpenService) {
    this.ticketOpenService = ticketOpenService
  }

  async transferTicket(
    data: {
      contact: ContactInstance
      userId?: string
      departmentId: string
      byUserId?: string
      comments?: string
      timestamp?: Date
      transferredByBot?: Boolean
      transferredByDistribution?: Boolean
    },
    options: {
      dontEmit?: boolean
      transaction?: any
      eventTransaction?: EventTransaction
    } = {},
    dontEmitContactEvent = false,
  ) {
    const {
      contact,
      departmentId,
      byUserId,
      comments,
      transferredByBot = false,
      transferredByDistribution = false,
    } = data
    let { userId = null } = data
    const currentTicket = contact?.currentTicket

    // Se ainda não existe um ticket para o contato,
    // a rotina é redirecionada para criação
    if (!currentTicket) {
      if (contact.data.survey) {
        await answersResource.unflagSurveyFromContact(contact, options)
      }

      const { ticket } = await this.ticketOpenService.openTicket(
        {
          contact,
          departmentId,
          userId,
          byUserId,
          comments,
          origin: data.byUserId ? 'manual' : 'automatic',
        },
        // @ts-ignore
        options,
      )

      return ticket
    }

    // Se não houve alteração, não faz nada
    if (currentTicket.departmentId === departmentId && currentTicket.userId === userId) {
      return contact
    }

    // Verifica se o usuário ainda está vinculado ao departamento, se não estiver, transfere para a fila do departamento selecionado
    if (userId) {
      const userBelongsToDepartment = await userResource.userBelongsToDepartment(userId, departmentId)

      if (!userBelongsToDepartment) {
        userId = null
      }
    }

    return getTransferTicketQueue(contact.id).run(async () =>
      ticketResource.maybeEventTransaction(
        async (eventTransaction) =>
          ticketResource.nestedTransaction(async (transaction) => {
            const transactions = { eventTransaction, transaction }
            const { dontEmit = false } = options

            const now = new Date()

            // Altera último ticketTransfer adicionado métricas
            if (!currentTicket.currentTicketTransfer) {
              throw new Error('currentTicket.currentTicketTransfer is required.')
            }
            const lastTicketTransfer = currentTicket.currentTicketTransfer

            if (lastTicketTransfer.firstMessageId) {
              lastTicketTransfer.firstMessage = await messageResource.findById(
                currentTicket.currentTicketTransfer.firstMessageId,
                transactions,
              )
            }

            const waitingTime = await getWaitingTime(
              {
                ticketTransfer: lastTicketTransfer,
                ticket: currentTicket,
                now,
              },
              transactions,
            )

            const ticketTime = getTicketTime({
              ticketTransfer: lastTicketTransfer,
              now,
            })

            const ticketTransferUpdated = await ticketTransfersResource.update(
              lastTicketTransfer,
              {
                endedAt: now,
                metrics: {
                  ...lastTicketTransfer.metrics,
                  ticketTime,
                  waitingTime,
                  transferredByBot,
                },
              },
              { ...transactions, dontEmit },
            )

            await updateTicketMetrics(
              {
                ticket: currentTicket,
                currentTicketTransfer: ticketTransferUpdated,
                transferredByBot,
                now,
              },
              { ...transactions, dontEmit: true },
            )

            // Cria mensagem de transferência
            const ticketTransferMessage = await messageResource.createTicketTransferMessage(
              {
                contact,
                timestamp: data.timestamp || now,
                ticketId: currentTicket.id,
                contactId: contact.id,
                serviceId: contact.serviceId,
                accountId: contact.accountId,
              },
              { ...transactions, dontEmit },
            )

            // Cria novo ticketTransfer
            const ticketTransfer = await ticketTransfersResource.create(
              {
                action: 'transferred',
                accountId: contact.accountId,
                toDepartmentId: departmentId,
                fromDepartmentId: currentTicket.departmentId,
                toUserId: userId,
                fromUserId: currentTicket.userId,
                transferredMessageId: ticketTransferMessage.id,
                ticketId: currentTicket.id,
                byUserId,
                comments,
                startedAt: now,
                fromDistribution: transferredByDistribution,
              },
              { ...transactions, dontEmit },
            )

            const account = await accountResource.findById(currentTicket?.accountId, { attributes: ['settings'] })

            if (!transferredByDistribution && byUserId && (account?.settings?.flags || {})['enable-smart-summary']) {
              await queueJobsDispatcher.dispatch<QueueStartSummaryJob>(
                'queued-start-summary',
                { currentTicket, type: 'transfer', contact },
                {
                  hashKey: contact.accountId + '_' + contact.serviceId + '_' + currentTicket?.id,
                },
              )
            }

            const oldUser = await userResource.findById(currentTicket.userId, {
              include: ['account', 'roles', 'departments'],
              ...transactions,
            })

            // Atualiza ticket com novo department, user e ticketTransfer
            const ticket = await ticketResource.update(
              currentTicket,
              {
                departmentId,
                userId,
                currentTicketTransferId: ticketTransfer.id,
              },
              { ...transactions, dontEmit },
            )

            if (oldUser && byUserId && !transferredByDistribution) {
              // Esse evento deve ser disparado para a distribuição atribuir um novo chamado ao usuário que realizou a transferência
              userResource.emit(USER_TRANSFER, oldUser, options.eventTransaction)
            }

            await contactResource.updateById(
              contact.id,
              { unread: 1 },
              { ...transactions, dontEmit: dontEmitContactEvent },
            )

            // Emite eventos de transferência de ticket e alteração de contact
            if (!dontEmit) ticketResource.emit(TRANSFER, ticket, options.eventTransaction)

            return contact
          }, options.transaction),
        options.eventTransaction,
      ),
    )
  }
}
