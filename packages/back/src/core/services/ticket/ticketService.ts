import { pickBy, isNil, negate } from 'lodash'
import { Transaction } from 'sequelize'
import { differenceInSeconds, isAfter, isEqual, subDays } from 'date-fns'
import { MessageInstance } from '../../dbSequelize/models/Message'
import messageResource from '../../resources/messageResource'
import ticketResource from '../../resources/ticketResource'
import serviceResource from '../../resources/serviceResource'
import { ContactInstance } from '../../dbSequelize/models/Contact'
import ticketTransfersResource from '../../resources/ticketTransfersResource'
import { EventTransaction } from '../../resources/BaseResource'
import TicketOpenService from './TicketOpenService'
import TicketTransferService from './TicketTransferService'
import TicketCloseService from './TicketCloseService'
import { decryptTextForAccount } from '../crypt/accountCryptor'
import answersResource from '../../resources/answersResource'
import { Container } from 'typedi'
import Logger from '../logs/Logger'
import SmartSummaryService from '../SmartSummary/SmartSummaryService'

const logger = Container.get(Logger)

const log = logger.log

const getMessageWithRelations = async (id: string, options = {}) => {
  const message = await messageResource.findOne({
    where: { id },
    include: [
      {
        model: 'contact',
        include: [
          {
            model: 'account',
            required: true,
          },
          {
            model: 'service',
            required: true,
          },
        ],
      },
    ],
    // @ts-ignore
    transaction: options.transaction,
  })

  message.contact.currentTicket = await ticketResource.findById(message.contact.currentTicketId, {
    include: [
      'firstMessage',
      {
        model: 'currentTicketTransfer',
        include: ['firstMessage'],
      },
    ],
    // @ts-ignore
    transaction: options.transaction,
  })

  return message
}

const calculatePerMessageMetrics = async (
  metrics: {
    ticketTime?: number
    waitingTime?: number
    messagingTime?: number
    waitingTimeAfterBot?: number
  },
  message: MessageInstance,
  firstMessage: MessageInstance,
  contact: ContactInstance,
  options: {
    dontEmit?: boolean
    eventTransaction?: EventTransaction
    transaction?: Transaction
  } = {},
) => {
  const messagingTime = differenceInSeconds(message.createdAt, firstMessage.createdAt)

  const waitingTime =
    metrics.waitingTime ||
    (!firstMessage.isFromMe && message.isFromMe && !message.isFromBot && message.origin !== 'bot'
      ? differenceInSeconds(message.createdAt, firstMessage.createdAt)
      : undefined)

  const getWaitingTimeAfterBot = async () => {
    if (metrics.waitingTimeAfterBot) return metrics.waitingTimeAfterBot

    const service = message.service ?? (await serviceResource.findById(message.serviceId, options))

    return service?.botId &&
      contact.data.botFinishedAt &&
      message.isFromMe &&
      !message.isFromBot &&
      message.origin !== 'bot' &&
      message.origin !== 'ticket'
      ? differenceInSeconds(message.createdAt, new Date(contact.data.botFinishedAt))
      : null
  }

  const waitingTimeAfterBot = await getWaitingTimeAfterBot()

  return pickBy(
    {
      ...metrics,
      messagingTime,
      waitingTime,
      waitingTimeAfterBot,
    },
    negate(isNil),
  )
}

class TicketService {
  protected ticketOpenService: TicketOpenService

  protected ticketTransferService: TicketTransferService

  protected ticketCloseService: TicketCloseService

  constructor() {
    this.ticketOpenService = new TicketOpenService()
    this.ticketTransferService = new TicketTransferService(this.ticketOpenService)
    this.ticketCloseService = new TicketCloseService()
  }

  handleMessageCreated = async (
    message: MessageInstance,
    options: { transaction?: any; eventTransaction: EventTransaction; dontEmit?: boolean },
    campaignData?: { departmentId: string; userId: string },
  ) => {
    // Or else it causes a loop and block the task queue
    if (message?.type === 'ticket') return

    return messageResource.maybeEventTransaction(async (eventTransaction) => {
      return messageResource.nestedTransaction(async (transaction) => {
        const messageWithRelations = await getMessageWithRelations(message.id, {
          ...options,
          eventTransaction,
          transaction,
        })

        if (!messageWithRelations) {
          throw new Error('messageWithRelations is null. Outside transaction?')
        }

        const { contact } = messageWithRelations
        const { account } = contact

        if (
          contact.data.survey &&
          !message.isFromMe &&
          message.type !== 'ticket' &&
          message.type !== 'reaction' &&
          message.origin !== 'bot'
        ) {
          const shouldOpenTicket: boolean = await answersResource.handleAnswer({
            contact,
            text: decryptTextForAccount(account, message.text),
            options: { ...options, transaction, eventTransaction },
          })

          if (!shouldOpenTicket) return
        }

        return this.handleTicket(
          messageWithRelations,
          {
            ...options,
            transaction,
            eventTransaction,
          },
          campaignData,
        )
      }, options?.transaction)
    }, options?.eventTransaction)
  }

  async summaryTicket(contact: ContactInstance) {
    const smartSummaryService = Container.get(SmartSummaryService)
    if (!(await smartSummaryService.limitReached(contact?.account?.id, false))) {
      await smartSummaryService.start(contact.currentTicket, 'manual', contact)
    }
  }

  async transferTicket(
    data: {
      contact: ContactInstance
      userId?: string
      departmentId: string
      byUserId?: string
      comments?: string
      timestamp?: Date
      transferredByBot?: Boolean
      transferredByDistribution?: Boolean
    },
    options: {
      dontEmit?: boolean
      transaction?: any
      eventTransaction?: EventTransaction
    } = {},
    dontEmitContactEvent = false,
  ) {
    return this.ticketTransferService.transferTicket(data, options, dontEmitContactEvent).catch((err) => {
      log(`Error to transferTicket for contact #${data.contact?.id}: %o`, 'error', [err])
      throw err
    })
  }

  async closeTicket(
    data: {
      contact: ContactInstance
      byUserId?: string
      comments?: string
      ticketTopicIds?: string[] | string
      transferredByBot?: Boolean
    },
    options: {
      dontEmit?: boolean
      transaction?: any
      eventTransaction?: EventTransaction
    } = {},
  ) {
    return this.ticketCloseService.closeTicket(data, options)
  }

  protected async openTicket(
    data: {
      contact: ContactInstance
      message?: MessageInstance
      departmentId: string
      userId: string
      byUserId?: string
      origin: string
      startedAt?: Date
      comments?: string
    },
    options: {
      transaction?: any
      eventTransaction?: EventTransaction
      dontEmit?: boolean
    } = {},
  ) {
    return this.ticketOpenService.openTicket(data, options)
  }

  protected async attachToExisting(
    data: {
      contact: ContactInstance
      message: MessageInstance
    },
    options: {
      transaction: any
      eventTransaction: EventTransaction
      dontEmit?: boolean
    },
  ) {
    const { contact, message } = data
    const { currentTicket } = contact
    const firstTicketMessage = currentTicket.firstMessage || message

    if (!currentTicket.currentTicketTransfer) {
      throw new Error('currentTicket.currentTicketTransfer is required.')
    }
    const currentTicketTransfer = currentTicket.currentTicketTransfer

    // QUERY MTO LONGA, CORTANDO NOME DE ATRIBUTOS
    const firstTicketTransferMessage = (currentTicketTransfer && currentTicketTransfer.firstMessage) || message

    // Atualiza ticketTransfer setando primeira e última mensagem e métricas
    const ticketTransfer = await ticketTransfersResource.update(
      currentTicketTransfer,
      {
        firstMessageId: firstTicketTransferMessage.id,
        lastMessageId: message.id,
      },
      { ...options, dontEmit: true },
    )

    // Atualiza chamado setando primeira e última mensagem e métricas
    const ticket = await ticketResource.update(
      currentTicket,
      {
        firstMessageId: currentTicket.firstMessageId || message.id,
        lastMessageId: message.id,
        metrics: await calculatePerMessageMetrics(currentTicket.metrics, message, firstTicketMessage, contact, options),
      },
      { ...options, dontEmit: true },
    )

    // Atualiza mensagem setando ids do chamado
    const updatedMessage = await messageResource.update(
      message,
      {
        ticketId: contact.currentTicketId,
        ticketUserId: currentTicket.userId,
        ticketDepartmentId: currentTicket.departmentId,
      },
      { ...options, dontEmit: true },
    )

    return {
      ticket,
      ticketTransfer,
      message: updatedMessage,
    }
  }

  // eslint-disable-next-line complexity
  protected handleTicket = async (
    message: MessageInstance,
    options: { transaction: any; eventTransaction: EventTransaction; dontEmit?: boolean },
    campaignData?: { departmentId: string; userId: string },
  ) => {
    const { contact, isFromSync, timestamp } = message

    if (message.type === 'ticket') return

    if (!contact) throw new Error('message.contact is required.')
    const { currentTicket, service, account } = contact

    if (!account) throw new Error('message.contact.account is required.')
    if (!service) throw new Error('message.contact.service is required.')

    const defaultDepartmentId =
      (!account.settings.disableDefaultTicketTransfer && contact.defaultDepartmentId) ||
      service.defaultDepartmentId ||
      account.defaultDepartmentId

    const defaultUserId = !account.settings.disableDefaultTicketTransfer ? contact.defaultUserId : null
    const { ticketsEnabled } = account.settings

    if (!ticketsEnabled) return

    const isOnOpeningWindow = isAfter(timestamp, subDays(new Date(), 7))

    const { isNew, isFromFirstSync, dontOpenTicket } = message.data
    const isBroadCast = !!contact.isBroadcast
    const isAFreshMessage = (!!isNew || (!!isFromSync && !isFromFirstSync)) && isOnOpeningWindow

    // A data da primeira mensagem pode ser antes da data de criação do chamado
    // devido a sincronização de mensagems antigas do WhatsApp
    const startDate =
      currentTicket && (currentTicket.firstMessage ? currentTicket.firstMessage.timestamp : currentTicket.startedAt)

    const isAfterStartDate = startDate && (isAfter(timestamp, startDate) || isEqual(timestamp, startDate))

    const isPartOfATicket = !isFromFirstSync && isAfterStartDate

    const { shouldOpenTicketForGroups = false } = service.settings || {}
    const isGroup = !!contact.isGroup
    const openForGroup = isGroup ? shouldOpenTicketForGroups : true

    const isReaction = message.type === 'reaction'
    const isScheduled = message.origin === 'schedule'

    const shouldOpen =
      !dontOpenTicket &&
      !isReaction &&
      (isAFreshMessage || isScheduled) &&
      !contact.currentTicketId &&
      defaultDepartmentId &&
      !['bot', 'campaign'].includes(message.origin) &&
      !isBroadCast &&
      openForGroup
    if (
      shouldOpen ||
      (['campaign'].includes(message.origin) && campaignData?.departmentId && !contact.currentTicketId)
    ) {
      log(`Opening ticket for contact #${contact.id} (${contact.name}).`)

      const res = await this.openTicket(
        {
          contact,
          departmentId: campaignData?.departmentId || defaultDepartmentId,
          userId: campaignData?.userId || defaultUserId,
          message,
          origin: message.origin === 'widget' ? 'widget' : 'automatic',
        },
        options,
      )

      return {
        opened: true,
        ...res,
      }
    }

    const shouldAttachToExisting = contact.currentTicketId && !message.ticketId && isPartOfATicket

    if (shouldAttachToExisting) {
      const res = await this.attachToExisting({ contact, message }, options)
      return {
        attached: true,
        ...res,
      }
    }
  }
}

export default new TicketService()
