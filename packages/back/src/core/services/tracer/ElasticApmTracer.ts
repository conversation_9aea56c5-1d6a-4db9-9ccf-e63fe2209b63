import apm, { Agent, AgentConfigOptions, Span as ESpan, Transaction as ETransaction } from 'elastic-apm-node'
import { Tracer, Outcome, Span, Transaction } from './Tracer'
import { Service, Container } from 'typedi'
import { IncomingMessage, ServerResponse } from 'http'
import { GenericTracer } from './GenericTracer'

export class ElasticTransaction implements Transaction {
  constructor(protected tracer: ElasticApmTracer, protected transaction?: ETransaction) {}

  setName(name: string) {
    apm.setTransactionName(name)
  }

  setOutcome(outcome: Outcome) {
    this.transaction?.setOutcome(outcome)
  }

  end(outcome?: Outcome): void {
    if (outcome) this.transaction?.setOutcome(outcome)
    this.transaction?.end()
  }

  addLabels(labels: { [p: string]: string }): void {
    this.transaction?.addLabels(labels)
  }

  startSpan(name: string, type: string): Span {
    return this.tracer.startSpan(name, type)
  }
}

export class ElasticSpan implements Span {
  constructor(protected tracer: ElasticApmTracer, protected span?: ESpan) {}

  setOutcome(outcome: Outcome) {
    this.span?.setOutcome(outcome)
  }

  end(outcome?: Outcome): void {
    if (outcome) this.span?.setOutcome(outcome)
    this.span?.end()
  }

  addLabels(labels: { [p: string]: string }): void {
    this.span?.addLabels(labels)
  }

  getTransaction(): Transaction {
    if (!this.span || !this.span.transaction) return Container.get(GenericTracer).startGenericTransaction()
    return new ElasticTransaction(this.tracer, this.span.transaction)
  }
}

@Service()
export class ElasticApmTracer implements Tracer {
  protected apm: Agent

  start(options: AgentConfigOptions) {
    this.apm = apm.start(options)
    return this
  }

  getCurrentTraceIds(): { 'transaction.id'?: string; 'trace.id'?: string; 'span.id'?: string } {
    if (!this.apm || !this.getApm().isStarted()) return {}
    return this.getApm().currentTraceIds
  }

  getCurrentTraceParent(): string {
    return this.apm.currentTraceparent
  }

  startTransaction(name: string, type: string, options: { childOf: string }): Transaction {
    const transaction = this.apm.startTransaction(name, type, options)
    return new ElasticTransaction(this, transaction)
  }

  getCurrentTransaction() {
    return new ElasticTransaction(this, this.apm.currentTransaction)
  }

  startSpan(name: string, type?: string): Span {
    // startSpan can return null!
    const span = this.getApm().startSpan(name, type)
    return new ElasticSpan(this, span)
  }

  setLabel(name: string, value: string | number | boolean | null | undefined): void {
    this.apm.setLabel(name, value)
  }

  setUserContext(user: { id?: string | number; username?: string; email?: string }): void {
    this.apm.setUserContext(user)
  }

  captureError(error: Error, options?: { request?: IncomingMessage; response?: ServerResponse }): void {
    this.apm.captureError(error, options)
  }

  protected getApm() {
    if (!this.apm) throw new Error('APM not yet started.')
    return this.apm
  }
}
