import { Token } from 'typedi'
import type { IncomingMessage, ServerResponse } from 'http'

export type Outcome = 'unknown' | 'success' | 'failure'

export interface Transaction {
  setName(name: string): void
  setOutcome(outcome: Outcome): void
  end(outcome?: Outcome): void
  addLabels(labels: { [name: string]: string }): void
  startSpan(name: string, type: string): Span
}

export interface Span {
  setOutcome(outcome: Outcome): void
  end(outcome?: Outcome): void
  addLabels(labels: { [name: string]: string }): void
  getTransaction(): Transaction
}

export interface Tracer {
  start(options: any): Tracer
  getCurrentTraceIds(): { 'transaction.id'?: string; 'trace.id'?: string; 'span.id'?: string }
  getCurrentTraceParent(): string
  startTransaction(name: string, type: string, options?: { childOf?: string }): Transaction
  getCurrentTransaction(): Transaction
  startSpan(name: string, type?: string): Span
  setUserContext(user: { id?: string | number; username?: string; email?: string }): void
  setLabel(name: string, value: string | number | boolean | null | undefined): void
  captureError(error: Error, options?: { request?: IncomingMessage; response?: ServerResponse }): void
}

export const TracerToken = new Token<Tracer>('TracerToken')
