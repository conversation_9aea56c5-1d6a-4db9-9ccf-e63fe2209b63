import { Service } from 'typedi'
import { Readable } from 'stream'
import { v4 as uuid } from 'uuid'
import WpLvS3Storage from '../storage/WpLvS3Storage'
import Storage from '../storage'
import base64UrlToBuffer from '../../utils/base64/base64UrlToBuffer'

@Service()
export default class WpLvStorage {
  protected storage = <WpLvS3Storage>Storage.getDriver('wpLvS3')

  async writeUploadedFile({
    accountId,
    filename,
    data,
    mimetype,
  }: {
    accountId: string
    filename: string
    data: Readable | Buffer
    mimetype: string
  }) {
    const now = new Date()
    const id = uuid()
    const filePath = `uploads/${now.getUTCFullYear()}/${now.getUTCMonth()}/${accountId}/${id}-${filename}`

    await this.storage.write(filePath, data, {
      ContentType: mimetype,
    })

    return this.storage.getS3().getSignedUrlPromise('getObject', {
      Key: filePath,
      Expires: 60 * 60 * 24 * 7, // 7 days
    })
  }

  async makeSignedUrlForDownloadMedia({
    accountId,
    serviceId,
    filename,
    mimetype,
  }: {
    accountId: string
    serviceId: string
    filename: string
    mimetype: string
  }) {
    const now = new Date()
    const id = uuid()
    const filePath = `downloads/${now.getUTCFullYear()}/${now.getUTCMonth()}/${accountId}/${serviceId}/${id}-${filename}`

    const signedRequest = await this.storage.getS3().getSignedUrlPromise('putObject', {
      Key: filePath,
      ContentType: mimetype,
      Expires: 60 * 60,
    })

    const signedUrl = await this.storage.getS3().getSignedUrlPromise('getObject', {
      Key: filePath,
      Expires: 60 * 60 * 24 * 7, // 7 days
    })

    return { signedRequest, signedUrl }
  }

  async uploadPreviewUrl({
    accountId,
    serviceId,
    base64Url,
  }: {
    accountId: string
    serviceId: string
    base64Url: string
  }) {
    const now = new Date()
    const id = uuid()
    const filePath = `downloads/${now.getUTCFullYear()}/${now.getUTCMonth()}/${accountId}/${serviceId}/${id}-preview.jpeg`

    const buffer = base64UrlToBuffer(base64Url)

    await this.storage.write(filePath, buffer)

    const signedUrl = await this.storage.getS3().getSignedUrlPromise('getObject', {
      Key: filePath,
      Expires: 60 * 60 * 24 * 7, // 7 days
    })

    return { signedUrl }
  }
}
