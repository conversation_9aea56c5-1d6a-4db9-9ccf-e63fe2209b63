import pick from 'lodash/pick'
import contactTransformer from './contactTransformer'
import ticketResource from '../resources/ticketResource'
import { decryptTextForAccount } from '../services/crypt/accountCryptor'

export default async (model, context) => {
  if (!model) return model

  const getAccount = async () => {
    if (!model?.account) {
      const ticket = await ticketResource.findById(model?.id, { include: ['account'] })
      return ticket?.account
    }
    return model?.account
  }

  return {
    ...pick(model, [
      'id',
      'isOpen',
      'comments',
      'protocol',
      'origin',
      'accountId',
      'departmentId',
      'contactId',
      'userId',
      'firstMessageId',
      'lastMessageId',
      'currentTicketTransferId',
      'startedAt',
      'endedAt',
      'metrics',
      'account',
      'user',
      'department',
      'messages',
      'firstMessage',
      'lastMessage',
      'ticketTransfers',
      'ticketTopics',
      'currentTicketTransfer',
      'createdAt',
      'updatedAt',
    ]),
    summary: await decryptTextForAccount(await getAccount(), model?.summary),
    contact: await contactTransformer(model.contact, context),
  }
}
