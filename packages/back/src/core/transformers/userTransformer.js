import pick from 'lodash/pick'
import { transformMany } from '../utils/resource/transformerHelpers'
import permissionTransformer from './permissionTransformer'
import accountTransformer from './accountTransformer'
import defaultTransformer from './defaultTransformer'
import organizationTransformer from './organizationTransformer'
import roleTransformer from './roleTransformer'
import generateSecret from './../../core/utils/otp/generateSecret'
import getURIFromSecret from './../../core/utils/otp/getURIFromSecret'
import config from './../../core/config'

const getTwoFactorAttributes = (user) => {
  if (user.otpAuthActive) return {}
  const secret = generateSecret()
  const issuer = config('domain')
  return {
    otpSecretKey: secret,
    otpSecretURI: getURIFromSecret(user.email, issuer, secret),
  }
}

export default async (model, context) => {
  if (!model) return model
  const { byPassTwoFactor = false } = context || {}

  return {
    timetable: null,
    ...pick(model, [
      'id',
      'name',
      'email',
      'phoneNumber',
      'branch',
      'isSuperAdmin',
      'isClientUser',
      'createdAt',
      'updatedAt',
      'deletedAt',
      'accountId',
      'archivedAt',
      'roles',
      'data',
      'departments',
      'isFirstLogin',
      'timetableId',
      'timetable',
      'status',
      'clientsStatus',
      'language',
      'isActiveInternalChat',
      'internalChatToken',
      'otpAuthActive',
      'passwordExpiresAt',
      'preferences',
    ]),
    ...((byPassTwoFactor && getTwoFactorAttributes(model)) || {}),
    tickets: await transformMany(defaultTransformer, context)(model.tickets),
    roles: await transformMany(roleTransformer, context)(model.roles),
    account: await accountTransformer(model.account),
    permissions: await transformMany(permissionTransformer, context)(model.permissions),
    organizations: await transformMany(organizationTransformer, context)(model.organizations),
  }
}
